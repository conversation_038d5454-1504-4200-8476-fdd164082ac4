name: CI

on:
  push:
    branches:
      - main
  pull_request:
  workflow_dispatch:

env:
  NODE_VERSION: 20
  DATABASE_URL: mongodb://localhost:27017/hopfun?authSource=admin&replicaSet=rs0
  NX_CLOUD_DISTRIBUTED_EXECUTION: false
  NX_SKIP_NX_CACHE: false
  NX_PARALLEL: 3

jobs:
  setup:
    name: Setup & Build Dependencies
    runs-on: ubuntu-latest
    outputs:
      node-modules-cache-hit: ${{ steps.cache-node-modules.outputs.cache-hit }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: ${{ github.event_name == 'pull_request' && '0' || '1' }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.13.1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Cache node_modules
        id: cache-node-modules
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            apps/*/node_modules
            packages/*/node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-node-modules-

      - name: Cache Nx
        uses: actions/cache@v4
        with:
          path: .nx/cache
          key: ${{ runner.os }}-nx-${{ github.ref }}-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-nx-${{ github.ref }}-
            ${{ runner.os }}-nx-

      - name: Install dependencies
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        run: pnpm install --frozen-lockfile

      - name: Build database package
        run: pnpm --filter @hopfun/database build

      - name: Upload database build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: database-build
          path: packages/database/dist
          retention-days: 1

      - name: Upload Prisma client
        uses: actions/upload-artifact@v4
        with:
          name: prisma-client
          path: packages/database/src/generated
          retention-days: 1

  validate:
    name: Validate Code
    needs: setup
    runs-on: ubuntu-latest
    strategy:
      matrix:
        command:
          - lint
          - format:check
          - type-check
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: ${{ github.event_name == 'pull_request' && '0' || '1' }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.13.1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Restore node_modules cache
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            apps/*/node_modules
            packages/*/node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-node-modules-

      - name: Restore Nx cache
        uses: actions/cache@v4
        with:
          path: .nx/cache
          key: ${{ runner.os }}-nx-${{ github.ref }}-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-nx-${{ github.ref }}-
            ${{ runner.os }}-nx-

      - name: Install dependencies if cache miss
        if: needs.setup.outputs.node-modules-cache-hit != 'true'
        run: pnpm install --frozen-lockfile

      - name: Download database build
        uses: actions/download-artifact@v4
        with:
          name: database-build
          path: packages/database/dist

      - name: Download Prisma client
        uses: actions/download-artifact@v4
        with:
          name: prisma-client
          path: packages/database/src/generated

      - name: Run ${{ matrix.command }}
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            pnpm nx affected --target=${{ matrix.command }} --base=origin/${{ github.base_ref }} --head=HEAD --parallel=3
          else
            pnpm ${{ matrix.command }}
          fi

  build-and-test:
    name: Build & Test
    needs: setup
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: ${{ github.event_name == 'pull_request' && '0' || '1' }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10.13.1

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'pnpm'

      - name: Restore node_modules cache
        uses: actions/cache@v4
        with:
          path: |
            node_modules
            apps/*/node_modules
            packages/*/node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-node-modules-

      - name: Restore Nx cache
        uses: actions/cache@v4
        with:
          path: .nx/cache
          key: ${{ runner.os }}-nx-${{ github.ref }}-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-nx-${{ github.ref }}-
            ${{ runner.os }}-nx-

      - name: Install dependencies if cache miss
        if: needs.setup.outputs.node-modules-cache-hit != 'true'
        run: pnpm install --frozen-lockfile

      - name: Download database build
        uses: actions/download-artifact@v4
        with:
          name: database-build
          path: packages/database/dist

      - name: Download Prisma client
        uses: actions/download-artifact@v4
        with:
          name: prisma-client
          path: packages/database/src/generated

      - name: Run build
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            pnpm nx affected --target=build --base=origin/${{ github.base_ref }} --head=HEAD --parallel=3
          else
            pnpm build
          fi

      - name: Run tests
        run: |
          if [ "${{ github.event_name }}" == "pull_request" ]; then
            pnpm nx affected --target=test --base=origin/${{ github.base_ref }} --head=HEAD --parallel=3 --passWithNoTests
          else
            pnpm test
          fi

      - name: Upload build artifacts
        if: success()
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            apps/*/dist
            packages/*/dist
          retention-days: 7

  ci-status:
    name: CI Status
    needs: [validate, build-and-test]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Check CI Status
        run: |
          if [ "${{ needs.validate.result }}" != "success" ] || \
             [ "${{ needs.build-and-test.result }}" != "success" ]; then
            echo "❌ CI failed"
            echo "Validation: ${{ needs.validate.result }}"
            echo "Build & Test: ${{ needs.build-and-test.result }}"
            exit 1
          fi
          echo "✅ CI passed successfully"
          echo "All checks completed successfully!"