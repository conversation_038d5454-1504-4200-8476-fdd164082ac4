# Config Registry Solution - Complete Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Deployment Guide](#deployment-guide)
4. [Usage Guide](#usage-guide)
5. [Migration from Hardcoded Approach](#migration-guide)
6. [Troubleshooting](#troubleshooting)
7. [API Reference](#api-reference)

## Overview

The Config Registry pattern solves the circular dependency problem in Sui Move smart contracts where:
- `connector.move` needs the MemeConfig address at compile time
- MemeConfig is only created after hopfun is deployed
- Previously required redeployment every time addresses changed

### Benefits
✅ **No more circular dependencies** - Registry acts as a dynamic lookup  
✅ **Deploy once, use forever** - No redeployment needed  
✅ **Single source of truth** - All configuration in one place  
✅ **Easy updates** - Change addresses without touching contracts  
✅ **Better gas efficiency** - Fewer deployments = less gas spent  

## Architecture

### Component Overview

```
┌─────────────────────────────────────────────────────────┐
│                    Config Registry                       │
│  (Shared Object - Stores all configuration addresses)    │
└────────────────┬────────────────────────────────────────┘
                 │
    ┌────────────┼────────────┬──────────────┐
    ▼            ▼            ▼              ▼
┌────────┐  ┌────────┐  ┌──────────┐  ┌──────────┐
│ HopFun │  │ HopDex │  │   Coin   │  │ Frontend │
│Package │  │Package │  │ Template │  │   App    │
└────────┘  └────────┘  └──────────┘  └──────────┘
```

### Key Components

1. **Config Registry Package** (`contracts/config_registry/`)
   - Shared object storing all addresses
   - Admin-controlled updates
   - Event emission for tracking changes

2. **Modified Connector** (`hopfun::connector_registry`)
   - Reads MemeConfig address from registry
   - No hardcoded addresses
   - Backward compatible

3. **Registry-Based Coin Template** (`contracts/coin_template_registry/`)
   - Uses registry for dynamic address resolution
   - Two-step creation process

4. **Frontend Integration**
   - Passes registry ID in transactions
   - Supports both registry and non-registry modes

## Deployment Guide

### Prerequisites
- Sui CLI installed and configured
- Active Sui wallet with gas tokens
- Node.js for running scripts

### Step 1: Deploy Config Registry

```bash
# Deploy the registry package
sui client publish contracts/config_registry --gas-budget 100000000

# Save these IDs:
# - Registry Package ID: 0x123...
# - Registry ID (ConfigRegistry object): 0x456...
# - Admin Cap ID: 0x789...
```

### Step 2: Deploy HopFun and HopDex

```bash
# Deploy HopFun (set address to 0x0 first)
sed -i '' 's/hopfun = "0x[a-f0-9]*"/hopfun = "0x0"/' contracts/hopfun/Move.toml
sui client publish contracts/hopfun --gas-budget 200000000

# Deploy HopDex (if needed)
sed -i '' 's/hopdex = "0x[a-f0-9]*"/hopdex = "0x0"/' contracts/hopdex/Move.toml
sui client publish contracts/hopdex --gas-budget 200000000
```

### Step 3: Update Registry with Addresses

```bash
# Update all addresses at once
sui client call \
  --package $REGISTRY_PACKAGE_ID \
  --module registry \
  --function update_all_addresses \
  --args $REGISTRY_ID $ADMIN_CAP_ID $MEME_CONFIG_ID $DEX_CONFIG_ID $HOPFUN_PACKAGE_ID $HOPDEX_PACKAGE_ID \
  --gas-budget 10000000
```

### Step 4: Automated Deployment

Use the provided script for complete deployment:

```bash
# Make script executable
chmod +x scripts/deploy-with-registry.sh

# Run deployment
./scripts/deploy-with-registry.sh
```

## Usage Guide

### For Smart Contract Developers

#### Using Registry in Your Coin Module

```move
module my_coin::my_token {
    use hopfun::connector_registry;
    use config_registry::registry::ConfigRegistry;
    
    public struct MY_TOKEN has drop {}
    
    // Store witness for later use
    public struct WitnessHolder has key {
        id: UID,
        witness: MY_TOKEN
    }
    
    fun init(witness: MY_TOKEN, ctx: &mut TxContext) {
        let holder = WitnessHolder { 
            id: object::new(ctx),
            witness 
        };
        transfer::transfer(holder, tx_context::sender(ctx));
    }
    
    // Entry function to create connector with registry
    public entry fun create_connector(
        holder: WitnessHolder,
        registry: &ConfigRegistry,
        ctx: &mut TxContext
    ) {
        let WitnessHolder { id, witness } = holder;
        object::delete(id);
        
        connector_registry::new_with_registry<MY_TOKEN>(
            witness,
            123, // temp_id
            b"MyToken",
            b"MTK",
            b"Description",
            b"icon_url",
            string::utf8(b"twitter"),
            string::utf8(b"website"),
            string::utf8(b"telegram"),
            1000000000 * 1000000, // total supply
            registry,
            ctx
        );
    }
}
```

### For Frontend Developers

#### Token Creation with Registry

```typescript
// In token-creation.service.ts
import { Transaction } from '@mysten/sui/transactions';

async function createTokenWithRegistry(
  payload: TokenCreationPayload,
  registryId: string
) {
  // Step 1: Publish coin module
  const publishTx = new Transaction();
  publishTx.publish({
    modules: [bytecodeRegistryData.bytecode],
    dependencies: bytecodeRegistryData.dependencies,
  });
  
  const publishResult = await signAndExecuteTransaction({
    transaction: publishTx
  });
  
  // Extract witness holder ID from result
  const witnessHolderId = extractWitnessHolderId(publishResult);
  
  // Step 2: Create connector using registry
  const createTx = new Transaction();
  createTx.moveCall({
    target: `${packageId}::template::create_connector_with_registry`,
    arguments: [
      createTx.object(witnessHolderId),
      createTx.object(registryId), // Pass registry as shared object
    ]
  });
  
  const createResult = await signAndExecuteTransaction({
    transaction: createTx
  });
  
  // Step 3: Create bonding curve (same as before)
  // ...
}
```

#### Configuration in Frontend

```typescript
// network-config.service.ts
interface ContractConfig {
  // ... existing fields
  registryId?: string;
  registryPackageId?: string;
  useRegistry?: boolean;
}

// In your .env.local
NEXT_PUBLIC_USE_REGISTRY=true
NEXT_PUBLIC_REGISTRY_ID_DEVNET=0x456...
NEXT_PUBLIC_REGISTRY_PACKAGE_ID_DEVNET=0x123...
```

## Migration Guide

### From Hardcoded to Registry Approach

#### Step 1: Deploy Registry Infrastructure
```bash
./scripts/deploy-with-registry.sh
```

#### Step 2: Update Frontend Configuration
```typescript
// Add to network-config.service.ts
registryId: process.env.NEXT_PUBLIC_REGISTRY_ID_DEVNET,
registryPackageId: process.env.NEXT_PUBLIC_REGISTRY_PACKAGE_ID_DEVNET,
useRegistry: true,
```

#### Step 3: Switch Token Creation Flow
```typescript
// Check if registry is available
const config = networkConfigService.getNetworkConfig();
if (config.contracts.useRegistry && config.contracts.registryId) {
  // Use registry-based flow
  await createTokenWithRegistry(payload, config.contracts.registryId);
} else {
  // Fall back to hardcoded approach
  await createTokenLegacy(payload);
}
```

#### Step 4: Update Bytecode Templates
```bash
# Build and extract registry-based template
sui move build --path contracts/coin_template_registry
node scripts/extract-bytecode-registry.js
```

### Backward Compatibility

The system maintains backward compatibility:
- Old tokens continue to work
- Frontend can support both approaches
- Gradual migration possible

## Troubleshooting

### Common Issues and Solutions

#### Issue: "Registry not initialized"
**Error**: `ERegistryNotInitialized` when creating token  
**Solution**: Update registry with addresses first
```bash
sui client call --package $REGISTRY_PKG --module registry \
  --function set_meme_config_address --args $REGISTRY_ID $ADMIN_CAP $MEME_CONFIG_ID
```

#### Issue: "Not admin"
**Error**: `ENotAdmin` when updating registry  
**Solution**: Use correct AdminCap ID
```bash
# Check AdminCap owner
sui client object $ADMIN_CAP_ID
```

#### Issue: "Invalid address"
**Error**: `EInvalidAddress` when setting address  
**Solution**: Don't use 0x0 as an address
```bash
# Use actual deployed address
--args $REGISTRY_ID $ADMIN_CAP "0xactual_address_here"
```

#### Issue: Build fails with missing dependency
**Error**: Cannot find module `config_registry`  
**Solution**: Add to Move.toml dependencies
```toml
[dependencies]
config_registry = { local = "../config_registry" }
```

## API Reference

### Config Registry Functions

#### Read Functions
```move
// Get MemeConfig address
public fun get_meme_config_address(registry: &ConfigRegistry): address

// Get DexConfig address  
public fun get_dex_config_address(registry: &ConfigRegistry): address

// Get all configuration
public fun get_all_config(registry: &ConfigRegistry): (address, address, address, address)

// Check if initialized
public fun is_meme_config_initialized(registry: &ConfigRegistry): bool
```

#### Admin Functions
```move
// Update MemeConfig address
public entry fun set_meme_config_address(
    registry: &mut ConfigRegistry,
    cap: &AdminCap,
    new_address: address,
    ctx: &mut TxContext
)

// Batch update all addresses
public entry fun update_all_addresses(
    registry: &mut ConfigRegistry,
    cap: &AdminCap,
    meme_config: address,
    dex_config: address,
    hopfun_package: address,
    hopdex_package: address,
    ctx: &mut TxContext
)

// Transfer admin control
public entry fun transfer_admin(
    registry: &mut ConfigRegistry,
    cap: AdminCap,
    new_admin: address,
    ctx: &mut TxContext
)
```

### Connector Registry Functions

```move
// Create connector with registry
public fun new_with_registry<T: drop>(
    witness: T,
    temp_id: u64,
    name: vector<u8>,
    symbol: vector<u8>,
    description: vector<u8>,
    icon_url: vector<u8>,
    twitter: string::String,
    website: string::String,
    telegram: string::String,
    total_supply: u64,
    registry: &ConfigRegistry,
    ctx: &mut TxContext
)
```

## Best Practices

### For Contract Development
1. Always check registry initialization before use
2. Emit events for important changes
3. Use batch update functions when possible
4. Keep registry version for future upgrades

### For Frontend Development
1. Cache registry ID in environment variables
2. Implement fallback for non-registry mode
3. Check registry availability before use
4. Handle registry update events

### For Operations
1. Keep AdminCap secure (cold storage)
2. Document all address updates
3. Test on devnet before mainnet
4. Monitor registry events

## Future Enhancements

### Planned Features
- Multi-sig admin control
- Automatic registry discovery
- Cross-chain registry sync
- Registry state snapshots

### Upgrade Path
The registry includes a version field for future upgrades:
```move
public fun get_version(registry: &ConfigRegistry): u64
```

## Support

For issues or questions:
1. Check this documentation
2. Review the example implementations
3. Open an issue on GitHub
4. Contact the development team

## Conclusion

The Config Registry pattern provides a robust, scalable solution to the circular dependency problem in Sui Move contracts. By centralizing configuration and enabling dynamic address resolution, it significantly improves the development and deployment experience while maintaining security and efficiency.