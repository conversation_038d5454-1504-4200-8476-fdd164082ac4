{"header": {"menu": {"advanced": "Advanced", "connect-wallet": "Connect Wallet", "menu": "<PERSON><PERSON>", "wallet": {"disconnect": "Disconnect", "not-installed": "Wallet not installed", "not-installed-description": "Please install a wallet to connect.", "address-copied": "Address copied!", "copy-address": "Copy address"}}, "languages": {"en": "English", "de": "German"}}, "home": {"sections": {"featured-coins": {"title": "Featured Coins", "description": "The hottest tokens everyone's watching right now", "card": {"market-cap": "Market Cap"}, "filters": {"search-placeholder": "Search featured coins", "last-trade": "Last Trade", "creation-time": "Creation Time", "heading-up": "Heading Up", "watchlist": "Watchlist", "all-tokens": "All Tokens"}}}}, "token-detail": {"header-section": {"created-by": "Created by", "contract-address": "Contract Address", "market-cap": "Market Cap", "time-created": "Time Created"}, "activity-table": {"title": "Activity", "date": "Date", "type": "Type", "price": "Price", "mini": "Mini", "account": "Account"}, "buy-sell-form": {"buy": "Buy", "sell": "<PERSON>ll", "half": "Half", "max": "Max", "slippage": "Slippage", "amount": "Amount"}, "coin-info": {"about-coin": "About Coin", "supply": "Supply", "created": "Created", "trade-fees": "Trade Fees", "contract-address": "Contract Address", "developer-address": "Developer Address"}, "comment-list": {"add-reply": "Add a reply"}, "market-cap-progress": {"market-cap-progress": "Market Cap Progress", "current": "Current", "target": "Target"}, "top-holders-table": {"title": "Top Holders", "holder": "Holder", "percentage": "Percentage (%)"}, "tabs-section": {"replies": "Replies", "coin-info": "Coin Info", "activity": "Activity", "top-holders": "Top Holders"}}, "advanced": {"advanced-section": {"newly-created": "Newly Created", "graduating": "Graduating", "graduated": "Graduated", "watchlist": "Watchlist"}, "advanced-card": {"progress": "Progress"}}, "footer": {"menu": {"request-api": "Request an API", "verify-token": "Verify <PERSON>", "docs": "Docs"}}, "theme": {"dark": "Dark", "light": "Light"}, "components": {"launch-token": {"label": "Launch Token"}, "address": {"copy-address": "Copy address", "copied": "Copied!", "view-on-explorer": "View on explorer"}, "image-upload": {"upload-error": "Error uploading image: {message}", "unknown-error": "An unknown error occurred", "uploading-picture": {"title": "Uploading Picture", "description": "Do not refresh or perform any other action while it is being uploaded"}, "form": {"title": "Image Upload", "description": "Max file size: 5MB. Supported formats: JPEG, PNG, GIF, WebP"}, "image-uploaded": {"title": "Image Uploaded", "description": "Click here to upload another image"}}}, "dialogs": {"launch-token": {"title": "Create a Token", "description": "Create and launch your own token on the blockchain", "form": {"optional": "Optional", "hide-options": "Hide options", "show-more-options": "Show more options", "next": "Next", "cancel": "Cancel", "image": {"validation": {"required": "Image is required", "file-size": "File size must be less than 5MB", "file-type": "Only JPEG, PNG, GIF, and WebP files are allowed", "url": "Image URL must be a valid URL"}}, "name": {"label": "Token Name", "placeholder": "Enter token name", "validation": {"required": "Token name is required", "max": "Token name must be 32 characters or less"}}, "symbol": {"label": "Token Symbol", "validation": {"required": "Token symbol is required", "max": "Token symbol must be 10 characters or less"}}, "decimals": {"label": "Decimals", "validation": {"min": "Decimals must be a non-negative integer", "max": "Decimals must be 18 or less"}}, "custom-token-supply": {"validation": {"min": "Custom supply must be at least 10 million", "positive": "Custom supply must be positive", "required": "Custom token supply is required and must be at least 10 million"}}, "platform": {"label": "Platform"}, "description": {"label": "Description", "placeholders": {"1": "The future of <PERSON><PERSON><PERSON> is here 🚀", "2": "Revolutionary token for the next generation", "3": "Bringing innovation to the blockchain space", "4": "Community-driven token with real utility", "5": "The memecoin that's going to the moon 🌙", "6": "Powered by the community, for the community", "7": "Next-gen token with unlimited potential", "8": "Join the revolution and hodl strong 💎", "9": "Building the future, one block at a time", "10": "Tokenizing dreams into reality", "11": "The ultimate store of value in Web3", "12": "Decentralized, deflationary, and degen-approved"}}, "website": {"label": "Website", "validation": {"url": "Please enter a valid URL"}}, "twitter": {"label": "Twitter", "validation": {"url": "Please enter a valid URL"}}, "telegram": {"label": "Telegram", "validation": {"url": "Please enter a valid URL"}}, "supply-token": {"label": "Total Supply Token", "custom": {"name": "Custom", "label": "Custom Supply Token"}}}}}}