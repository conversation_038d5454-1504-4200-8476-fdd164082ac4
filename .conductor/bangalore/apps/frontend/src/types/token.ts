export interface Token {
  id: string;
  address: string;
  name: string;
  description: string;
  symbol: string;
  emoji?: string;
  imageUrl: string;
  price: number;
  marketCap: number;
  tradeFee: number; // in percentage
  volume24h: number;
  change24h: number;
  circulatingSupply: number;
  totalSupply: number;
  maxSupply?: number; // Optional, as not all coins have a max supply
  createdAt: string; // ISO date string
}

export interface TokenComment {
  id: string;
  author: string;
  content: string;
  createdAt: string; // ISO date string
  avatarUrl: string;
}

export interface TokenActivity {
  id: string;
  type: 'buy' | 'sell';
  price: string; // Price at which the activity occurred
  mini: string;
  account: string;
  createdAt: string; // ISO date string
}

export interface TokenHolder {
  address: string;
  balance: number;
  percentage: number; // Percentage of total supply held by this address
}
