import { ChartSection } from '@/features/token-detail/sections/chart-section';
import { FormSection } from '@/features/token-detail/sections/form-section';
import { HeaderSection } from '@/features/token-detail/sections/header-section';
import { TabsSection } from '@/features/token-detail/sections/tabs-section';

const TokenDetailPage = async ({
  params,
}: {
  params: Promise<{ address: string }>;
}) => {
  const { address } = await params;

  return (
    <div className="flex flex-col gap-5 px-4 py-10 md:px-0 md:py-12">
      <HeaderSection address={address} />
      <div className="grid grid-cols-1 gap-5 lg:grid-cols-3">
        <div className="flex flex-col gap-5 lg:col-span-2">
          <ChartSection address={address} />
          <TabsSection address={address} />
        </div>
        <div>
          <FormSection address={address} />
        </div>
      </div>
    </div>
  );
};

export default TokenDetailPage;
