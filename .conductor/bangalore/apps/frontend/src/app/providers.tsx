import { NextIntlClientProvider } from 'next-intl';
import { ThemeProvider } from 'next-themes';
import type { ReactNode } from 'react';

import { SuiWalletProvider } from '@/providers/wallet-provider';

export const Providers = ({ children }: { children: ReactNode }) => {
  // TODO: For now we force light mode until we implement a theme switcher
  return (
    <ThemeProvider forcedTheme="light">
      <NextIntlClientProvider>
        <SuiWalletProvider>{children}</SuiWalletProvider>
      </NextIntlClientProvider>
    </ThemeProvider>
  );
};
