import '@/styles/globals.css';

import type { Metadata, Viewport } from 'next';
import { getLocale } from 'next-intl/server';
import type { CSSProperties, ReactNode } from 'react';

import { Providers } from '@/app/providers';
import { MainLayout } from '@/components/layout/main-layout';
import { Toaster } from '@/components/ui/sonner';
import { APP_CONFIG } from '@/config/app.config';
import { dmSans, inter } from '@/fonts';

export const viewport: Viewport = {
  themeColor: APP_CONFIG.THEME_COLOR,
};

export const metadata: Metadata = {
  title: APP_CONFIG.TITLE,
  description: APP_CONFIG.DESCRIPTION,
  metadataBase: new URL(APP_CONFIG.URL),
  openGraph: {
    title: APP_CONFIG.TITLE,
    description: APP_CONFIG.DESCRIPTION,
    url: APP_CONFIG.URL,
    siteName: APP_CONFIG.TITLE,
  },
  twitter: {
    card: 'summary_large_image',
    site: APP_CONFIG.TWITTER_USERNAME,
    creator: APP_CONFIG.TWITTER_USERNAME,
  },
};

const RootLayout = async ({
  children,
}: Readonly<{
  children: ReactNode;
}>) => {
  const locale = await getLocale();

  return (
    <html
      suppressHydrationWarning
      lang={locale}
      style={
        {
          '--font-inter': inter.style.fontFamily,
          '--font-dm-sans': dmSans.style.fontFamily,
        } as CSSProperties
      }
    >
      <body>
        <Providers>
          <MainLayout showBackground={false}>{children}</MainLayout>
          <Toaster position="top-right" />
        </Providers>
        <Toaster />
      </body>
    </html>
  );
};

export default RootLayout;
