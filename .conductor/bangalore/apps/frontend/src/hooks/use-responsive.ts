'use client';

import { useEffect, useState } from 'react';

type BreakpointType = 'mobile' | 'tablet' | 'small-desktop' | 'large-desktop';

interface UseResponsiveReturn {
  breakpoint: BreakpointType;
  isMobile: boolean;
  isTablet: boolean;
  isSmallDesktop: boolean;
  isLargeDesktop: boolean;
  isMobileOrTablet: boolean;
  isDesktop: boolean;
}

const getBreakpoint = (width: number): BreakpointType => {
  if (width < 768) return 'mobile';
  if (width < 1024) return 'tablet';
  if (width < 1440) return 'small-desktop';
  return 'large-desktop';
};

export const useResponsive = (): UseResponsiveReturn => {
  const [breakpoint, setBreakpoint] = useState<BreakpointType>('large-desktop');

  useEffect(() => {
    const handleResize = () => {
      setBreakpoint(getBreakpoint(window.innerWidth));
    };

    // Set initial breakpoint
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return {
    breakpoint,
    isMobile: breakpoint === 'mobile',
    isTablet: breakpoint === 'tablet',
    isSmallDesktop: breakpoint === 'small-desktop',
    isLargeDesktop: breakpoint === 'large-desktop',
    isMobileOrTablet: breakpoint === 'mobile' || breakpoint === 'tablet',
    isDesktop: breakpoint === 'small-desktop' || breakpoint === 'large-desktop',
  };
};
