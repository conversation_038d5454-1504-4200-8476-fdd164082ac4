import { SuiClient } from '@mysten/sui/client';
import { Transaction } from '@mysten/sui/transactions';

import { BytecodeTemplateService } from './bytecode-template.service';
import { networkConfigService } from './network-config.service';

// Error types for better error handling
export class TokenCreationError extends Error {
  constructor(
    message: string,
    public code:
      | 'WALLET_NOT_CONNECTED'
      | 'INVALID_NETWORK'
      | 'BYTECODE_ERROR'
      | 'TRANSACTION_FAILED'
      | 'VALIDATION_ERROR'
      | 'NETWORK_ERROR'
      | 'TRANSACTION_NOT_FOUND',
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public details?: any,
  ) {
    super(message);
    this.name = 'TokenCreationError';
  }
}

export interface TokenCreationPayload {
  name: string;
  symbol: string;
  description?: string;
  imageUrl?: string;
  twitter?: string;
  website?: string;
  telegram?: string;
  totalSupply: number;
  decimals: number;
}

export interface TokenCreationResult {
  success: boolean;
  packageId?: string;
  digest?: string;
  error?: TokenCreationError;
}

export interface BondingCurveCreationResult {
  success: boolean;
  bondingCurveId?: string;
  digest?: string;
  error?: TokenCreationError;
}

export class TokenCreationService {
  private static suiClient: SuiClient | null = null;

  /**
   * Initialize the SUI client with the current network configuration
   */
  static initializeClient(): SuiClient {
    const config = networkConfigService.getNetworkConfig();
    TokenCreationService.suiClient ??= new SuiClient({ url: config.rpcUrl });
    return TokenCreationService.suiClient;
  }

  /**
   * Create a transaction to publish a new coin module
   */
  static createPublishCoinTransaction(
    payload: TokenCreationPayload,
    sender: string,
  ): Transaction {
    const tx = new Transaction();
    const _config = networkConfigService.getNetworkConfig();

    // Validate the payload first
    const validation = this.validatePayload(payload);
    if (!validation.valid) {
      throw new TokenCreationError(
        `Validation failed: ${validation.errors.join(', ')}`,
        'VALIDATION_ERROR',
        validation.errors,
      );
    }

    // Get the base64 bytecode directly
    // The SDK expects modules as base64 strings
    const bytecodeBase64 = BytecodeTemplateService.getBase64Bytecode();

    // Get the exact dependencies the bytecode was compiled with
    const dependencies = BytecodeTemplateService.getDependencies();

    // Debug: Log the bytecode info
    console.log('Publishing bytecode:', {
      bytecodeBase64Length: bytecodeBase64.length,
      bytecodeBase64First100: bytecodeBase64.substring(0, 100),
      dependencies: dependencies,
    });

    // Publish the coin module with the exact dependencies it was compiled with
    // This ensures compatibility with the on-chain packages
    const [upgradeCap] = tx.publish({
      modules: [bytecodeBase64], // SDK expects base64 strings
      dependencies: dependencies, // Use the exact dependencies from the bytecode
    });

    // Transfer the upgrade capability to the sender
    tx.transferObjects([upgradeCap], tx.pure.address(sender));

    // Set the sender
    tx.setSender(sender);

    return tx;
  }

  /**
   * Create a transaction to create the bonding curve for a published coin
   * This is actually a two-step process:
   * 1. place_dev_order to pay for the bonding curve creation
   * 2. accept_connector to create the bonding curve with the Connector from the published coin
   */
  // eslint-disable-next-line max-params
  static createBondingCurveTransaction(
    coinPackageId: string,
    coinSymbol: string,
    sender: string,
    connectorId: string,
    coinMetadataId: string,
    tempId = 123, // This should match the TEMP_ID in the coin template
    buyAmount = 0, // Initial buy amount in SUI (0 means no initial buy)
  ): Transaction {
    const tx = new Transaction();
    const config = networkConfigService.getNetworkConfig();

    // Generate the module name that was used during publication
    // The module name is fixed as "template" in our bytecode
    const moduleName = 'template';
    // The struct name is fixed as "TEMPLATE" in our bytecode
    const structName = 'TEMPLATE';

    // Create the correct coin type identifier
    const coinType = `${coinPackageId}::${moduleName}::${structName}`;

    // Step 1: Place dev order (pay for bonding curve creation)
    // This creates a DevOrder that will be consumed by accept_connector
    if (buyAmount > 0) {
      const [buyCoin] = tx.splitCoins(tx.gas, [tx.pure.u64(buyAmount)]);
      tx.moveCall({
        target: `${config.contracts.hopfunPackageId}::meme::place_dev_order`,
        arguments: [
          tx.object(config.contracts.memeConfigId),
          tx.pure.u64(tempId),
          buyCoin,
        ],
      });
    } else {
      // Even with 0 buy amount, we need to call place_dev_order
      const [zeroCoin] = tx.splitCoins(tx.gas, [tx.pure.u64(0)]);
      tx.moveCall({
        target: `${config.contracts.hopfunPackageId}::meme::place_dev_order`,
        arguments: [
          tx.object(config.contracts.memeConfigId),
          tx.pure.u64(tempId),
          zeroCoin,
        ],
      });
    }

    // Step 2: Accept the connector to create the bonding curve
    // The connector was created when the coin was published and transferred to MemeConfig
    // We need to use tx.object with the connector ID directly as it's a receiving object
    tx.moveCall({
      target: `${config.contracts.hopfunPackageId}::meme::accept_connector`,
      arguments: [
        tx.object(config.contracts.hopdexConfigId), // DexConfig
        tx.object(config.contracts.memeConfigId), // MemeConfig
        tx.object(connectorId), // Receiving<Connector<T>> - SDK will handle receiving automatically
        tx.object(coinMetadataId), // CoinMetadata<T>
      ],
      typeArguments: [coinType],
    });

    // Set the sender
    tx.setSender(sender);

    return tx;
  }

  /**
   * Execute the complete token creation flow
   * This handles both publishing the coin and creating the bonding curve
   */
  static async executeTokenCreation(
    payload: TokenCreationPayload,
    sender: string,
    signAndExecuteTransaction: (params: {
      transaction: Transaction;
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    }) => Promise<any>,
  ): Promise<{ packageId: string; bondingCurveId?: string }> {
    try {
      const config = networkConfigService.getNetworkConfig();

      // Step 1: Publish the coin module
      const publishTx = this.createPublishCoinTransaction(payload, sender);
      const publishResult = await signAndExecuteTransaction({
        transaction: publishTx,
      });

      // The result contains digest and effects (base64 BCS encoded)
      if (!publishResult.digest) {
        throw new TokenCreationError(
          'Transaction failed: no digest returned',
          'TRANSACTION_FAILED',
          publishResult,
        );
      }

      // Fetch the full transaction details to get the created objects
      const transactionDetails = await this.getTransactionDetails(
        publishResult.digest,
      );

      if (
        !transactionDetails?.effects?.created ||
        transactionDetails.effects.created.length === 0
      ) {
        throw new TokenCreationError(
          'Failed to publish coin module: no objects created',
          'TRANSACTION_FAILED',
          transactionDetails,
        );
      }

      // Extract the package ID from the created objects
      // Packages have owner type 'Immutable'
      const packageObject = transactionDetails.effects.created.find(
        (obj: any) => obj.owner === 'Immutable',
      );

      if (!packageObject) {
        throw new TokenCreationError(
          'Could not find published package in transaction result',
          'TRANSACTION_FAILED',
          transactionDetails,
        );
      }

      const packageId = packageObject.reference.objectId;

      // Extract the CurrencyHolder and CoinMetadata objects from the publish transaction
      // The CurrencyHolder contains the treasury cap and will be used to create the Connector
      // The CoinMetadata is frozen (Immutable)
      const MEME_SHARED_OBJECT_ID = config.contracts.memeConfigId;

      // Find the CurrencyHolder object that was created and transferred to the sender
      const currencyHolderObject = transactionDetails.objectChanges?.find(
        (change: any) => {
          if (change.type === 'created' && change.objectType) {
            // Check if it's a CurrencyHolder type - it should match the pattern:
            // {coin_package}::template::CurrencyHolder<{coin_package}::template::TEMPLATE>
            if (
              change.objectType.includes('::template::CurrencyHolder<') &&
              change.objectType.includes('::template::TEMPLATE>')
            ) {
              // Verify the owner is the sender (creator)
              const owner = change.owner;
              if (owner && typeof owner === 'object' && owner.AddressOwner) {
                return owner.AddressOwner === sender;
              }
            }
          }
          return false;
        },
      );

      // CoinMetadata has a specific pattern with the coin type and is Immutable
      const coinMetadataObject = transactionDetails.objectChanges?.find(
        (change: any) => {
          if (change.type === 'created' && change.objectType) {
            // Check if it's CoinMetadata and has Immutable owner
            if (change.objectType.includes('0x2::coin::CoinMetadata<')) {
              const owner = change.owner;
              if (owner === 'Immutable') {
                return true;
              }
            }
          }
          return false;
        },
      );

      // Log all created objects for debugging
      console.log(
        'All created objects from publish transaction:',
        transactionDetails.objectChanges
          ?.filter((change: any) => change.type === 'created')
          .map((change: any) => ({
            objectId: change.objectId,
            objectType: change.objectType,
            owner: change.owner,
          })),
      );

      if (!currencyHolderObject || !coinMetadataObject) {
        console.error(
          'Could not find CurrencyHolder or CoinMetadata in transaction result',
          {
            currencyHolderObject,
            coinMetadataObject,
            objectChanges: transactionDetails.objectChanges,
            MEME_SHARED_OBJECT_ID,
          },
        );
        // Return just the package ID if we can't create the bonding curve
        return { packageId };
      }

      const currencyHolderId = currencyHolderObject.objectId;
      const coinMetadataId = coinMetadataObject.objectId;

      // Validate that the IDs are not undefined and are valid addresses
      if (!currencyHolderId || !coinMetadataId) {
        console.error('Invalid object IDs extracted:', {
          currencyHolderId,
          coinMetadataId,
        });
        return { packageId };
      }

      // Ensure the IDs are in the correct format (should start with 0x)
      if (
        !currencyHolderId.startsWith('0x') ||
        !coinMetadataId.startsWith('0x')
      ) {
        console.error('Object IDs are not in the correct format:', {
          currencyHolderId,
          coinMetadataId,
        });
        return { packageId };
      }

      console.log('Extracted IDs from publish transaction:', {
        packageId,
        currencyHolderId,
        currencyHolderObject,
        coinMetadataId,
        coinMetadataObject,
      });

      // Step 1.5: Create the connector using the CurrencyHolder
      console.log('Creating connector from CurrencyHolder...');

      // Validate that registryId exists
      const registryId = config.contracts.registryId;
      if (!registryId) {
        console.error('Registry ID not found in config');
        return { packageId };
      }

      const createConnectorTx = new Transaction();
      createConnectorTx.moveCall({
        target: `${packageId}::template::create_connector`,
        arguments: [
          createConnectorTx.object(currencyHolderId),
          createConnectorTx.object(registryId),
        ],
      });

      const createConnectorResult = await signAndExecuteTransaction({
        transaction: createConnectorTx,
      });

      console.log(
        'Create connector transaction result:',
        createConnectorResult,
      );

      // Get the detailed transaction result to extract objects
      const createConnectorDetails = await this.getTransactionDetails(
        createConnectorResult.digest,
      );

      // Extract the Connector object from the second transaction
      const connectorObject = createConnectorDetails.objectChanges?.find(
        (change: any) => {
          if (change.type === 'created' && change.objectType) {
            // Check if it's a Connector type
            if (
              change.objectType.includes('::connector::Connector<') &&
              change.objectType.includes('::template::TEMPLATE>')
            ) {
              return true;
            }
          }
          return false;
        },
      );

      if (!connectorObject) {
        console.error(
          'Could not find Connector in create_connector transaction result',
        );
        return { packageId };
      }

      const connectorId = connectorObject.objectId;

      console.log('Extracted connector ID:', {
        connectorId,
        connectorObject,
      });

      // Step 2: Create the bonding curve
      // The Connector has already been transferred to MemeConfig during token creation
      let bondingCurveId: string | undefined;

      try {
        const bondingCurveTx = this.createBondingCurveTransaction(
          packageId,
          payload.symbol,
          sender,
          connectorId,
          coinMetadataId,
          123, // TEMP_ID - should match the one in the coin template
          0, // No initial buy amount
        );

        const bondingCurveResult = await signAndExecuteTransaction({
          transaction: bondingCurveTx,
        });

        if (bondingCurveResult.digest) {
          const bondingCurveDetails = await this.getTransactionDetails(
            bondingCurveResult.digest,
          );
          if (
            bondingCurveDetails?.effects?.created &&
            bondingCurveDetails.effects.created.length > 0
          ) {
            // Look for the BondingCurve object
            const bondingCurveObject = bondingCurveDetails.objectChanges?.find(
              (change: any) =>
                change.type === 'created' &&
                change.objectType?.includes('::meme::BondingCurve<'),
            );
            bondingCurveId = bondingCurveObject?.objectId;
          }
        }
      } catch (error) {
        console.error('Failed to create bonding curve:', error);
        // Bonding curve creation is optional, so we don't throw here
      }

      return {
        packageId,
        bondingCurveId,
      };
    } catch (error) {
      if (error instanceof TokenCreationError) {
        throw error;
      }
      throw new TokenCreationError(
        'Token creation failed',
        'TRANSACTION_FAILED',
        error,
      );
    }
  }

  /**
   * Fetch transaction details from the blockchain with retry mechanism
   */
  private static async getTransactionDetails(digest: string): Promise<any> {
    const config = networkConfigService.getNetworkConfig();
    const maxRetries = 10; // Maximum number of retry attempts
    const initialDelay = 1000; // Initial delay in milliseconds
    const maxDelay = 30000; // Maximum delay between retries

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const response = await fetch(config.rpcUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            jsonrpc: '2.0',
            id: 1,
            method: 'sui_getTransactionBlock',
            params: [
              digest,
              {
                showInput: true,
                showRawInput: false,
                showEffects: true,
                showEvents: true,
                showObjectChanges: true,
                showBalanceChanges: true,
              },
            ],
          }),
        });

        const data = await response.json();

        // Check if we got a valid result
        if (data.result) {
          console.log(
            `Transaction ${digest} fetched successfully after ${attempt + 1} attempt(s)`,
          );
          return data.result;
        }

        // Check for specific error conditions
        if (data.error) {
          const errorMessage = data.error.message || '';

          // If the error indicates the transaction doesn't exist yet, retry
          if (
            errorMessage.includes(
              'Could not find the referenced transaction',
            ) ||
            errorMessage.includes('Transaction not found') ||
            errorMessage.includes('not found')
          ) {
            console.log(
              `Transaction ${digest} not found yet, attempt ${attempt + 1}/${maxRetries}`,
            );

            // If this isn't the last attempt, wait before retrying
            if (attempt < maxRetries - 1) {
              // Calculate delay with exponential backoff and jitter
              const baseDelay = Math.min(
                initialDelay * Math.pow(2, attempt),
                maxDelay,
              );
              const jitter = Math.random() * 0.3 * baseDelay; // Add up to 30% jitter
              const delay = baseDelay + jitter;

              console.log(`Waiting ${Math.round(delay)}ms before retry...`);
              await new Promise((resolve) => setTimeout(resolve, delay));
              continue; // Try again
            }
          }

          // For other errors, throw immediately
          throw new Error(
            errorMessage || 'Failed to fetch transaction details',
          );
        }

        // If we got here, something unexpected happened
        throw new Error('Unexpected response format from RPC');
      } catch (error) {
        // If this is a network error and not the last attempt, retry
        if (attempt < maxRetries - 1 && error instanceof TypeError) {
          const delay = Math.min(initialDelay * Math.pow(2, attempt), maxDelay);
          console.log(`Network error, retrying in ${delay}ms...`);
          await new Promise((resolve) => setTimeout(resolve, delay));
          continue;
        }

        // If this is the last attempt or a non-retryable error, throw
        if (attempt === maxRetries - 1) {
          console.error(
            `Failed to fetch transaction ${digest} after ${maxRetries} attempts:`,
            error,
          );
          throw new TokenCreationError(
            `Transaction ${digest} not found after ${maxRetries} attempts. The transaction may still be processing.`,
            'TRANSACTION_NOT_FOUND',
            error,
          );
        }

        // For other errors, throw immediately
        console.error('Error fetching transaction details:', error);
        throw new TokenCreationError(
          'Failed to fetch transaction details',
          'NETWORK_ERROR',
          error,
        );
      }
    }

    // This should never be reached, but just in case
    throw new TokenCreationError(
      'Failed to fetch transaction details',
      'NETWORK_ERROR',
    );
  }

  /**
   * Helper to validate the token creation payload
   */
  static validatePayload(payload: TokenCreationPayload): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Name validation
    if (!payload.name || payload.name.length === 0) {
      errors.push('Token name is required');
    } else if (payload.name.length > 32) {
      errors.push('Token name must be 32 characters or less');
    }

    // Symbol validation
    if (!payload.symbol || payload.symbol.length === 0) {
      errors.push('Token symbol is required');
    } else if (payload.symbol.length > 10) {
      errors.push('Token symbol must be 10 characters or less');
    } else if (!/^[A-Z0-9]+$/i.test(payload.symbol)) {
      errors.push('Token symbol must contain only letters and numbers');
    }

    // Description validation
    if (payload.description && payload.description.length > 500) {
      errors.push('Description must be 500 characters or less');
    }

    // URL validations
    if (payload.twitter && !this.isValidUrl(payload.twitter)) {
      errors.push('Invalid Twitter URL');
    }

    if (payload.website && !this.isValidUrl(payload.website)) {
      errors.push('Invalid website URL');
    }

    if (payload.telegram && !this.isValidUrl(payload.telegram)) {
      errors.push('Invalid Telegram URL');
    }

    // Total supply validation
    if (!payload.totalSupply || payload.totalSupply <= 0) {
      errors.push('Total supply must be greater than 0');
    } else if (payload.totalSupply > 1e15) {
      // Max 1 quadrillion
      errors.push('Total supply is too large');
    }

    // Decimals validation
    if (payload.decimals < 0 || payload.decimals > 18) {
      errors.push('Decimals must be between 0 and 18');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  private static isValidUrl(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
    } catch {
      return false;
    }
  }

  /**
   * Check if the current network is supported for token creation
   */
  static isNetworkSupported(): boolean {
    const network = networkConfigService.getCurrentNetwork();
    const config = networkConfigService.getNetworkConfig();

    // Check if the necessary contract addresses are configured
    return (
      config.contracts.hopfunPackageId !== '0x0' &&
      config.contracts.memeConfigId !== '0x0'
    );
  }

  /**
   * Get a user-friendly error message for common errors
   */
  static getErrorMessage(error: any): string {
    if (error instanceof TokenCreationError) {
      return error.message;
    }

    if (error?.message?.includes('Rejected from user')) {
      return 'Transaction was cancelled by user';
    }

    if (error?.message?.includes('Insufficient balance')) {
      return 'Insufficient balance to pay for gas';
    }

    if (error?.message?.includes('Network')) {
      return 'Network error. Please check your connection and try again';
    }

    return error?.message || 'An unexpected error occurred';
  }
}
