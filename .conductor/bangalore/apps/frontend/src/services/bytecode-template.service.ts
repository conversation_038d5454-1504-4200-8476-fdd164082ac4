/**
 * Service for handling Move bytecode template manipulation
 * This service is responsible for creating customized coin modules
 * by modifying the base template with token-specific metadata
 */

import bytecodeData from './bytecode-data.json';

interface TokenMetadata {
  name: string;
  symbol: string;
  description: string;
  iconUrl: string;
  twitter: string;
  website: string;
  telegram: string;
  totalSupply: number;
  decimals: number;
}

export class BytecodeTemplateService {
  // Use the actual compiled bytecode from the Move module
  private static readonly TEMPLATE_BYTECODE_BASE64 = bytecodeData.bytecode;
  private static readonly DEPENDENCIES = bytecodeData.dependencies;

  /**
   * Generates a module name for the token
   * IMPORTANT: This must be deterministic and match the module name in the bytecode
   * Since we're using a fixed template, we need to use the fixed module name "template"
   */
  static generateModuleName(_symbol: string): string {
    // The module name in the bytecode is "template" as defined in coin_template::template
    // We must use this exact name to reference the module correctly
    return 'template';
  }

  /**
   * Creates the coin module bytecode with token metadata
   * For now, we return the template bytecode as-is since modifying Move bytecode
   * at runtime is complex and requires deep understanding of the bytecode format.
   *
   * In production, you would either:
   * 1. Have a backend service that compiles Move code with the metadata
   * 2. Use a Move bytecode manipulation library (if available)
   * 3. Deploy the template contract and use entry functions to configure it
   */
  static createCoinModuleBytecode(metadata: TokenMetadata): Uint8Array {
    // Log the metadata for debugging
    const moduleName = this.generateModuleName(metadata.symbol);
    const structName = metadata.symbol.toUpperCase();
    const totalSupplyWithDecimals =
      metadata.totalSupply * Math.pow(10, metadata.decimals);
    const templateId = Date.now() % 1000000;

    console.log('Creating coin module with:', {
      moduleName,
      structName,
      metadata,
      totalSupplyWithDecimals,
      templateId,
    });

    // Return the actual template bytecode
    // Note: The template bytecode already contains placeholder values
    // that the hopfun::connector::new function will use
    return this.getTemplateBytecode();
  }

  /**
   * Get the raw base64 bytecode string (for SDK publish)
   */
  static getBase64Bytecode(): string {
    return BytecodeTemplateService.TEMPLATE_BYTECODE_BASE64;
  }

  /**
   * Validates that the bytecode template is available and valid
   */
  static isTemplateAvailable(): boolean {
    return BytecodeTemplateService.TEMPLATE_BYTECODE_BASE64.length > 0;
  }

  /**
   * Decodes the base64 template bytecode
   */
  static getTemplateBytecode(): Uint8Array {
    if (!this.isTemplateAvailable()) {
      throw new Error(
        'Bytecode template not available. Please build the Move module first.',
      );
    }

    // Decode base64 to Uint8Array
    const binaryString = atob(BytecodeTemplateService.TEMPLATE_BYTECODE_BASE64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    return bytes;
  }

  /**
   * Creates Move source code for a new token
   * This is an alternative approach where we generate Move source
   * that would need to be compiled
   */
  static generateMoveSource(metadata: TokenMetadata): string {
    const moduleName = this.generateModuleName(metadata.symbol);
    const structName = metadata.symbol.toUpperCase();
    const totalSupplyWithDecimals =
      metadata.totalSupply * Math.pow(10, metadata.decimals);
    const templateId = Date.now() % 1000000;

    return `module coin_template::${moduleName} {

    use std::string;
    use hopfun::connector;

    /// The OTW for the Coin
    public struct ${structName} has drop {}

    const ICON_URL: vector<u8> = b"${metadata.iconUrl || ''}";

    const TWITTER: vector<u8> = b"${metadata.twitter || ''}";
    const WEBSITE: vector<u8> = b"${metadata.website || ''}";
    const TELEGRAM: vector<u8> = b"${metadata.telegram || ''}";

    const NAME: vector<u8> = b"${metadata.name}";
    const SYMBOL: vector<u8> = b"${metadata.symbol}";
    const DESCRIPTION: vector<u8> = b"${metadata.description || ''}";

    const TEMP_ID: u64 = ${templateId}u64;

    const TOTAL_SUPPLY: u64 = ${totalSupplyWithDecimals};

    /// Init the Coin
    fun init(witness: ${structName}, ctx: &mut TxContext) {
        connector::new<${structName}>(
            witness,
            TEMP_ID,

            NAME,
            SYMBOL,
            DESCRIPTION,
            ICON_URL,

            string::utf8(TWITTER),
            string::utf8(WEBSITE),
            string::utf8(TELEGRAM),

            TOTAL_SUPPLY,

            ctx
        );
    }
}`;
  }

  /**
   * Get the dependencies for the bytecode
   * These are the exact dependencies the bytecode was compiled with
   */
  static getDependencies(): string[] {
    return BytecodeTemplateService.DEPENDENCIES;
  }
}
