{"bytecode": "oRzrCwYAAAALAQAWAhY8A1JNBJ8BEAWvAbEBB+AChQMI5QWgAQaFB2YK6wcbC4YIAgyICHkAJAMeBA4BHAEhAgwCDQIbAiUCJwIoAAcCAAAECAEAAQEDCAADBQcBAAAEBgcABQAEAQABBgEMAQABBgIMAQABBggMAQABBwoEAAkJAgAKCwcAABUAAQAADwIBAAIZGAEBAAMgBgcBAAQpBBcABhAJCgECBhYNEgEABhcMDQEABxIRAQAHGBARAAgdBgEBDAglFAEBCAkfDg8AChoEBQADBQUICgsHCAYICxMKFgIIAggABwgKAAMLAQEIAAYIAgcICgQFCwcBCAALBgEIAAsIAQgAAQoCAQgLAQkAAQsDAQkAAQgABwkAAgoCCgIKAgsDAQgLBwgKAgsIAQkACwcBCQABCwcBCAADBwsIAQkAAwcICgELBgEJAAEGCAoBBQEHCAoBCAkBCwUBCQABCwEBCAACCQAFBAULBQEIAAMLCAEIAAELCAEIAAEIBAgDCwUBCQAIBAgECAQFBggCBwgKB0JhbGFuY2UEQ29pbgxDb2luTWV0YWRhdGEOQ29uZmlnUmVnaXN0cnkOQ3VycmVuY3lIb2xkZXIGT3B0aW9uBlN0cmluZwhURU1QTEFURQtUcmVhc3VyeUNhcAlUeENvbnRleHQDVUlEA1VybAdiYWxhbmNlBGNvaW4JY29ubmVjdG9yEGNyZWF0ZV9jb25uZWN0b3IPY3JlYXRlX2N1cnJlbmN5B2NyZWF0b3IGZGVsZXRlC2R1bW15X2ZpZWxkAmlkBGluaXQMaW50b19iYWxhbmNlBG1pbnQDbmV3D25ld19mcm9tX3N1cHBseRVuZXdfdW5zYWZlX2Zyb21fYnl0ZXMGb2JqZWN0Bm9wdGlvbhRwdWJsaWNfZnJlZXplX29iamVjdAhyZWdpc3RyeQZzZW5kZXIEc29tZQZzdHJpbmcGc3VwcGx5B3RlbXBfaWQIdGVtcGxhdGUIdHJhbnNmZXIMdHJlYXN1cnlfY2FwCnR4X2NvbnRleHQDdXJsBHV0ZjgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIBJ5OQr6JKKFhyGSg9AHhe7UkDxB6AyztjY5k8FdOKvHuxmiVTk1p3V/M7c1AK6ivOn/BqFMMIsSCxcYIu1B1yCgIJCEltYWdlVXJsAgEGCgIIB1R3aXR0ZXIKAggHV2Vic2l0ZQoCCQhUZWxlZ3JhbQoCBQROYW1lCgIHBlN5bWJvbAoCDAtEZXNjcmlwdGlvbgMIewAAAAAAAAADCACAxqR+jQMAAAIBEwEBAgUUCAkmCwgBCQAiCwUBCQAjAxEFAQgAAAAAAyILAAcBBwYHBQcHBwARDTgACgE4AQwDDAULAzgCDQUHCQoBOAMMBAoBLhEMDAILAREJCwULBDgEBwgKAjkACwI4BQIBAQQAFRYLADoADAMMBQwEDAYRCAsGOAYLBQsEBwIRBAcDEQQHBBEECwMLAQsCOAcCAA==", "dependencies": ["0x0000000000000000000000000000000000000000000000000000000000000001", "0x0000000000000000000000000000000000000000000000000000000000000002", "0x01279390afa24a28587219283d00785eed4903c41e80cb3b6363993c15d38abc", "0x55b04c619307e842a4644314bf30bd1fc8920ea1df36c305728eaf36cd658327", "0x7bb19a2553935a7757f33b73500aea2bce9ff06a14c308b120b171822ed41d72"], "metadata": {"name": "TEMPLATE", "symbol": "TEMPLATE", "description": "Template coin with registry support", "iconUrl": "ImageUrl", "totalSupply": "1000000000000000", "decimals": 6, "usesRegistry": true, "version": "2.0.0"}, "checksum": "==", "generatedAt": "2025-08-11T10:58:46.775Z"}