import { getFullnodeUrl } from '@mysten/sui/client';

export type Network = 'devnet' | 'testnet' | 'mainnet';

interface ContractConfig {
  hopfunPackageId: string;
  memeConfigId: string;
  hopfunAdminCap: string;
  hopdexPackageId: string;
  hopdexConfigId: string;
  registryId?: string; // Config Registry ID (optional for backward compatibility)
  registryPackageId?: string; // Config Registry Package ID
  useRegistry?: boolean; // Flag to indicate if registry should be used
}

interface NetworkConfig {
  network: Network;
  rpcUrl: string;
  contracts: ContractConfig;
}

class NetworkConfigService {
  private static instance: NetworkConfigService;
  private currentNetwork: Network;

  private constructor() {
    const envNetwork = process.env.NEXT_PUBLIC_NETWORK ?? 'devnet';
    this.currentNetwork = this.validateNetwork(envNetwork);
  }

  static getInstance(): NetworkConfigService {
    NetworkConfigService.instance = new NetworkConfigService();
    return NetworkConfigService.instance;
  }

  private validateNetwork(network: string): Network {
    const validNetworks: Network[] = ['devnet', 'testnet', 'mainnet'];
    if (validNetworks.includes(network as Network)) {
      return network as Network;
    }
    console.warn(`Invalid network: ${network}, defaulting to devnet`);
    return 'devnet';
  }

  getCurrentNetwork(): Network {
    return this.currentNetwork;
  }

  setNetwork(network: Network): void {
    this.currentNetwork = network;
  }

  getNetworkConfig(): NetworkConfig {
    const network = this.getCurrentNetwork();
    const contracts = this.getContractConfig(network);
    const rpcUrl = this.getRpcUrl(network);

    return {
      network,
      rpcUrl,
      contracts,
    };
  }

  private getRpcUrl(network: Network): string {
    // Use the SUI SDK's built-in RPC URLs
    return getFullnodeUrl(network);
  }

  private getContractConfig(network: Network): ContractConfig {
    switch (network) {
      case 'devnet':
        return {
          hopfunPackageId:
            process.env.NEXT_PUBLIC_HOPFUN_PACKAGE_ID_DEVNET ??
            '0x7bb19a2553935a7757f33b73500aea2bce9ff06a14c308b120b171822ed41d72',
          memeConfigId:
            process.env.NEXT_PUBLIC_MEME_CONFIG_ID_DEVNET ??
            '0xf0d970d4459922d14862d10cfb6464abd84a90e86e394574f7e474b7aa5e85ef',
          hopfunAdminCap:
            process.env.NEXT_PUBLIC_HOPFUN_ADMIN_CAP_DEVNET ??
            '0x6c8c96b67051337d39af8bc9b3579cc9f3680b302c88b687727db20f76033770',
          hopdexPackageId:
            process.env.NEXT_PUBLIC_HOPDEX_PACKAGE_ID_DEVNET ??
            '0x55b04c619307e842a4644314bf30bd1fc8920ea1df36c305728eaf36cd658327',
          hopdexConfigId:
            process.env.NEXT_PUBLIC_HOPDEX_CONFIG_ID_DEVNET ??
            '0x227219002b36b702a68eef0ee93bb95c35289068f9526bc53dfea0fb9dd77f59',
          registryId:
            process.env.NEXT_PUBLIC_REGISTRY_ID_DEVNET ??
            '0xe4759fb4dd9ff60ceecc4fbc278a0ce0e68c09edf925b9dce2e5ba16c7221d1c',
          registryPackageId:
            process.env.NEXT_PUBLIC_REGISTRY_PACKAGE_ID_DEVNET ??
            '0x01279390afa24a28587219283d00785eed4903c41e80cb3b6363993c15d38abc',
          useRegistry: process.env.NEXT_PUBLIC_USE_REGISTRY === 'true',
        };

      case 'testnet':
        // Return devnet values for testnet as fallback for now
        return {
          hopfunPackageId:
            process.env.NEXT_PUBLIC_HOPFUN_PACKAGE_ID_TESTNET ?? '0x0',
          memeConfigId: process.env.NEXT_PUBLIC_MEME_CONFIG_ID_TESTNET ?? '0x0',
          hopfunAdminCap:
            process.env.NEXT_PUBLIC_HOPFUN_ADMIN_CAP_TESTNET ?? '0x0',
          hopdexPackageId:
            process.env.NEXT_PUBLIC_HOPDEX_PACKAGE_ID_TESTNET ?? '0x0',
          hopdexConfigId:
            process.env.NEXT_PUBLIC_HOPDEX_CONFIG_ID_TESTNET ?? '0x0',
          registryId: process.env.NEXT_PUBLIC_REGISTRY_ID_TESTNET ?? undefined,
          registryPackageId:
            process.env.NEXT_PUBLIC_REGISTRY_PACKAGE_ID_TESTNET ?? undefined,
          useRegistry: false,
        };

      case 'mainnet':
        return {
          hopfunPackageId:
            process.env.NEXT_PUBLIC_HOPFUN_PACKAGE_ID_MAINNET ?? '0x0',
          memeConfigId: process.env.NEXT_PUBLIC_MEME_CONFIG_ID_MAINNET ?? '0x0',
          hopfunAdminCap:
            process.env.NEXT_PUBLIC_HOPFUN_ADMIN_CAP_MAINNET ?? '0x0',
          hopdexPackageId:
            process.env.NEXT_PUBLIC_HOPDEX_PACKAGE_ID_MAINNET ?? '0x0',
          hopdexConfigId:
            process.env.NEXT_PUBLIC_HOPDEX_CONFIG_ID_MAINNET ?? '0x0',
          registryId: process.env.NEXT_PUBLIC_REGISTRY_ID_MAINNET ?? undefined,
          registryPackageId:
            process.env.NEXT_PUBLIC_REGISTRY_PACKAGE_ID_MAINNET ?? undefined,
          useRegistry: false,
        };

      default:
        throw new Error(`Unsupported network: ${network as string}`);
    }
  }

  isProductionNetwork(): boolean {
    return this.currentNetwork === 'mainnet';
  }

  isDevelopmentNetwork(): boolean {
    return (
      this.currentNetwork === 'devnet' || this.currentNetwork === 'testnet'
    );
  }
}

export const networkConfigService = NetworkConfigService.getInstance();
