import type { AdvancedListType } from '@/features/advanced/hooks/use-advanced-websocket';
import type { Token } from '@/types/token';

// Helper function to generate random token data
const generateToken = (id: string, overrides: Partial<Token> = {}): Token => ({
  id,
  address: `0x${Math.random().toString(16).slice(2, 10)}`,
  name: `Token ${id}`,
  description: `This is a mock token ${id} for testing purposes`,
  symbol: `TK${id.slice(-3).toUpperCase()}`,
  emoji: ['🚀', '🌙', '💎', '🔥', '⚡', '🌟'][Math.floor(Math.random() * 6)],
  imageUrl: `https://picsum.photos/64/64`,
  price: Math.random() * 10,
  marketCap: Math.random() * 1000000,
  tradeFee: Math.random() * 5,
  volume24h: Math.random() * 100000,
  change24h: (Math.random() - 0.5) * 200,
  circulatingSupply: Math.random() * 1000000,
  totalSupply: Math.random() * 2000000,
  maxSupply: Math.random() > 0.5 ? Math.random() * 3000000 : undefined,
  createdAt: new Date(Date.now() - Math.random() * 86400000 * 30).toISOString(),
  ...overrides,
});

// Newly Created - Recent tokens with low market cap
const NEWLY_CREATED_TOKENS: Token[] = Array.from({ length: 12 }, (_, i) =>
  generateToken(`newly-${i + 1}`, {
    marketCap: Math.random() * 10000, // Lower market cap
    createdAt: new Date(
      Date.now() - Math.random() * 86400000 * 3,
    ).toISOString(), // Created within last 3 days
    price: Math.random() * 0.1, // Lower price
  }),
);

// Graduating - Tokens approaching graduation criteria
const GRADUATING_TOKENS: Token[] = Array.from({ length: 8 }, (_, i) =>
  generateToken(`graduating-${i + 1}`, {
    marketCap: 60000 + Math.random() * 20000, // Near graduation threshold
    volume24h: Math.random() * 50000 + 25000, // Higher volume
    change24h: Math.random() * 50 + 10, // Positive growth
  }),
);

// Graduated - Tokens that have graduated (higher market cap)
const GRADUATED_TOKENS: Token[] = Array.from({ length: 15 }, (_, i) =>
  generateToken(`graduated-${i + 1}`, {
    marketCap: Math.random() * 5000000 + 100000, // Higher market cap
    volume24h: Math.random() * 500000 + 100000, // High volume
    price: Math.random() * 50 + 1, // Higher price
  }),
);

// Watchlist - Mix of interesting tokens
const WATCHLIST_TOKENS: Token[] = Array.from({ length: 10 }, (_, i) =>
  generateToken(`watchlist-${i + 1}`, {
    change24h: (Math.random() - 0.3) * 100, // Mix of positive and negative
    volume24h: Math.random() * 200000 + 10000,
  }),
);

export const ADVANCED_TOKEN_MOCK_DATA: Record<AdvancedListType, Token[]> = {
  'newly-created': NEWLY_CREATED_TOKENS,
  graduating: GRADUATING_TOKENS,
  graduated: GRADUATED_TOKENS,
  watchlist: WATCHLIST_TOKENS,
};

// Function to get mock data for a specific list type
export const getMockDataForType = (type: AdvancedListType): Token[] => {
  return ADVANCED_TOKEN_MOCK_DATA[type];
};

// Function to simulate WebSocket data updates
export const getUpdatedMockData = (type: AdvancedListType): Token[] => {
  const baseData = getMockDataForType(type);

  // Simulate price and market cap changes
  return baseData.map((token) => ({
    ...token,
    price: token.price * (0.95 + Math.random() * 0.1), // ±5% price change
    marketCap: token.marketCap * (0.95 + Math.random() * 0.1),
    circulatingSupply: token.circulatingSupply * (0.95 + Math.random() * 0.1),
    totalSupply: token.totalSupply * (0.95 + Math.random() * 0.1),
    volume24h: token.volume24h * (0.9 + Math.random() * 0.2),
    change24h: token.change24h + (Math.random() - 0.5) * 10,
  }));
};
