import type { ComponentPropsWithoutRef } from 'react';

import type { AdvancedToken } from '@/features/advanced/components/advanced-card';
import { AdvancedCard } from '@/features/advanced/components/advanced-card';
import { cn } from '@/utils/classnames';

interface AdvancedListProps extends ComponentPropsWithoutRef<'div'> {
  items: AdvancedToken[];
  amount: number;
}

export const AdvancedList = ({
  items,
  amount,
  className,
  ...props
}: AdvancedListProps) => {
  return (
    <div
      className={cn(
        'flex h-[calc(100vh-300px)] flex-col gap-1 overflow-y-auto',
        className,
      )}
      {...props}
    >
      {items.map((item, index) => (
        <AdvancedCard key={index} amount={amount} token={item} />
      ))}
    </div>
  );
};
