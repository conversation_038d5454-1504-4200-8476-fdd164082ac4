import type { TokenActivity } from '@/types/token';

const generateRealisticPrice = (): string => {
  const random = Math.random();

  if (random < 0.4) {
    // Small prices like 0.038087289 (many decimal places)
    return (Math.random() * 0.1).toFixed(9);
  } else if (random < 0.7) {
    // Medium prices like 1.008 (few decimal places)
    return (Math.random() * 10 + 0.1).toFixed(3);
  } else {
    // Simple prices like 0.38 (2 decimal places)
    return (Math.random() * 1).toFixed(2);
  }
};

export const ACTIVITY_MOCK_DATA: TokenActivity[] = Array.from(
  { length: 10 },
  (_, index) => ({
    id: `activity-${index + 1}`,
    type: index % 2 === 0 ? 'buy' : 'sell',
    price: generateRealisticPrice(),
    mini: (
      Math.floor(Math.random() * (********** - ********* + 1)) + *********
    ).toString(),
    account: `0x${Math.random().toString(16).slice(2, 10)}`,
    createdAt: new Date(
      Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000,
    ).toISOString(), // Random date within last week
  }),
);
