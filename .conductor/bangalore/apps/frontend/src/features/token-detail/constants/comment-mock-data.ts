import type { TokenComment } from '@/types/token';

const SAMPLE_COMMENTS = [
  'Just bought some tokens! 🚀 Looking forward to the **upcoming developments**.',
  "The tokenomics look solid. Here's what I found:\n\n- Good distribution model\n- Strong utility case\n- Active community",
  "```javascript\nconst analysis = {\n  potential: 'high',\n  risk: 'medium'\n};\n```\n\nMy technical analysis suggests this could be promising.",
  '## Market Update\n\nSeeing some interesting *movement* in the charts today. The volume is picking up significantly.',
  'Has anyone else noticed the correlation with [Bitcoin](https://bitcoin.org)? The patterns are quite similar.',
  '⚠️ **Important**: Always DYOR before investing. This is not financial advice!\n\n> Remember: only invest what you can afford to lose.',
  "The development team's [latest update](https://example.com) is very encouraging. Key highlights:\n\n1. Mainnet launch on schedule\n2. New partnerships announced\n3. Audit completed",
  'Love the community here! 💪 Everyone is so helpful and knowledgeable.',
  '## Price Prediction\n\nBased on recent trends:\n- Short term: *cautiously optimistic*\n- Long term: **very bullish**\n\nWhat do you all think?',
  'Just shared this project with my crypto group. The response has been overwhelmingly positive! 📈',
];

const SAMPLE_USERNAMES = [
  'CryptoTrader2024',
  'BlockchainBull',
  'TokenAnalyst',
  'DeFiExplorer',
  'MoonShotHunter',
  'ChartMaster',
  'HODLer4Life',
  'CommunityMember',
  'TechAnalysisGuru',
  'CryptoEnthusiast',
  'DiamondHands',
  'AltcoinAddict',
  'SmartContractDev',
  'MarketWatcher',
  'TokenCollector',
  'CryptoNewbie',
  'InvestorPro',
  'BlockchainFan',
  'CryptoWhale',
  'DegenTrader',
];

export const COMMENT_MOCK_DATA: TokenComment[] = Array.from(
  { length: 20 },
  (_, index) => ({
    id: `comment-${index + 1}`,
    author: SAMPLE_USERNAMES[index % SAMPLE_USERNAMES.length],
    content: SAMPLE_COMMENTS[index % SAMPLE_COMMENTS.length],
    createdAt: new Date(
      Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000,
    ).toISOString(), // Random date within last week
    avatarUrl: `https://i.pravatar.cc/150`, // Unique avatar for each comment
  }),
);
