import type { ComponentPropsWithoutRef, ReactNode } from 'react';

import { cn } from '@/utils/classnames';

interface TabSectionTitleProps extends ComponentPropsWithoutRef<'h2'> {
  title: string;
  icon?: ReactNode;
}

export const TabSectionTitle = ({
  title,
  icon,
  className,
  ...props
}: TabSectionTitleProps) => {
  return (
    <h2
      className={cn(
        'flex items-center gap-2 pl-1 font-medium',
        "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
        className,
      )}
      {...props}
    >
      {icon}
      {title}
    </h2>
  );
};
