import Image from 'next/image';
import { useFormatter } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';

import { Markdown } from '@/components/markdown';
import type { TokenComment } from '@/types/token';
import { cn } from '@/utils/classnames';

interface CommentCardProps extends ComponentPropsWithoutRef<'div'> {
  comment: TokenComment;
}

export const CommentCard = ({
  comment,
  className,
  ...props
}: CommentCardProps) => {
  const format = useFormatter();

  return (
    <div
      className={cn('bg-card flex flex-col gap-2 rounded-lg p-4', className)}
      {...props}
    >
      <div className="flex items-center gap-2">
        <div className="relative size-8 overflow-hidden rounded-full">
          <Image
            fill
            alt={`${comment.author}'s avatar`}
            className="rounded-full object-cover"
            sizes="32px"
            src={comment.avatarUrl}
          />
        </div>
        <span className="font-semibold">{comment.author}</span>
      </div>
      <div className="text-muted-foreground prose prose-sm dark:prose-invert max-w-none text-sm">
        <Markdown>{comment.content}</Markdown>
      </div>
      <span className="text-muted-foreground text-xs">
        {format.dateTime(new Date(comment.createdAt), {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: true,
        })}
      </span>
    </div>
  );
};
