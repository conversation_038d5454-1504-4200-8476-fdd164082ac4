'use client';

import type { ComponentPropsWithoutRef } from 'react';

import { cn } from '@/utils/classnames';

// const TradingViewChart = dynamic(
//   () =>
//     import('@/components/trading-view-chart').then(
//       (mod) => mod.TradingViewChart,
//     ),
//   {
//     ssr: false,
//     loading: () => (
//       <div className="flex h-96 items-center justify-center rounded bg-gray-100">
//         <div className="text-gray-500">Loading chart...</div>
//       </div>
//     ),
//   },
// );

interface ChartSectionProps extends ComponentPropsWithoutRef<'section'> {
  address: string;
}

export const ChartSection = ({
  className,
  address,
  ...props
}: ChartSectionProps) => {
  return (
    <section
      className={cn('flex h-[600px] w-full flex-col gap-4', className)}
      {...props}
    >
      {/* Alert here if needed */}
      {/* // TODO:Implement trading view chart  */}
      <div className="flex h-full items-center justify-center rounded bg-gray-100">
        <p>Chart for token: {address}</p>
      </div>
    </section>
  );
};
