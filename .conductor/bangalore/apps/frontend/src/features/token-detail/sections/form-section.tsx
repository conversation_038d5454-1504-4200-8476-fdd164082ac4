import type { ComponentPropsWithoutRef } from 'react';

import { BuySellFormTabs } from '@/features/token-detail/components/buy-sell-form-tabs';
import { MarketCapProgress } from '@/features/token-detail/components/market-cap-progress';
import { useGetTokenDetail } from '@/hooks/use-get-token-detail';
import { cn } from '@/utils/classnames';

interface FormSectionProps extends ComponentPropsWithoutRef<'section'> {
  address: string;
}

export const FormSection = ({
  address,
  className,
  ...props
}: FormSectionProps) => {
  const token = useGetTokenDetail(address);

  return (
    <section
      className={cn(
        'bg-card border-primary flex flex-col gap-4 rounded-lg border p-4',
        className,
      )}
      {...props}
    >
      <MarketCapProgress
        circulatingSupply={token.circulatingSupply}
        marketCap={token.marketCap}
        totalSupply={token.totalSupply}
      />
      <BuySellFormTabs address={address} />
    </section>
  );
};
