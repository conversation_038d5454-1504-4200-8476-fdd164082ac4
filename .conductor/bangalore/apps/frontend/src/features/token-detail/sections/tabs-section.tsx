'use client';

import { useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';

import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ActivityTable } from '@/features/token-detail/components/activity-table';
import { CoinInfo } from '@/features/token-detail/components/coin-info';
import { CommentList } from '@/features/token-detail/components/comment-list';
import { TopHoldersTable } from '@/features/token-detail/components/top-holders-table';
import { COMMENT_MOCK_DATA } from '@/features/token-detail/constants/comment-mock-data';
import { useGetTokenDetail } from '@/hooks/use-get-token-detail';
import { useResponsive } from '@/hooks/use-responsive';
import { cn } from '@/utils/classnames';

type TabsSectionState = 'replies' | 'coin-info' | 'activity' | 'top-holders';

interface TabsSectionProps extends ComponentPropsWithoutRef<'section'> {
  address: string;
}

export const TabsSection = ({
  className,
  address,
  ...props
}: TabsSectionProps) => {
  const [selectedTab, setSelectedTab] = useState<TabsSectionState>('coin-info');
  const [isSticky, setIsSticky] = useState(false);
  const [showLeftMask, setShowLeftMask] = useState(false);
  const [showRightMask, setShowRightMask] = useState(false);
  const tabsRef = useRef<HTMLDivElement>(null);
  const tabsListRef = useRef<HTMLDivElement>(null);

  const token = useGetTokenDetail(address);
  const { isMobileOrTablet } = useResponsive();
  const t = useTranslations('token-detail.tabs-section');

  const TAB_ITEMS = [
    {
      value: 'replies',
      label: t('replies'),
    },
    {
      value: 'coin-info',
      label: t('coin-info'),
    },
    {
      value: 'activity',
      label: t('activity'),
    },
    {
      value: 'top-holders',
      label: t('top-holders'),
    },
  ];

  // Check if the tabs section is sticky based on scroll position
  useEffect(() => {
    if (!isMobileOrTablet) {
      setIsSticky(false);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsSticky(!entry.isIntersecting);
      },
      {
        rootMargin: '-1px 0px 0px 0px',
        threshold: 0,
      },
    );

    if (tabsRef.current) {
      observer.observe(tabsRef.current);
    }

    return () => observer.disconnect();
  }, [isMobileOrTablet]);

  // Check scroll position to show/hide masks
  const updateMaskVisibility = useCallback(() => {
    if (!tabsListRef.current || !isMobileOrTablet) {
      setShowLeftMask(false);
      setShowRightMask(false);
      return;
    }

    const { scrollLeft, scrollWidth, clientWidth } = tabsListRef.current;
    const isAtStart = scrollLeft <= 0;
    const isAtEnd = scrollLeft >= scrollWidth - clientWidth - 1; // -1 for rounding

    setShowLeftMask(!isAtStart);
    setShowRightMask(!isAtEnd);
  }, [isMobileOrTablet]);

  // Update mask visibility on mount and when isMobileOrTablet changes
  useEffect(() => {
    updateMaskVisibility();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isMobileOrTablet]);

  const handleTabChange = (value: string) => {
    setSelectedTab(value as TabsSectionState);
  };

  const handleScroll = () => {
    updateMaskVisibility();
  };

  return (
    <section className={cn('flex w-full flex-col gap-4', className)} {...props}>
      <div ref={tabsRef} className="pointer-events-none h-1 w-full" />
      <Tabs
        className="-mt-4 gap-4"
        value={selectedTab}
        onValueChange={handleTabChange}
      >
        <div
          className={cn('relative', {
            'sticky top-0 z-10': isMobileOrTablet,
          })}
        >
          <TabsList
            ref={tabsListRef}
            className={cn(
              'w-full justify-start gap-3 transition-all',
              // Hide scrollbar but keep functionality
              'scrollbar-hide overflow-x-auto',
              // Desktop overflow behavior
              'md:overflow-hidden',
              {
                'bg-background/80 shadow-xs backdrop-blur-sm':
                  !isSticky && isMobileOrTablet,
                'bg-transparent': !isSticky || !isMobileOrTablet,
              },
            )}
            onScroll={handleScroll}
          >
            {TAB_ITEMS.map((item) => (
              <TabsTrigger
                key={item.value}
                className="flex-grow-0"
                size="md"
                value={item.value}
              >
                {item.label}
              </TabsTrigger>
            ))}
          </TabsList>
          {/* Left mask for mobile - only show when not at start */}
          {isMobileOrTablet && showLeftMask && (
            <div className="from-background pointer-events-none absolute top-0 left-0 z-20 h-full w-6 bg-gradient-to-r to-transparent" />
          )}

          {/* Right mask for mobile - only show when not at end */}
          {isMobileOrTablet && showRightMask && (
            <div className="from-background pointer-events-none absolute top-0 right-0 z-20 h-full w-6 bg-gradient-to-l to-transparent" />
          )}
        </div>
        <TabsContent value="replies">
          <CommentList comments={COMMENT_MOCK_DATA} />
        </TabsContent>
        <TabsContent value="coin-info">
          <CoinInfo token={token} />
        </TabsContent>
        <TabsContent value="activity">
          <ActivityTable address={address} />
        </TabsContent>
        <TabsContent value="top-holders">
          <TopHoldersTable address={address} />
        </TabsContent>
      </Tabs>
    </section>
  );
};
