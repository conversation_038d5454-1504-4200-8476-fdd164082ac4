// eslint-disable-next-line simple-import-sort/imports
import { useFormatter, useTranslations } from 'next-intl';
import Image from 'next/image';
import type { ComponentPropsWithoutRef } from 'react';

import { Address } from '@/components/address';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/utils/classnames';
import { formatMarketCap } from '@/utils/currency';
import { capitalizeFirst } from '@/utils/format';

interface FeaturedCoinCardProps extends ComponentPropsWithoutRef<'div'> {
  id: string;
  title: string;
  currency: string;
  imageUrl: string;
  address: string;
  createdAt: string;
  marketCap: number;
  circulatingSupply: number;
  totalSupply: number;
}

export const FeaturedCoinCard = ({
  title,
  currency,
  imageUrl,
  address,
  createdAt,
  marketCap,
  circulatingSupply,
  totalSupply,
  className,
  ...props
}: FeaturedCoinCardProps) => {
  const t = useTranslations('home.sections.featured-coins.card');
  const format = useFormatter();
  const now = new Date();

  return (
    <div
      className={cn(
        'group flex flex-col gap-4 rounded-2xl p-3.5 lg:flex-row',
        'border-foreground/50 bg-background cursor-pointer border transition',
        'h-auto w-full lg:h-[178px] lg:min-w-[350px]',
        // 'hover:bg-primary/5 hover:border-primary/60 hover:scale-105 hover:shadow-xl',
        className,
      )}
      {...props}
    >
      <div className="relative h-48 w-full lg:h-[152px] lg:w-[152px]">
        <Image
          fill
          alt={`${title} picture`}
          blurDataURL="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIj48Y2lyY2xlIGN4PSI1MDAiIGN5PSI1MDAiIHI9IjUwMCIgZmlsbD0iI2ZmZmZmZiIgLz48L3N2Zz4="
          className="rounded-md object-cover transition duration-300 ease-in-out group-hover:scale-105"
          sizes="(max-width: 768px) 100vw, (min-width: 769px) 152px"
          src={imageUrl}
        />
      </div>
      <div className="flex flex-1 flex-col justify-between gap-3">
        <div className="flex flex-col">
          <h3 className="text-2xl font-semibold">{currency}</h3>
          <Address address={address} />
          <p className="text-muted-foreground mt-1 text-xs">
            {capitalizeFirst(format.relativeTime(new Date(createdAt), now))}
          </p>
        </div>
        <div className="flex flex-col gap-1">
          <p className="text-primary text-sm font-medium">
            {t('market-cap')}: {formatMarketCap(marketCap)}
          </p>
          <Progress value={(circulatingSupply / totalSupply) * 100} />
        </div>
      </div>
    </div>
  );
};
