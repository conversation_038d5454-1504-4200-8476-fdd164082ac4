import type { ComponentPropsWithoutRef } from 'react';

import { Logo } from '@/components/logo';
import { HeroCard } from '@/features/home/<USER>/hero-card';
import { TOKEN_MOCK_DATA } from '@/features/home/<USER>/home-mock-data';
import { cn } from '@/utils/classnames';

interface HeroProps extends ComponentPropsWithoutRef<'section'> {}

export const HeroSection = ({ className, ...props }: HeroProps) => {
  return (
    <section
      className={cn(
        'relative mt-20 flex w-full max-w-[1400px] flex-col items-center justify-center py-10 sm:mt-6 md:mt-24 lg:py-20',
        className,
      )}
      {...props}
    >
      <div className="relative w-full">
        {/* Background */}
        <div className="absolute inset-0 flex w-full flex-col overflow-hidden bg-[linear-gradient(0deg,#FFD302_0%,#E89607_100%)] md:rounded-2xl">
          {[...Array(7)].map((_, rowIdx) => (
            <div
              key={rowIdx}
              className="text-background flex items-center justify-center gap-6 p-4 opacity-20"
            >
              {Array.from({ length: 12 }).map((_, index) => (
                <Logo key={index} disabled showText={false} size={160} />
              ))}
            </div>
          ))}
        </div>
        {/* Cards */}
        <div className="flex w-full flex-col items-center justify-center gap-10 bg-transparent lg:flex-row lg:gap-12">
          {TOKEN_MOCK_DATA.slice(0, 3).map((item) => (
            <HeroCard
              key={item.id}
              address={item.address}
              className="mb-0 lg:mb-4"
              currency={item.symbol}
              emoji={item.emoji}
              id={item.id}
              imageUrl={item.imageUrl}
              title={item.name}
            />
          ))}
        </div>
      </div>
    </section>
  );
};
