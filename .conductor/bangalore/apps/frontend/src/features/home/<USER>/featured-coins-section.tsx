import { useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';

import { SectionTitle } from '@/components/section-title';
import { FeaturedCoinCard } from '@/features/home/<USER>/featured-coin-card';
import { TOKEN_MOCK_DATA } from '@/features/home/<USER>/home-mock-data';
import { cn } from '@/utils/classnames';

interface FeaturedCoinsSectionProps
  extends ComponentPropsWithoutRef<'section'> {}

export const FeaturedCoinsSection = ({
  className,
  ...props
}: FeaturedCoinsSectionProps) => {
  const t = useTranslations('home.sections.featured-coins');

  return (
    <section
      className={cn(
        'container flex w-full max-w-[1400px] flex-col items-center px-4 py-10 md:mx-auto md:py-12',
        className,
      )}
      {...props}
    >
      <SectionTitle subtitle={t('description')} title={t('title')} />
      <div className="mt-2 grid w-full grid-flow-row grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-[repeat(auto-fill,_minmax(350px,_1fr))]">
        {TOKEN_MOCK_DATA.map((coin) => (
          <FeaturedCoinCard
            key={coin.id}
            address={coin.address}
            circulatingSupply={coin.circulatingSupply}
            createdAt={coin.createdAt}
            currency={coin.symbol}
            id={coin.id}
            imageUrl={coin.imageUrl}
            marketCap={coin.marketCap}
            title={coin.name}
            totalSupply={coin.totalSupply}
          />
        ))}
      </div>
    </section>
  );
};
