'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { SearchIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';
import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Form, FormField } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { FILTER_ITEMS } from '@/features/home/<USER>/filter-data';
import { useResponsive } from '@/hooks/use-responsive';
import { cn } from '@/utils/classnames';

interface FilterSectionProps extends ComponentPropsWithoutRef<'form'> {}

export const FilterSection = ({ className, ...props }: FilterSectionProps) => {
  const t = useTranslations('home.sections.featured-coins.filters');
  const { isMobileOrTablet } = useResponsive();

  const FILTERED_ITEMS = useMemo(() => FILTER_ITEMS(t), [t]);
  const filterSchema = useMemo(
    () =>
      z.object({
        search: z.string().min(2).max(100).optional(),
        filter: z.enum(FILTERED_ITEMS.map((item) => item.value)).optional(),
      }),
    [FILTERED_ITEMS],
  );

  const form = useForm<z.infer<typeof filterSchema>>({
    resolver: zodResolver(filterSchema),
    defaultValues: {
      search: '',
      filter: FILTERED_ITEMS[0].value,
    },
  });

  const onSubmit = (data: z.infer<typeof filterSchema>) => {
    // Handle form submission logic here
    console.log('Filter data submitted:', data);
  };

  const onValueChange = (value: string) => {
    form.setValue('filter', value);
  };

  return (
    <Form {...form}>
      <form
        {...props}
        className={cn(
          'flex w-full flex-col items-center justify-center gap-4 px-4 md:px-0 lg:flex-row',
          className,
        )}
        onSubmit={form.handleSubmit(onSubmit)}
      >
        {/* Left side */}
        <FormField
          control={form.control}
          name="search"
          render={({ field }) => (
            <Input
              {...field}
              className="focus-visible:border-yellow focus-visible:ring-yellow/50 h-10 w-full bg-transparent lg:w-64"
              fullWidth={isMobileOrTablet}
              leftContent={<SearchIcon />}
              placeholder={t('search-placeholder')}
            />
          )}
        />
        {/* Right side */}
        <FormField
          control={form.control}
          name="filter"
          render={({ field }) => (
            <ToggleGroup
              {...field}
              className="flex-wrap justify-center gap-2"
              size="lg"
              type="single"
              value={field.value}
              variant="yellow"
              onValueChange={onValueChange}
            >
              {FILTERED_ITEMS.map((item) => (
                <ToggleGroupItem key={item.value} value={item.value}>
                  {item.icon}
                  {item.label}
                </ToggleGroupItem>
              ))}
            </ToggleGroup>
          )}
        />
      </form>
    </Form>
  );
};
