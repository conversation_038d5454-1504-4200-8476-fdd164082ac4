import { GraduationCapIcon } from 'lucide-react';
import type { useTranslations } from 'next-intl';

export const FILTER_ITEMS = (t: ReturnType<typeof useTranslations>) => [
  {
    value: 'last-trade',
    label: t('last-trade'),
  },
  {
    value: 'creation-time',
    label: t('creation-time'),
  },
  {
    value: 'heading-up',
    label: t('heading-up'),
  },
  {
    value: 'watchlist',
    label: t('watchlist'),
  },
  {
    value: 'all-tokens',
    label: t('all-tokens'),
    icon: <GraduationCapIcon />,
  },
];
