'use client';

import { CopyIcon, ExternalLinkIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/utils/classnames';
import { formatAddress } from '@/utils/format';

interface AddressProps extends ComponentPropsWithoutRef<'div'> {
  address: string;
  maxFirstChars?: number;
  maxLastChars?: number;
  truncate?: boolean;
  truncateText?: string;
  showLastChars?: boolean;
  enableCopy?: boolean;
  redirectUrl?: string;
  redirectText?: string;
}

export const Address = ({
  address,
  maxFirstChars = 6,
  maxLastChars = 4,
  truncate = true,
  truncateText = '...',
  enableCopy = true,
  showLastChars = true,
  redirectUrl,
  redirectText = 'View on explorer',
  className,
  ...props
}: AddressProps) => {
  const [isCopied, setIsCopied] = useState(false);
  const [isCopyHovered, setIsCopyHovered] = useState(false);

  const t = useTranslations('components.address');

  const displayText = formatAddress(address, {
    maxFirstChars,
    maxLastChars,
    truncate,
    truncateText,
    showLastChars,
  });

  const handleCopy = () => {
    // Ensure copying the full address, not the truncated displayText
    navigator.clipboard
      .writeText(address)
      .then(() => {
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000); // Reset copied state after 2 seconds
      })
      .catch((err) => {
        console.error('Failed to copy address:', err);
        setIsCopied(false);
      });
  };

  const handleRedirect = () => {
    if (redirectUrl) {
      window.open(redirectUrl, '_blank', 'noopener,noreferrer');
    }
  };

  return (
    <div
      className={cn('flex items-center gap-1 text-sm font-medium', className)}
      {...props}
    >
      {displayText}
      {enableCopy && (
        <Tooltip delayDuration={0} open={isCopyHovered || isCopied}>
          <TooltipTrigger asChild>
            <Button
              aria-label={t('copy-address')}
              size="sm"
              variant="icon"
              onClick={handleCopy}
              onMouseEnter={() => setIsCopyHovered(true)}
              onMouseLeave={() => setIsCopyHovered(false)}
            >
              <CopyIcon className="size-3.5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            {isCopied ? t('copied') : t('copy-address')}
          </TooltipContent>
        </Tooltip>
      )}

      {redirectUrl && (
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              aria-label={t('view-on-explorer')}
              size="sm"
              variant="icon"
              onClick={handleRedirect}
            >
              <ExternalLinkIcon className="size-3.5" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>{redirectText}</TooltipContent>
        </Tooltip>
      )}
    </div>
  );
};
