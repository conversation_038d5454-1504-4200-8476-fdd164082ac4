'use client';

// eslint-disable-next-line simple-import-sort/imports
import type { AxiosProgressEvent } from 'axios';
import { CameraIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'sonner';

import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { uploadImageToImgBb } from '@/libs/api';
import { cn } from '@/utils/classnames';

interface ImageUploadProps {
  value?: File;
  onChange?: (file: File | undefined) => void;
  onUploadComplete?: (url: string) => void;
}

export const ImageUpload = ({
  onChange,
  onUploadComplete,
}: ImageUploadProps) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [uploadedImagePath, setUploadedImagePath] = useState<string | null>(
    null,
  );

  const t = useTranslations('components.image-upload');

  const onUploadProgress = (progressEvent: AxiosProgressEvent) => {
    if (progressEvent.total) {
      const percentage = Math.round(
        (progressEvent.loaded * 100) / progressEvent.total,
      );
      setProgress(percentage);
    }
  };

  const handleImageChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (event.target.files?.length) {
      const image = event.target.files[0];
      onChange?.(image);
      await handleImageUpload(image);
    }
  };

  const handleImageUpload = useCallback(
    async (image: File) => {
      setLoading(true);
      setProgress(0);

      try {
        const res = await uploadImageToImgBb(image, onUploadProgress);
        if (res.status === 200) {
          setLoading(false);
          setUploadedImagePath(res.data.data.url);
          if (onUploadComplete) {
            onUploadComplete(res.data.data.url);
          }
        }
      } catch (error) {
        setLoading(false);
        console.error('Error uploading image:', error);
        toast.error(t('upload-error'), {
          description:
            error instanceof Error ? error.message : t('unknown-error'),
        });
      }
    },
    [onUploadComplete, t],
  );

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0) {
        const image = acceptedFiles[0];
        onChange?.(image);
        await handleImageUpload(image);
      }
    },
    [handleImageUpload, onChange],
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    noClick: true,
  });

  return (
    <div {...getRootProps()} className="h-full">
      <label
        className={cn(
          'visually-hidden-focusable relative flex w-full cursor-pointer flex-col items-center justify-center',
        )}
        htmlFor="dropzone-file"
      >
        {loading && (
          <div className="flex max-w-md flex-col items-center text-center">
            <div className="flex h-24 w-1/2 items-center justify-center">
              <Progress className="my-2" value={progress} />
            </div>
            <p className="mt-2 text-sm font-semibold">
              {t('uploading-picture.title')}
            </p>
            <p className="text-muted-foreground text-xs">
              {t('uploading-picture.description')}
            </p>
          </div>
        )}

        {!loading && !uploadedImagePath && (
          <div className="text-center">
            <div className="bg-card mx-auto flex size-24 items-center justify-center rounded-full">
              <CameraIcon className="size-8" />
            </div>

            <p className="mt-2 text-sm">
              <span className="font-semibold">{t('form.title')}</span>
            </p>
            <p className="text-muted-foreground text-xs">
              {t('form.description')}
            </p>
          </div>
        )}

        {uploadedImagePath && !loading && (
          <div className="text-center">
            <div className="relative mx-auto size-24 rounded-full">
              <Image
                fill
                alt="uploaded image"
                className="rounded-full object-cover"
                sizes="(max-width: 24px) 24px, (max-width: 48px) 48px, 96px"
                src={uploadedImagePath}
              />
            </div>
            <p className="mt-2 text-sm font-semibold">
              {t('image-uploaded.title')}
            </p>
            <p className="text-muted-foreground text-xs">
              {t('image-uploaded.description')}
            </p>
          </div>
        )}
      </label>

      <Input
        {...getInputProps()}
        accept="image/png, image/jpeg, image/gif, image/webp"
        className="hidden"
        disabled={loading}
        id="dropzone-file"
        type="file"
        onChange={handleImageChange}
      />
    </div>
  );
};
