'use client';

import { RocketIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';

import { LaunchTokenDialog } from '@/components/dialogs/launch-token-dialog';
import type { ButtonProps } from '@/components/ui/button';
import { Button } from '@/components/ui/button';

type LaunchTokenButtonProps = ButtonProps;

export const LaunchTokenButton = ({ ...props }: LaunchTokenButtonProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const t = useTranslations('components.launch-token');

  return (
    <>
      <Button size="sm" {...props} onClick={() => setIsDialogOpen(true)}>
        <RocketIcon /> <span className="hidden md:inline">{t('label')}</span>
      </Button>
      <LaunchTokenDialog open={isDialogOpen} onOpenChange={setIsDialogOpen} />
    </>
  );
};
