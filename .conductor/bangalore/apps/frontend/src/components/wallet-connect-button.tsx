'use client';

import '@mysten/dapp-kit/dist/index.css';

import {
  ConnectModal,
  useCurrentAccount,
  useDisconnectWallet,
} from '@mysten/dapp-kit';
import { CopyIcon, UnplugIcon, WalletIcon } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState } from 'react';
import { toast } from 'sonner';

import type { ButtonProps } from '@/components/ui/button';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useResponsive } from '@/hooks/use-responsive';
import { formatAddress } from '@/utils/format';

type WalletConnectButtonProps = ButtonProps;

export const WalletConnectButton = ({ ...props }: WalletConnectButtonProps) => {
  const [open, setOpen] = useState(false);

  const t = useTranslations('header.menu');
  const currentAccount = useCurrentAccount();
  const { mutate: disconnect } = useDisconnectWallet();
  const { isMobile } = useResponsive();

  const handleConnect = () => {
    if (!currentAccount) {
      setOpen(true);
    }
  };

  const handleDisconnect = () => {
    if (currentAccount) {
      disconnect();
    }
    setOpen(false);
  };

  const handleCopyAddress = async () => {
    if (currentAccount) {
      await navigator.clipboard.writeText(currentAccount.address);
      toast.success(t('wallet.address-copied'));
    }
  };

  return currentAccount ? (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className="max-h-8 text-sm md:max-h-9">
        <Button {...props} size="sm">
          {isMobile ? <UnplugIcon /> : <WalletIcon />}
          <span className="hidden md:block">
            {formatAddress(currentAccount.address)}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuItem onClick={handleCopyAddress}>
          <CopyIcon />
          {t('wallet.copy-address')}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleDisconnect}>
          <UnplugIcon />
          {t('wallet.disconnect')}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  ) : (
    <ConnectModal
      open={open}
      trigger={
        <Button {...props} size="sm" onClick={handleConnect}>
          <WalletIcon />
          <span className="hidden md:block">{t('connect-wallet')}</span>
        </Button>
      }
      onOpenChange={(isOpen) => setOpen(isOpen)}
    />
  );
};
