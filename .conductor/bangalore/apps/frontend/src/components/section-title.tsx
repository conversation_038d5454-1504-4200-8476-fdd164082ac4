import type { ComponentPropsWithoutRef } from 'react';

import { cn } from '@/utils/classnames';

interface SectionTitleProps extends ComponentPropsWithoutRef<'div'> {
  title: string;
  subtitle?: string;
}

export const SectionTitle = ({
  title,
  subtitle,
  className,
  ...props
}: SectionTitleProps) => {
  return (
    <div className={cn('mb-4 flex w-full flex-col', className)} {...props}>
      <h2 className={cn('text-3xl leading-none font-bold tracking-tight')}>
        {title}
      </h2>
      {subtitle && (
        <p className="text-muted-foreground mt-1 text-base">{subtitle}</p>
      )}
    </div>
  );
};
