import type { ComponentPropsWithoutRef } from 'react';

import { cn } from '@/utils/classnames';

interface BackgroundProps extends ComponentPropsWithoutRef<'div'> {}

export const Background = ({ className, ...props }: BackgroundProps) => {
  return (
    <div
      className={cn(
        'pointer-events-none absolute inset-0 z-0 max-h-screen',
        className,
      )}
      style={{
        backgroundImage: `linear-gradient(to right, rgba(71,85,105,0.3) 1px, transparent 1px),linear-gradient(to bottom, rgba(71,85,105,0.3) 1px, transparent 1px)`,
        backgroundSize: '20px 30px',
        WebkitMaskImage:
          'radial-gradient(ellipse 70% 60% at 50% 0%, #1b1b1b 60%, transparent 100%)',
        maskImage:
          'radial-gradient(ellipse 70% 60% at 50% 0%, #1b1b1b 60%, transparent 100%)',
      }}
      {...props}
    />
  );
};
