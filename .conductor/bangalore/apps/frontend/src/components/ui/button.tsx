import Link from 'next/link';
import type { Ref } from 'react';
import { forwardRef } from 'react';
import type { VariantProps } from 'tailwind-variants';
import { tv } from 'tailwind-variants';

import { cn } from '@/utils/classnames';

export const buttonVariants = tv({
  base: [
    'inline-flex shrink-0 items-center justify-center gap-2 transition-all outline-none',
    'leading-none font-semibold whitespace-nowrap',
    'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
    'aria-invalid:border-destructive aria-invalid:ring-destructive/20',
    'disabled:pointer-events-none disabled:opacity-50',
    "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
  ],
  variants: {
    variant: {
      default: 'bg-primary text-primary-foreground hover:bg-primary/90',
      destructive:
        'bg-destructive hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60 text-white shadow-xs',
      outline:
        'bg-background hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 border shadow-xs',
      secondary:
        'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-xs',
      tertiary: 'bg-card text-muted-foreground hover:bg-card/80',
      ghost: 'hover:bg-accent hover:text-accent-foreground',
      link: 'border-foreground cursor-pointer border-b-[1px] border-dashed',
      icon: 'hover:text-foreground/80 text-foreground bg-transparent',
    },
    size: {
      default: 'h-10 rounded-md px-4 py-2 text-base has-[>svg]:px-3',
      xs: 'h-6 rounded-sm px-2 text-xs has-[>svg]:px-1',
      sm: 'rounded-sm px-3 py-2 text-sm has-[>svg]:px-3',
      lg: 'h-11 rounded-md px-6 text-base has-[>svg]:px-4',
    },
  },
  compoundVariants: [
    {
      variant: 'link',
      class: 'h-min p-0 text-xs has-[>svg]:px-0',
    },
    {
      variant: 'icon',
      class: 'h-min p-0 has-[>svg]:px-0',
    },
  ],
  defaultVariants: {
    variant: 'default',
    size: 'default',
  },
});

interface AnchorProps extends React.ComponentPropsWithRef<'a'> {
  as?: 'a';
  external?: boolean;
  href: string;
  disabled?: boolean;
}

interface BaseButtonProps extends React.ComponentPropsWithRef<'button'> {
  as?: 'button';
}

export type ButtonVariants = VariantProps<typeof buttonVariants>;

export type ButtonProps = (AnchorProps | BaseButtonProps) & ButtonVariants;

const Button = forwardRef<HTMLAnchorElement | HTMLButtonElement, ButtonProps>(
  ({ children, className, variant, size, ...props }, ref) => {
    const styles = cn(buttonVariants({ variant, size, class: className }));
    // Keep button as as=button if disabled to allow onClick override
    if (props.as === 'a' && !props.disabled) {
      const { external, href, ...baseLinkProps } = props;

      // External link
      if (external) {
        const externalLinkProps = {
          target: '_blank',
          rel: 'noopener',
          href,
          ...baseLinkProps,
        };
        return (
          <a
            {...externalLinkProps}
            ref={ref as Ref<HTMLAnchorElement>}
            className={styles}
          >
            {children}
          </a>
        );
      }

      return (
        <Link
          {...baseLinkProps}
          ref={ref as Ref<HTMLAnchorElement>}
          className={styles}
          href={href}
        >
          {children}
        </Link>
      );
    } else {
      const { ...buttonProps } = props;
      return (
        <button
          {...(buttonProps as BaseButtonProps)}
          ref={ref as Ref<HTMLButtonElement>}
          className={styles}
        >
          {children}
        </button>
      );
    }
  },
);

Button.displayName = 'Button';

export { Button };
