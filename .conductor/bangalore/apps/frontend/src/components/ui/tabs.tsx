'use client';

import * as TabsPrimitive from '@radix-ui/react-tabs';
import type { ComponentProps } from 'react';
import type { VariantProps } from 'tailwind-variants';
import { tv } from 'tailwind-variants';

import { cn } from '@/utils/classnames';

const tabsVariants = tv({
  base: [
    'inline-flex flex-1 items-center justify-center',
    'h-[calc(100%-1px)] font-medium whitespace-nowrap',
    'border border-transparent transition-[color,box-shadow]',
    'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring focus-visible:ring-[3px] focus-visible:outline-1',
    'data-[state=active]:bg-background',
    'disabled:pointer-events-none disabled:opacity-50',
    "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
  ],
  variants: {
    variant: {
      default: [
        'bg-secondary text-secondary-foreground',
        'hover:text-secondary-foreground hover:bg-secondary/40',
        'data-[state=active]:bg-primary data-[state=active]:text-primary-foreground',
      ],
      buy: [
        'bg-tertiary text-tertiary-foreground',
        'hover:bg-tertiary/80',
        'data-[state=active]:bg-emerald-400 data-[state=active]:text-emerald-50',
      ],
      sell: [
        'bg-tertiary text-tertiary-foreground',
        'hover:bg-tertiary/80',
        'data-[state=active]:bg-red-400 data-[state=active]:text-red-50',
      ],
    },
    size: {
      default: 'gap-1.5 rounded-md px-2 py-1 text-sm',
      md: 'h-[30px] gap-1.5 rounded-md px-4 py-2 text-base',
      lg: 'h-10 rounded-md px-6 text-base font-medium has-[>svg]:px-4',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'default',
  },
});

export type TabsVariants = VariantProps<typeof tabsVariants>;

function Tabs({
  className,
  ...props
}: ComponentProps<typeof TabsPrimitive.Root>) {
  return (
    <TabsPrimitive.Root
      className={cn('flex flex-col gap-2', className)}
      data-slot="tabs"
      {...props}
    />
  );
}

function TabsList({
  className,
  ...props
}: ComponentProps<typeof TabsPrimitive.List>) {
  return (
    <TabsPrimitive.List
      className={cn(
        'bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]',
        className,
      )}
      data-slot="tabs-list"
      {...props}
    />
  );
}

type TabsTriggerProps = ComponentProps<typeof TabsPrimitive.Trigger> &
  TabsVariants;

function TabsTrigger({ className, variant, size, ...props }: TabsTriggerProps) {
  return (
    <TabsPrimitive.Trigger
      className={cn(tabsVariants({ variant, size, className }))}
      data-slot="tabs-trigger"
      {...props}
    />
  );
}

function TabsContent({
  className,
  ...props
}: ComponentProps<typeof TabsPrimitive.Content>) {
  return (
    <TabsPrimitive.Content
      className={cn('flex-1 outline-none', className)}
      data-slot="tabs-content"
      {...props}
    />
  );
}

export { Tabs, TabsContent, TabsList, TabsTrigger };
