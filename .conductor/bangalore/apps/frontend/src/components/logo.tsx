import type { LinkProps } from 'next/link';
import Link from 'next/link';

import { LogoIcon } from '@/assets/icons';

interface LogoProps extends Omit<LinkProps, 'href'> {
  showText?: boolean;
  disabled?: boolean;
  size?: number;
}

export const Logo = ({
  showText = true,
  size = 20,
  disabled = false,
  ...props
}: LogoProps) => {
  const content = (
    <div className="flex items-center gap-2.5">
      <LogoIcon height={size} width={size} />
      {showText && <span className="text-2xl font-bold">hop.fun</span>}
    </div>
  );

  return disabled ? (
    content
  ) : (
    <Link {...props} aria-disabled={disabled} href="/">
      {content}
    </Link>
  );
};
