'use client';

import { ChevronDownIcon } from 'lucide-react';
import { useLocale, useTranslations } from 'next-intl';
import type { ComponentPropsWithoutRef } from 'react';
import { useMemo, useTransition } from 'react';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Locale } from '@/libs/i18n/config';
import { setUserLocale } from '@/services/locale';

interface LanguageSwitcherProps extends ComponentPropsWithoutRef<'button'> {}

export const LanguageSwitcher = ({
  className,
  ...props
}: LanguageSwitcherProps) => {
  const t = useTranslations('header.languages');
  const locale = useLocale();

  const [isPending, startTransition] = useTransition();

  const LANGUAGES = useMemo(
    () => [
      { code: 'en', label: t('en'), icon: <span className="fi fi-us" /> },
      { code: 'de', label: t('de'), icon: <span className="fi fi-de" /> },
    ],
    [t],
  );

  const handleChangeLocale = (newLocale: string) => {
    startTransition(async () => {
      await setUserLocale(newLocale as Locale);
    });
  };

  const renderFlag = useMemo(() => {
    switch (locale) {
      case 'en':
        return <span className="fi fi-us" />;
      case 'de':
        return <span className="fi fi-de" />;
      default:
        return <span className="fi fi-us" />;
    }
  }, [locale]);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger
        className={className}
        disabled={isPending}
        size="sm"
        variant="ghost"
        {...props}
      >
        {renderFlag}
        <ChevronDownIcon />
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuRadioGroup
          value={locale}
          onValueChange={handleChangeLocale}
        >
          {LANGUAGES.map(({ code, label, icon }) => (
            <DropdownMenuRadioItem key={code} value={code}>
              {icon}
              {label}
            </DropdownMenuRadioItem>
          ))}
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
