export function capitalizeFirst(str: string) {
  if (!str) return '';
  return str[0].toUpperCase() + str.slice(1);
}

export function formatNumber(value: number, decimals = 2): string {
  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  }).format(value);
}

export const formatDynamicNumber = (value: number, maxDecimals = 6) => {
  // Convert to number if it's a string
  const numValue = typeof value === 'string' ? parseFloat(value) : value;

  // For very small numbers (< 0.001), show more precision
  if (numValue < 0.001 && numValue > 0) {
    const formatted = numValue.toFixed(9);
    // Remove trailing zeros
    return parseFloat(formatted).toString();
  }

  // For small numbers (< 1), show up to 6 decimal places
  if (numValue < 1 && numValue >= 0.001) {
    const formatted = numValue.toFixed(maxDecimals);
    // Remove trailing zeros
    return parseFloat(formatted).toString();
  }

  // For larger numbers (>= 1), show up to 3 decimal places
  if (numValue >= 1) {
    const formatted = numValue.toFixed(3);
    // Remove trailing zeros
    return parseFloat(formatted).toString();
  }

  // Default fallback
  return numValue.toString();
};

export interface FormatAddressOptions {
  maxFirstChars?: number;
  maxLastChars?: number;
  truncate?: boolean;
  truncateText?: string;
  showLastChars?: boolean;
}

export const formatAddress = (
  address: string,
  options: FormatAddressOptions = {},
): string => {
  const {
    maxFirstChars = 6,
    maxLastChars = 4,
    truncate = true,
    truncateText = '...',
    showLastChars = true,
  } = options;

  if (!truncate) {
    return address;
  }

  const firstPart = address.slice(0, maxFirstChars);

  if (!showLastChars) {
    return `${firstPart}${truncateText}`;
  }

  const lastPart = address.slice(-maxLastChars);
  return `${firstPart}${truncateText}${lastPart}`;
};
