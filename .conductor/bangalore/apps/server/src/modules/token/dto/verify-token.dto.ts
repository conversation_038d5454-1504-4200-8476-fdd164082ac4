import { z } from 'zod';

export const VerifyTokenRequestSchema = z.object({
  curveId: z.string().min(1, 'Curve ID is required'),
  creator: z
    .string()
    .min(1, 'Creator address is required')
    .regex(/^0x[a-fA-F0-9]{64}$/, 'Invalid SUI address format')
    .optional(),
  ticker: z.string().min(1).max(10, 'Ticker must be between 1 and 10 characters').optional(),
});

export const VerifyTokenExistenceRequestSchema = z.object({
  curveId: z.string().min(1, 'Curve ID is required'),
});

export type VerifyTokenRequest = z.infer<typeof VerifyTokenRequestSchema>;
export type VerifyTokenExistenceRequest = z.infer<typeof VerifyTokenExistenceRequestSchema>;

export interface TokenVerificationResult {
  exists: boolean;
  isValid: boolean;
  token?: {
    id: string;
    curveId: string;
    creator: string;
    coinName: string;
    ticker: string;
    status: string;
    network: string;
    createdAt: string;
  };
  validationErrors?: string[];
}

export interface VerifyTokenResponseDto {
  success: boolean;
  data?: TokenVerificationResult;
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}

export interface VerifyTokenExistenceResponseDto {
  success: boolean;
  data?: {
    exists: boolean;
    curveId: string;
    token?: {
      id: string;
      status: string;
      createdAt: string;
    };
  };
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}
