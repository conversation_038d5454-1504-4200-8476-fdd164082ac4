import { z } from 'zod';

export const GetTokenByIdRequestSchema = z.object({
  id: z.string().min(1, 'Token ID is required'),
});

export const GetTokenByCurveIdRequestSchema = z.object({
  curveId: z.string().min(1, 'Curve ID is required'),
});

export const GetTokensByCreatorRequestSchema = z.object({
  creator: z
    .string()
    .min(1, 'Creator address is required')
    .regex(/^0x[a-fA-F0-9]{64}$/, 'Invalid SUI address format'),
});

export const ListTokensRequestSchema = z.object({
  page: z.coerce.number().min(1, 'Page must be at least 1').default(1),
  limit: z.coerce
    .number()
    .min(1)
    .max(100, 'Limit must be between 1 and 100')
    .default(20),
  network: z.enum(['MAINNET', 'TESTNET']).optional(),
  status: z.enum(['ACTIVE', 'COMPLETED', 'MIGRATED']).optional(),
  sortBy: z
    .enum(['createdAt', 'marketCap', 'currentPrice'])
    .default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export type GetTokenByIdRequest = z.infer<typeof GetTokenByIdRequestSchema>;
export type GetTokenByCurveIdRequest = z.infer<
  typeof GetTokenByCurveIdRequestSchema
>;
export type GetTokensByCreatorRequest = z.infer<
  typeof GetTokensByCreatorRequestSchema
>;
export type ListTokensRequest = z.infer<typeof ListTokensRequestSchema>;

export interface TokenResponseDto {
  id: string;
  curveId: string;
  creator: string;
  coinName: string;
  ticker: string;
  description: string;
  imageUrl?: string;
  twitter: string;
  website: string;
  telegram: string;
  totalSupply: string;
  network: string;
  status: string;
  currentPrice: string;
  marketCap: string;
  virtualSuiAmount: string;
  suiBalance: string;
  tokenBalance: string;
  availableTokenReserves: string;
  poolId?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  migratedAt?: string;
}

export interface GetTokenResponseDto {
  success: boolean;
  data?: TokenResponseDto;
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}

export interface ListTokensResponseDto {
  success: boolean;
  data?: {
    tokens: TokenResponseDto[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}
