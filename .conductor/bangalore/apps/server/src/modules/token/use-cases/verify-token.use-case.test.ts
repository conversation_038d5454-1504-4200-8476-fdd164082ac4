import type { Token } from '@hopfun/database';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import type { ITokenService } from '../interfaces/token.interfaces';
import { VerifyTokenUseCase } from './verify-token.use-case';

// Mock the logger
vi.mock('@hopfun/logger', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

describe('VerifyTokenUseCase', () => {
  let verifyTokenUseCase: VerifyTokenUseCase;
  let mockTokenService: ITokenService;

  const mockToken: Token = {
    id: 'token-id-1',
    curveId: 'curve-id-1',
    creator: '0x' + 'a'.repeat(64),
    coinName: 'Test Token',
    ticker: 'TEST',
    description: 'A test token',
    imageUrl: 'https://example.com/image.png',
    twitter: '@testtoken',
    website: 'https://testtoken.com',
    telegram: '@testtoken',
    totalSupply: '1000000000000000',
    network: 'TESTNET',
    status: 'ACTIVE',
    currentPrice: '0.001',
    marketCap: '1000000',
    virtualSuiAmount: '500000',
    suiBalance: '250000',
    tokenBalance: '750000000000000',
    availableTokenReserves: '250000000000000',
    poolId: null,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    completedAt: null,
    migratedAt: null,
  };

  beforeEach(() => {
    mockTokenService = {
      getTokenById: vi.fn(),
      getTokenByCurveId: vi.fn(),
      getTokensByCreator: vi.fn(),
      listTokens: vi.fn(),
      verifyTokenExists: vi.fn(),
      verifyToken: vi.fn(),
    };

    verifyTokenUseCase = new VerifyTokenUseCase(mockTokenService);
    vi.clearAllMocks();
  });

  describe('verifyToken', () => {
    it('should return success response when token verification succeeds', async () => {
      const mockVerificationResult = {
        exists: true,
        isValid: true,
        token: {
          id: mockToken.id,
          curveId: mockToken.curveId,
          creator: mockToken.creator,
          coinName: mockToken.coinName,
          ticker: mockToken.ticker,
          status: mockToken.status,
          network: mockToken.network,
          createdAt: mockToken.createdAt.toISOString(),
        },
      };

      vi.mocked(mockTokenService.verifyToken).mockResolvedValue(
        mockVerificationResult,
      );

      const params = {
        curveId: 'curve-id-1',
        creator: '0x' + 'a'.repeat(64),
        ticker: 'TEST',
      };

      const result = await verifyTokenUseCase.verifyToken(params);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockVerificationResult);
      expect(mockTokenService.verifyToken).toHaveBeenCalledWith(params);
    });

    it('should return success response when token exists but is invalid', async () => {
      const mockVerificationResult = {
        exists: true,
        isValid: false,
        token: {
          id: mockToken.id,
          curveId: mockToken.curveId,
          creator: mockToken.creator,
          coinName: mockToken.coinName,
          ticker: mockToken.ticker,
          status: mockToken.status,
          network: mockToken.network,
          createdAt: mockToken.createdAt.toISOString(),
        },
        validationErrors: ['Creator address does not match'],
      };

      vi.mocked(mockTokenService.verifyToken).mockResolvedValue(
        mockVerificationResult,
      );

      const params = {
        curveId: 'curve-id-1',
        creator: '0x' + 'b'.repeat(64), // Different creator
      };

      const result = await verifyTokenUseCase.verifyToken(params);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockVerificationResult);
    });

    it('should return success response when token does not exist', async () => {
      const mockVerificationResult = {
        exists: false,
        isValid: false,
        validationErrors: ['Token does not exist'],
      };

      vi.mocked(mockTokenService.verifyToken).mockResolvedValue(
        mockVerificationResult,
      );

      const params = {
        curveId: 'non-existent-curve-id',
      };

      const result = await verifyTokenUseCase.verifyToken(params);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockVerificationResult);
    });

    it('should return error response when curve ID is empty', async () => {
      const params = {
        curveId: '',
      };

      const result = await verifyTokenUseCase.verifyToken(params);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.message).toBe('Curve ID is required');
    });

    it('should return error response when creator address is invalid', async () => {
      const params = {
        curveId: 'curve-id-1',
        creator: 'invalid-address',
      };

      const result = await verifyTokenUseCase.verifyToken(params);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.message).toBe(
        'Invalid SUI address format for creator',
      );
    });

    it('should return error response when ticker is invalid', async () => {
      const params = {
        curveId: 'curve-id-1',
        ticker: 'TOOLONGTICKERHERE', // Too long
      };

      const result = await verifyTokenUseCase.verifyToken(params);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.message).toBe(
        'Ticker must be between 1 and 10 characters',
      );
    });

    it('should handle service errors', async () => {
      const serviceError = new Error('Database connection failed');
      vi.mocked(mockTokenService.verifyToken).mockRejectedValue(serviceError);

      const params = {
        curveId: 'curve-id-1',
      };

      const result = await verifyTokenUseCase.verifyToken(params);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('DATABASE_ERROR');
      expect(result.error?.message).toBe('Database connection failed');
    });
  });

  describe('verifyTokenExists', () => {
    it('should return success response when token exists', async () => {
      vi.mocked(mockTokenService.verifyTokenExists).mockResolvedValue(true);
      vi.mocked(mockTokenService.getTokenByCurveId).mockResolvedValue(
        mockToken,
      );

      const result = await verifyTokenUseCase.verifyTokenExists('curve-id-1');

      expect(result.success).toBe(true);
      expect(result.data?.exists).toBe(true);
      expect(result.data?.curveId).toBe('curve-id-1');
      expect(result.data?.token).toEqual({
        id: mockToken.id,
        status: mockToken.status,
        createdAt: mockToken.createdAt.toISOString(),
      });
    });

    it('should return success response when token does not exist', async () => {
      vi.mocked(mockTokenService.verifyTokenExists).mockResolvedValue(false);

      const result = await verifyTokenUseCase.verifyTokenExists(
        'non-existent-curve-id',
      );

      expect(result.success).toBe(true);
      expect(result.data?.exists).toBe(false);
      expect(result.data?.curveId).toBe('non-existent-curve-id');
      expect(result.data?.token).toBeUndefined();
    });

    it('should return error response when curve ID is empty', async () => {
      const result = await verifyTokenUseCase.verifyTokenExists('');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.message).toBe('Curve ID is required');
    });

    it('should handle service errors', async () => {
      const serviceError = new Error('Database connection failed');
      vi.mocked(mockTokenService.verifyTokenExists).mockRejectedValue(
        serviceError,
      );

      const result = await verifyTokenUseCase.verifyTokenExists('curve-id-1');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('DATABASE_ERROR');
      expect(result.error?.message).toBe('Database connection failed');
    });

    it('should handle case when token exists but getTokenByCurveId returns null', async () => {
      vi.mocked(mockTokenService.verifyTokenExists).mockResolvedValue(true);
      vi.mocked(mockTokenService.getTokenByCurveId).mockResolvedValue(null);

      const result = await verifyTokenUseCase.verifyTokenExists('curve-id-1');

      expect(result.success).toBe(true);
      expect(result.data?.exists).toBe(true);
      expect(result.data?.token).toBeUndefined();
    });
  });
});
