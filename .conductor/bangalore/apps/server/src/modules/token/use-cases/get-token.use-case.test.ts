import type { Token } from '@hopfun/database';
import { beforeEach, describe, expect, it, vi } from 'vitest';

import type { ITokenService } from '../interfaces/token.interfaces';
import { GetTokenUseCase } from './get-token.use-case';

// Mock the logger
vi.mock('@hopfun/logger', () => ({
  logger: {
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn(),
  },
}));

describe('GetTokenUseCase', () => {
  let getTokenUseCase: GetTokenUseCase;
  let mockTokenService: ITokenService;

  const mockToken: Token = {
    id: 'token-id-1',
    curveId: 'curve-id-1',
    creator: '0x' + 'a'.repeat(64),
    coinName: 'Test Token',
    ticker: 'TEST',
    description: 'A test token',
    imageUrl: 'https://example.com/image.png',
    twitter: '@testtoken',
    website: 'https://testtoken.com',
    telegram: '@testtoken',
    totalSupply: '1000000000000000',
    network: 'TESTNET',
    status: 'ACTIVE',
    currentPrice: '0.001',
    marketCap: '1000000',
    virtualSuiAmount: '500000',
    suiBalance: '250000',
    tokenBalance: '750000000000000',
    availableTokenReserves: '250000000000000',
    poolId: null,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    completedAt: null,
    migratedAt: null,
  };

  beforeEach(() => {
    mockTokenService = {
      getTokenById: vi.fn(),
      getTokenByCurveId: vi.fn(),
      getTokensByCreator: vi.fn(),
      listTokens: vi.fn(),
      verifyTokenExists: vi.fn(),
      verifyToken: vi.fn(),
    };

    getTokenUseCase = new GetTokenUseCase(mockTokenService);
    vi.clearAllMocks();
  });

  describe('getById', () => {
    it('should return success response when token is found', async () => {
      vi.mocked(mockTokenService.getTokenById).mockResolvedValue(mockToken);

      const result = await getTokenUseCase.getById('token-id-1');

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.id).toBe('token-id-1');
      expect(result.data?.ticker).toBe('TEST');
      expect(mockTokenService.getTokenById).toHaveBeenCalledWith('token-id-1');
    });

    it('should return error response when token is not found', async () => {
      vi.mocked(mockTokenService.getTokenById).mockResolvedValue(null);

      const result = await getTokenUseCase.getById('non-existent-id');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('TOKEN_NOT_FOUND');
      expect(result.error?.message).toBe('Token not found');
    });

    it('should return error response when ID is empty', async () => {
      const result = await getTokenUseCase.getById('');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.message).toBe('Token ID is required');
    });

    it('should handle service errors', async () => {
      const serviceError = new Error('Database connection failed');
      vi.mocked(mockTokenService.getTokenById).mockRejectedValue(serviceError);

      const result = await getTokenUseCase.getById('token-id-1');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('DATABASE_ERROR');
      expect(result.error?.message).toBe('Database connection failed');
    });
  });

  describe('getByCurveId', () => {
    it('should return success response when token is found', async () => {
      vi.mocked(mockTokenService.getTokenByCurveId).mockResolvedValue(
        mockToken,
      );

      const result = await getTokenUseCase.getByCurveId('curve-id-1');

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.curveId).toBe('curve-id-1');
      expect(mockTokenService.getTokenByCurveId).toHaveBeenCalledWith(
        'curve-id-1',
      );
    });

    it('should return error response when token is not found', async () => {
      vi.mocked(mockTokenService.getTokenByCurveId).mockResolvedValue(null);

      const result = await getTokenUseCase.getByCurveId(
        'non-existent-curve-id',
      );

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('TOKEN_NOT_FOUND');
    });

    it('should return error response when curve ID is empty', async () => {
      const result = await getTokenUseCase.getByCurveId('');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.message).toBe('Curve ID is required');
    });
  });

  describe('getByCreator', () => {
    it('should return success response with tokens', async () => {
      const mockTokens = [mockToken];
      vi.mocked(mockTokenService.getTokensByCreator).mockResolvedValue(
        mockTokens,
      );

      const creator = '0x' + 'a'.repeat(64);
      const result = await getTokenUseCase.getByCreator(creator);

      expect(result.success).toBe(true);
      expect(result.data?.tokens).toHaveLength(1);
      expect(result.data?.pagination.total).toBe(1);
      expect(mockTokenService.getTokensByCreator).toHaveBeenCalledWith(creator);
    });

    it('should return success response with empty tokens', async () => {
      vi.mocked(mockTokenService.getTokensByCreator).mockResolvedValue([]);

      const creator = '0x' + 'b'.repeat(64);
      const result = await getTokenUseCase.getByCreator(creator);

      expect(result.success).toBe(true);
      expect(result.data?.tokens).toHaveLength(0);
      expect(result.data?.pagination.total).toBe(0);
    });

    it('should return error response for invalid creator address', async () => {
      const result = await getTokenUseCase.getByCreator('invalid-address');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('INVALID_ADDRESS_FORMAT');
      expect(result.error?.message).toBe('Invalid SUI address format');
    });

    it('should return error response when creator is empty', async () => {
      const result = await getTokenUseCase.getByCreator('');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.message).toBe('Creator address is required');
    });
  });

  describe('list', () => {
    it('should return paginated tokens successfully', async () => {
      const mockTokens = [mockToken];
      const mockResult = { tokens: mockTokens, total: 1 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params = {
        page: 1,
        limit: 20,
        sortBy: 'createdAt' as const,
        sortOrder: 'desc' as const,
      };

      const result = await getTokenUseCase.list(params);

      expect(result.success).toBe(true);
      expect(result.data?.tokens).toHaveLength(1);
      expect(result.data?.pagination).toEqual({
        page: 1,
        limit: 20,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      });
      expect(mockTokenService.listTokens).toHaveBeenCalledWith(params);
    });

    it('should calculate pagination correctly for multiple pages', async () => {
      const mockTokens = Array(10).fill(mockToken);
      const mockResult = { tokens: mockTokens, total: 25 };
      vi.mocked(mockTokenService.listTokens).mockResolvedValue(mockResult);

      const params = {
        page: 2,
        limit: 10,
        sortBy: 'createdAt' as const,
        sortOrder: 'desc' as const,
      };

      const result = await getTokenUseCase.list(params);

      expect(result.success).toBe(true);
      expect(result.data?.pagination).toEqual({
        page: 2,
        limit: 10,
        total: 25,
        totalPages: 3,
        hasNext: true,
        hasPrev: true,
      });
    });

    it('should handle service errors', async () => {
      const serviceError = new Error('Database connection failed');
      vi.mocked(mockTokenService.listTokens).mockRejectedValue(serviceError);

      const params = {
        page: 1,
        limit: 20,
        sortBy: 'createdAt' as const,
        sortOrder: 'desc' as const,
      };

      const result = await getTokenUseCase.list(params);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('DATABASE_ERROR');
      expect(result.error?.message).toBe('Database connection failed');
    });
  });
});
