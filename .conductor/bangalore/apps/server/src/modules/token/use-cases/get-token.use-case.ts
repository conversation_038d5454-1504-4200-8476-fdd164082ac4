import type { Token } from '@hopfun/database';
import { logger } from '@hopfun/logger';

import type {
  GetTokenResponseDto,
  ListTokensRequest,
  ListTokensResponseDto,
  TokenResponseDto,
} from '../dto/get-token.dto';
import type {
  IGetTokenUseCase,
  ITokenService,
  PaginationMeta,
} from '../interfaces/token.interfaces';
import { TokenService } from '../services/token.service';

export class GetTokenUseCase implements IGetTokenUseCase {
  private tokenService: ITokenService;

  constructor(tokenService?: ITokenService) {
    this.tokenService = tokenService ?? new TokenService();
  }

  async getById(id: string): Promise<GetTokenResponseDto> {
    const startTime = Date.now();

    logger.info({ id }, 'Starting token retrieval by ID');

    try {
      // Validate input
      this.validateId(id);

      // Get token
      const token = await this.tokenService.getTokenById(id);

      if (!token) {
        const duration = Date.now() - startTime;
        logger.warn({ id, duration }, 'Token not found');

        return {
          success: false,
          error: {
            code: 'TOKEN_NOT_FOUND',
            message: 'Token not found',
            details: { id },
          },
        };
      }

      const duration = Date.now() - startTime;
      logger.info(
        { id, ticker: token.ticker, duration },
        'Token retrieved successfully',
      );

      return {
        success: true,
        data: this.tokenToDto(token),
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const duration = Date.now() - startTime;

      logger.error(
        { error: errorMessage, id, duration },
        'Failed to retrieve token',
      );

      return {
        success: false,
        error: {
          code: this.getErrorCode(errorMessage),
          message: errorMessage,
          details: { id },
        },
      };
    }
  }

  async getByCurveId(curveId: string): Promise<GetTokenResponseDto> {
    const startTime = Date.now();

    logger.info({ curveId }, 'Starting token retrieval by curve ID');

    try {
      // Validate input
      this.validateCurveId(curveId);

      // Get token
      const token = await this.tokenService.getTokenByCurveId(curveId);

      if (!token) {
        const duration = Date.now() - startTime;
        logger.warn({ curveId, duration }, 'Token not found');

        return {
          success: false,
          error: {
            code: 'TOKEN_NOT_FOUND',
            message: 'Token not found',
            details: { curveId },
          },
        };
      }

      const duration = Date.now() - startTime;
      logger.info(
        { curveId, ticker: token.ticker, duration },
        'Token retrieved successfully',
      );

      return {
        success: true,
        data: this.tokenToDto(token),
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const duration = Date.now() - startTime;

      logger.error(
        { error: errorMessage, curveId, duration },
        'Failed to retrieve token',
      );

      return {
        success: false,
        error: {
          code: this.getErrorCode(errorMessage),
          message: errorMessage,
          details: { curveId },
        },
      };
    }
  }

  async getByCreator(creator: string): Promise<ListTokensResponseDto> {
    const startTime = Date.now();

    logger.info({ creator }, 'Starting tokens retrieval by creator');

    try {
      // Validate input
      this.validateCreatorAddress(creator);

      // Get tokens
      const tokens = await this.tokenService.getTokensByCreator(creator);

      const duration = Date.now() - startTime;
      logger.info(
        { creator, count: tokens.length, duration },
        'Tokens retrieved successfully',
      );

      return {
        success: true,
        data: {
          tokens: tokens.map((token) => this.tokenToDto(token)),
          pagination: {
            page: 1,
            limit: tokens.length,
            total: tokens.length,
            totalPages: 1,
            hasNext: false,
            hasPrev: false,
          },
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const duration = Date.now() - startTime;

      logger.error(
        { error: errorMessage, creator, duration },
        'Failed to retrieve tokens',
      );

      return {
        success: false,
        error: {
          code: this.getErrorCode(errorMessage),
          message: errorMessage,
          details: { creator },
        },
      };
    }
  }

  async list(params: ListTokensRequest): Promise<ListTokensResponseDto> {
    const startTime = Date.now();

    logger.info(params, 'Starting tokens listing');

    try {
      // Get tokens with pagination
      const result = await this.tokenService.listTokens(params);

      // Calculate pagination metadata
      const totalPages = Math.ceil(result.total / params.limit);
      const pagination: PaginationMeta = {
        page: params.page,
        limit: params.limit,
        total: result.total,
        totalPages,
        hasNext: params.page < totalPages,
        hasPrev: params.page > 1,
      };

      const duration = Date.now() - startTime;
      logger.info(
        {
          count: result.tokens.length,
          total: result.total,
          page: params.page,
          duration,
        },
        'Tokens listed successfully',
      );

      return {
        success: true,
        data: {
          tokens: result.tokens.map((token) => this.tokenToDto(token)),
          pagination,
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const duration = Date.now() - startTime;

      logger.error(
        { error: errorMessage, params, duration },
        'Failed to list tokens',
      );

      return {
        success: false,
        error: {
          code: this.getErrorCode(errorMessage),
          message: errorMessage,
          details: params,
        },
      };
    }
  }

  private tokenToDto(token: Token): TokenResponseDto {
    return {
      id: token.id,
      curveId: token.curveId,
      creator: token.creator,
      coinName: token.coinName,
      ticker: token.ticker,
      description: token.description,
      imageUrl: token.imageUrl ?? undefined,
      twitter: token.twitter,
      website: token.website,
      telegram: token.telegram,
      totalSupply: token.totalSupply,
      network: token.network,
      status: token.status,
      currentPrice: token.currentPrice,
      marketCap: token.marketCap,
      virtualSuiAmount: token.virtualSuiAmount,
      suiBalance: token.suiBalance,
      tokenBalance: token.tokenBalance,
      availableTokenReserves: token.availableTokenReserves,
      poolId: token.poolId ?? undefined,
      createdAt: token.createdAt.toISOString(),
      updatedAt: token.updatedAt.toISOString(),
      completedAt: token.completedAt?.toISOString(),
      migratedAt: token.migratedAt?.toISOString(),
    };
  }

  private validateId(id: string): void {
    if (!id || id.trim().length === 0) {
      throw new Error('Token ID is required');
    }
  }

  private validateCurveId(curveId: string): void {
    if (!curveId || curveId.trim().length === 0) {
      throw new Error('Curve ID is required');
    }
  }

  private validateCreatorAddress(creator: string): void {
    if (!creator || creator.trim().length === 0) {
      throw new Error('Creator address is required');
    }

    // SUI addresses are 64 characters long (32 bytes in hex) with 0x prefix
    if (!/^0x[a-fA-F0-9]{64}$/.test(creator)) {
      throw new Error('Invalid SUI address format');
    }
  }

  private getErrorCode(errorMessage: string): string {
    if (
      errorMessage.includes('Token ID is required') ||
      errorMessage.includes('Curve ID is required') ||
      errorMessage.includes('Creator address is required')
    ) {
      return 'VALIDATION_ERROR';
    }

    if (errorMessage.includes('Invalid SUI address format')) {
      return 'INVALID_ADDRESS_FORMAT';
    }

    if (
      errorMessage.includes('database') ||
      errorMessage.includes('connection')
    ) {
      return 'DATABASE_ERROR';
    }

    return 'TOKEN_RETRIEVAL_FAILED';
  }
}
