import type { Token } from '@hopfun/database';
import { logger } from '@hopfun/logger';

import { db } from '@/libs/database';

import type {
  ITokenService,
  ListTokensParams,
  ListTokensResult,
  TokenVerificationResult,
  VerifyTokenParams,
} from '../interfaces/token.interfaces';

export class TokenService implements ITokenService {
  async getTokenById(id: string): Promise<Token | null> {
    try {
      logger.debug({ id }, 'Fetching token by ID');

      const token = await db.token.findUnique({
        where: { id },
        include: {
          stats: true,
        },
      });

      if (!token) {
        logger.debug({ id }, 'Token not found');
        return null;
      }

      logger.debug({ id, ticker: token.ticker }, 'Token found');
      return token;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error({ error: errorMessage, id }, 'Failed to fetch token by ID');
      throw new Error(`Failed to fetch token: ${errorMessage}`);
    }
  }

  async getTokenByCurveId(curveId: string): Promise<Token | null> {
    try {
      logger.debug({ curveId }, 'Fetching token by curve ID');

      const token = await db.token.findUnique({
        where: { curveId },
        include: {
          stats: true,
        },
      });

      if (!token) {
        logger.debug({ curveId }, 'Token not found');
        return null;
      }

      logger.debug({ curveId, ticker: token.ticker }, 'Token found');
      return token;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        { error: errorMessage, curveId },
        'Failed to fetch token by curve ID',
      );
      throw new Error(`Failed to fetch token: ${errorMessage}`);
    }
  }

  async getTokensByCreator(creator: string): Promise<Token[]> {
    try {
      logger.debug({ creator }, 'Fetching tokens by creator');

      const tokens = await db.token.findMany({
        where: { creator },
        include: {
          stats: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      logger.debug({ creator, count: tokens.length }, 'Tokens found');
      return tokens;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        { error: errorMessage, creator },
        'Failed to fetch tokens by creator',
      );
      throw new Error(`Failed to fetch tokens: ${errorMessage}`);
    }
  }

  async listTokens(params: ListTokensParams): Promise<ListTokensResult> {
    try {
      logger.debug(params, 'Listing tokens with parameters');

      const { page, limit, network, status, sortBy, sortOrder } = params;
      const skip = (page - 1) * limit;

      // Build where clause
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const where: any = {};
      if (network) where.network = network;
      if (status) where.status = status;

      // Build order by clause
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const orderBy: any = {};
      orderBy[sortBy] = sortOrder;

      // Execute queries in parallel
      const [tokens, total] = await Promise.all([
        db.token.findMany({
          where,
          include: {
            stats: true,
          },
          orderBy,
          skip,
          take: limit,
        }),
        db.token.count({ where }),
      ]);

      logger.debug(
        {
          count: tokens.length,
          total,
          page,
          limit,
          network,
          status,
          sortBy,
          sortOrder,
        },
        'Tokens listed successfully',
      );

      return { tokens, total };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error({ error: errorMessage, params }, 'Failed to list tokens');
      throw new Error(`Failed to list tokens: ${errorMessage}`);
    }
  }

  async verifyTokenExists(curveId: string): Promise<boolean> {
    try {
      logger.debug({ curveId }, 'Verifying token existence');

      const token = await db.token.findUnique({
        where: { curveId },
        select: { id: true },
      });

      const exists = !!token;
      logger.debug({ curveId, exists }, 'Token existence verified');
      return exists;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error(
        { error: errorMessage, curveId },
        'Failed to verify token existence',
      );
      throw new Error(`Failed to verify token existence: ${errorMessage}`);
    }
  }

  async verifyToken(
    params: VerifyTokenParams,
  ): Promise<TokenVerificationResult> {
    try {
      logger.debug(params, 'Verifying token with parameters');

      const { curveId, creator, ticker } = params;

      const token = await db.token.findUnique({
        where: { curveId },
        select: {
          id: true,
          curveId: true,
          creator: true,
          coinName: true,
          ticker: true,
          status: true,
          network: true,
          createdAt: true,
        },
      });

      if (!token) {
        logger.debug({ curveId }, 'Token not found during verification');
        return {
          exists: false,
          isValid: false,
          validationErrors: ['Token does not exist'],
        };
      }

      // Perform validation checks
      const validationErrors: string[] = [];

      if (creator && token.creator !== creator) {
        validationErrors.push('Creator address does not match');
      }

      if (ticker && token.ticker !== ticker) {
        validationErrors.push('Ticker does not match');
      }

      const isValid = validationErrors.length === 0;

      logger.debug(
        {
          curveId,
          exists: true,
          isValid,
          validationErrors: validationErrors.length,
        },
        'Token verification completed',
      );

      return {
        exists: true,
        isValid,
        token: {
          id: token.id,
          curveId: token.curveId,
          creator: token.creator,
          coinName: token.coinName,
          ticker: token.ticker,
          status: token.status,
          network: token.network,
          createdAt: token.createdAt.toISOString(),
        },
        validationErrors:
          validationErrors.length > 0 ? validationErrors : undefined,
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      logger.error({ error: errorMessage, params }, 'Failed to verify token');
      throw new Error(`Failed to verify token: ${errorMessage}`);
    }
  }
}
