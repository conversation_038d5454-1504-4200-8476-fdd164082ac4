import { app } from '@/app';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

describe('Wallet Routes', () => {
  beforeEach(() => {
    // Mock console methods to avoid logs in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'info').mockImplementation(() => {});
    vi.spyOn(console, 'debug').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('GET /api/wallet/health', () => {
    it('should return health status', async () => {
      const res = await app.request('/api/wallet/health');
      const body = await res.json();

      expect(res.status).toBe(200);
      expect(body.status).toBe('success');
      expect(body.message).toBe('Wallet module is healthy');
      expect(body.data).toEqual({
        version: '1.0.0',
        status: 'operational',
      });
    });
  });

  describe('GET /api/wallet/balance', () => {
    const validAddress = '0x' + 'a'.repeat(64);
    const invalidAddress = 'invalid-address';

    it('should return 400 for missing address query parameter', async () => {
      const res = await app.request('/api/wallet/balance');
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for empty address query parameter', async () => {
      const res = await app.request('/api/wallet/balance?address=');
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid address format', async () => {
      const res = await app.request(
        `/api/wallet/balance?address=${invalidAddress}`,
      );
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
      expect(body.error?.details).toBeDefined();
    });

    it('should return 400 for address with wrong length', async () => {
      const shortAddress = '0x' + 'a'.repeat(63); // 63 chars instead of 64
      const res = await app.request(
        `/api/wallet/balance?address=${shortAddress}`,
      );
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it.skip('should handle valid address format with mock RPC', async () => {
      // Skipped: Requires complex mocking of SuiClient module
      // For true integration tests, use a test RPC endpoint
    });

    it('should handle special characters in address parameter', async () => {
      const res = await app.request(
        '/api/wallet/balance?address=0x<script>alert(1)</script>',
      );
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should handle URL encoded address parameter', async () => {
      const encodedAddress = encodeURIComponent(validAddress);
      const res = await app.request(
        `/api/wallet/balance?address=${encodedAddress}`,
      );
      const body = await res.json();

      // Should pass validation for properly encoded valid address
      // Will return success status but actual balance fetch would fail without real RPC
      expect([200, 500, 503]).toContain(res.status);
      expect(['success', 'error']).toContain(body.status);
    });
  });

  describe('GET /api/wallet/balance/:address', () => {
    const validAddress = '0x' + 'a'.repeat(64);
    const invalidAddress = 'invalid-address';

    it('should return 400 for invalid address format in path', async () => {
      const res = await app.request(`/api/wallet/balance/${invalidAddress}`);
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for address with wrong length in path', async () => {
      const shortAddress = '0x' + 'a'.repeat(63); // 63 chars instead of 64
      const res = await app.request(`/api/wallet/balance/${shortAddress}`);
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it.skip('should handle valid address format in path with mock RPC', async () => {
      // Skipped: Requires complex mocking of SuiClient module
      // For true integration tests, use a test RPC endpoint
    });

    it('should handle special characters in address path parameter', async () => {
      const res = await app.request(
        '/api/wallet/balance/<script>alert(1)</script>',
      );
      const body = await res.json();

      // Special characters in path may return 404 (not found) or 400 (bad request)
      expect([400, 404]).toContain(res.status);
      if (res.status === 400) {
        expect(body.status).toBe('error');
        expect(body.error?.code).toBe('VALIDATION_ERROR');
      }
    });

    it('should handle very long invalid address in path', async () => {
      const longAddress = '0x' + 'a'.repeat(200);
      const res = await app.request(`/api/wallet/balance/${longAddress}`);
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('GET /api/wallet/faucet', () => {
    const validAddress = '0x' + 'a'.repeat(64);
    const invalidAddress = 'invalid-address';

    beforeEach(() => {
      // Mock the faucet module to avoid actual network calls
      vi.mock('@mysten/sui/faucet', () => ({
        getFaucetHost: vi.fn(
          (network: string) => `https://faucet.${network}.sui.io`,
        ),
        requestSuiFromFaucetV2: vi.fn(),
      }));
    });

    it('should return 400 for missing address query parameter', async () => {
      const res = await app.request('/api/wallet/faucet');
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for empty address query parameter', async () => {
      const res = await app.request('/api/wallet/faucet?address=');
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for invalid address format', async () => {
      const res = await app.request(
        `/api/wallet/faucet?address=${invalidAddress}`,
      );
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
      expect(body.error?.details).toBeDefined();
    });

    it('should handle valid address format', async () => {
      const res = await app.request(
        `/api/wallet/faucet?address=${validAddress}`,
      );

      // We expect this to potentially fail with a real faucet call or succeed if mocked properly
      // The important part is that it passes validation
      expect([200, 429, 500, 503]).toContain(res.status);
    });

    it('should handle address with 0x prefix', async () => {
      const addressWithPrefix = '0x' + 'b'.repeat(64);
      const res = await app.request(
        `/api/wallet/faucet?address=${addressWithPrefix}`,
      );

      // Should pass validation at minimum
      expect([200, 429, 500, 503]).toContain(res.status);
    });

    it('should return 400 for address without 0x prefix', async () => {
      const addressWithoutPrefix = 'a'.repeat(64);
      const res = await app.request(
        `/api/wallet/faucet?address=${addressWithoutPrefix}`,
      );
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for short address', async () => {
      const shortAddress = '0x' + 'a'.repeat(32);
      const res = await app.request(
        `/api/wallet/faucet?address=${shortAddress}`,
      );
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should return 400 for very long address', async () => {
      const longAddress = '0x' + 'a'.repeat(200);
      const res = await app.request(
        `/api/wallet/faucet?address=${longAddress}`,
      );
      const body = await res.json();

      expect(res.status).toBe(400);
      expect(body.status).toBe('error');
      expect(body.error?.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('Response format validation', () => {
    it('should return consistent error format for validation errors', async () => {
      const res = await app.request('/api/wallet/balance?address=invalid');
      const body = await res.json();

      expect(body).toHaveProperty('status', 'error');
      expect(body).toHaveProperty('error');
      expect(body.error).toHaveProperty('code');
      expect(body.error).toHaveProperty('details');
    });

    it('should include CORS headers', async () => {
      const res = await app.request('/api/wallet/balance?address=invalid');

      expect(res.headers.get('access-control-allow-origin')).toBeDefined();
    });

    it('should include security headers', async () => {
      const res = await app.request('/api/wallet/health');

      expect(res.headers.get('x-content-type-options')).toBe('nosniff');
      expect(res.headers.get('x-frame-options')).toBe('SAMEORIGIN');
    });

    it('should return consistent error format for faucet validation errors', async () => {
      const res = await app.request('/api/wallet/faucet?address=invalid');
      const body = await res.json();

      expect(body).toHaveProperty('status', 'error');
      expect(body).toHaveProperty('error');
      expect(body.error).toHaveProperty('code');
      expect(body.error).toHaveProperty('details');
    });
  });
});
