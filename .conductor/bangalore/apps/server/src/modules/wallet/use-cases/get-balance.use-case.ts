import { logger } from '@hopfun/logger';

import type {
  GetBalanceResponse,
  IGetBalanceUseCase,
  ISuiBalanceService,
} from '../interfaces/wallet.interfaces';
import { SuiBalanceService } from '../services/sui-balance.service';

export class GetBalanceUseCase implements IGetBalanceUseCase {
  private suiBalanceService: ISuiBalanceService;

  constructor(suiBalanceService?: ISuiBalanceService) {
    this.suiBalanceService = suiBalanceService ?? new SuiBalanceService();
  }

  async execute(address: string): Promise<GetBalanceResponse> {
    const startTime = Date.now();

    logger.info(
      {
        address,
      },
      'Starting balance retrieval',
    );

    try {
      // Validate input
      this.validateAddress(address);

      // Validate if address exists on chain
      const isValid = await this.suiBalanceService.validateAddress(address);
      if (!isValid) {
        throw new Error('Address does not exist on the blockchain');
      }

      // Get balance information
      const balance = await this.suiBalanceService.getBalance(address);

      // Get coin details for more information
      const coins = await this.suiBalanceService.getCoins(address);

      // Convert balance from MIST to SUI (1 SUI = 10^9 MIST)
      const totalBalanceInSui = Number(balance.totalBalance) / 1e9;

      const duration = Date.now() - startTime;
      logger.info(
        {
          address,
          totalBalance: balance.totalBalance,
          totalBalanceInSui,
          coinObjectCount: balance.coinObjectCount,
          duration,
        },
        'Balance retrieved successfully',
      );

      return {
        success: true,
        data: {
          address,
          totalBalance: balance.totalBalance,
          totalBalanceInSui,
          coinObjectCount: balance.coinObjectCount,
          lockedBalance: balance.lockedBalance,
          coins,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      const duration = Date.now() - startTime;

      logger.error(
        {
          error: errorMessage,
          address,
          duration,
        },
        'Failed to retrieve balance',
      );

      // Determine error code based on the error message
      let errorCode = 'BALANCE_RETRIEVAL_FAILED';

      if (errorMessage.includes('Invalid SUI address format')) {
        errorCode = 'INVALID_ADDRESS_FORMAT';
      } else if (errorMessage.includes('Address does not exist')) {
        errorCode = 'ADDRESS_NOT_FOUND';
      } else if (errorMessage.includes('RPC')) {
        errorCode = 'RPC_CONNECTION_ERROR';
      }

      return {
        success: false,
        error: {
          code: errorCode,
          message: errorMessage,
          details: {
            address,
          },
        },
      };
    }
  }

  private validateAddress(address: string): void {
    // Validate address format
    if (!address || address.trim().length === 0) {
      throw new Error('Address is required');
    }

    // SUI addresses are 64 characters long (32 bytes in hex) with 0x prefix
    if (!/^0x[a-fA-F0-9]{64}$/.test(address)) {
      throw new Error('Invalid SUI address format');
    }
  }
}
