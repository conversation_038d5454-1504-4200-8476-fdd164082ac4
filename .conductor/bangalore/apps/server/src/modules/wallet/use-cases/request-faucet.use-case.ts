import { logger } from '@hopfun/logger';

import type {
  IRequestFaucetUseCase,
  ISuiFaucetService,
  RequestFaucetResponse,
} from '../interfaces/wallet.interfaces';

export class RequestFaucetUseCase implements IRequestFaucetUseCase {
  constructor(private readonly suiFaucetService: ISuiFaucetService) {}

  async execute(address: string): Promise<RequestFaucetResponse> {
    try {
      logger.info(`Processing faucet request for address: ${address}`);

      const result = await this.suiFaucetService.requestFaucet(address);

      if (result.error) {
        logger.error(result.error, 'Faucet request failed:');
        return {
          success: false,
          error: {
            code: 'FAUCET_REQUEST_FAILED',
            message: result.error,
          },
        };
      }

      const totalAmount = result.transferredGasObjects.reduce(
        (sum, obj) => sum + obj.amount,
        0,
      );

      logger.info(
        `Successfully completed faucet request. Total amount: ${totalAmount} MIST`,
      );

      return {
        success: true,
        data: {
          address,
          transferredGasObjects: result.transferredGasObjects,
          totalAmount,
          timestamp: new Date().toISOString(),
        },
      };
    } catch (error) {
      logger.error(error, 'Error in RequestFaucetUseCase:');

      if (error instanceof Error) {
        return {
          success: false,
          error: {
            code: 'FAUCET_ERROR',
            message: error.message,
            details: error.stack,
          },
        };
      }

      return {
        success: false,
        error: {
          code: 'UNKNOWN_ERROR',
          message: 'An unknown error occurred while requesting faucet',
        },
      };
    }
  }
}
