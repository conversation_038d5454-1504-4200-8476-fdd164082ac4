import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

import type { ISuiBalanceService } from '../interfaces/wallet.interfaces';
import { GetBalanceUseCase } from './get-balance.use-case';

describe('GetBalanceUseCase', () => {
  let mockSuiBalanceService: ISuiBalanceService;
  let useCase: GetBalanceUseCase;

  beforeEach(() => {
    // Create mock service
    mockSuiBalanceService = {
      getBalance: vi.fn(),
      getCoins: vi.fn(),
      validateAddress: vi.fn(),
    } as unknown as ISuiBalanceService;

    // Create use case with mock service
    useCase = new GetBalanceUseCase(mockSuiBalanceService);

    // Mock console methods to avoid logs in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'info').mockImplementation(() => {});
    vi.spyOn(console, 'debug').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('execute', () => {
    const validAddress = '0x' + 'a'.repeat(64);
    const mockBalance = {
      totalBalance: '1000000000', // 1 SUI in MIST
      coinObjectCount: 5,
      lockedBalance: undefined,
    };
    const mockCoins = [
      {
        coinType: '0x2::sui::SUI',
        coinObjectId: '0x123',
        version: '1',
        digest: 'digest123',
        balance: '500000000',
        previousTransaction: 'tx123',
      },
      {
        coinType: '0x2::sui::SUI',
        coinObjectId: '0x456',
        version: '2',
        digest: 'digest456',
        balance: '500000000',
        previousTransaction: 'tx456',
      },
    ];

    it('should successfully retrieve balance for valid address', async () => {
      // Arrange
      vi.mocked(mockSuiBalanceService.validateAddress).mockResolvedValue(true);
      vi.mocked(mockSuiBalanceService.getBalance).mockResolvedValue(
        mockBalance,
      );
      vi.mocked(mockSuiBalanceService.getCoins).mockResolvedValue(mockCoins);

      // Act
      const result = await useCase.execute(validAddress);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.address).toBe(validAddress);
      expect(result.data?.totalBalance).toBe('1000000000');
      expect(result.data?.totalBalanceInSui).toBe(1);
      expect(result.data?.coinObjectCount).toBe(5);
      expect(result.data?.coins).toEqual(mockCoins);
      expect(result.data?.timestamp).toBeDefined();
      expect(result.error).toBeUndefined();

      // Verify service calls
      expect(mockSuiBalanceService.validateAddress).toHaveBeenCalledWith(
        validAddress,
      );
      expect(mockSuiBalanceService.getBalance).toHaveBeenCalledWith(
        validAddress,
      );
      expect(mockSuiBalanceService.getCoins).toHaveBeenCalledWith(validAddress);
    });

    it('should handle locked balances', async () => {
      // Arrange
      const balanceWithLocked = {
        ...mockBalance,
        lockedBalance: {
          '0xStakePool1': '100000000',
          '0xStakePool2': '200000000',
        },
      };
      vi.mocked(mockSuiBalanceService.validateAddress).mockResolvedValue(true);
      vi.mocked(mockSuiBalanceService.getBalance).mockResolvedValue(
        balanceWithLocked,
      );
      vi.mocked(mockSuiBalanceService.getCoins).mockResolvedValue(mockCoins);

      // Act
      const result = await useCase.execute(validAddress);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.lockedBalance).toEqual(
        balanceWithLocked.lockedBalance,
      );
    });

    it('should return error for empty address', async () => {
      // Act
      const result = await useCase.execute('');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('BALANCE_RETRIEVAL_FAILED');
      expect(result.error?.message).toContain('Address is required');
      expect(result.data).toBeUndefined();

      // Verify no service calls were made
      expect(mockSuiBalanceService.validateAddress).not.toHaveBeenCalled();
      expect(mockSuiBalanceService.getBalance).not.toHaveBeenCalled();
      expect(mockSuiBalanceService.getCoins).not.toHaveBeenCalled();
    });

    it('should return error for invalid address format', async () => {
      // Act
      const result = await useCase.execute('invalid-address');

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('INVALID_ADDRESS_FORMAT');
      expect(result.error?.message).toContain('Invalid SUI address format');
      expect(result.data).toBeUndefined();

      // Verify no service calls were made
      expect(mockSuiBalanceService.validateAddress).not.toHaveBeenCalled();
      expect(mockSuiBalanceService.getBalance).not.toHaveBeenCalled();
      expect(mockSuiBalanceService.getCoins).not.toHaveBeenCalled();
    });

    it('should return error for address with wrong length', async () => {
      // Act
      const result = await useCase.execute('0x' + 'a'.repeat(63)); // 63 chars instead of 64

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('INVALID_ADDRESS_FORMAT');
      expect(result.error?.message).toContain('Invalid SUI address format');
    });

    it('should return error for non-existent address', async () => {
      // Arrange
      vi.mocked(mockSuiBalanceService.validateAddress).mockResolvedValue(false);

      // Act
      const result = await useCase.execute(validAddress);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('ADDRESS_NOT_FOUND');
      expect(result.error?.message).toContain(
        'Address does not exist on the blockchain',
      );
      expect(result.data).toBeUndefined();

      // Verify service calls
      expect(mockSuiBalanceService.validateAddress).toHaveBeenCalledWith(
        validAddress,
      );
      expect(mockSuiBalanceService.getBalance).not.toHaveBeenCalled();
      expect(mockSuiBalanceService.getCoins).not.toHaveBeenCalled();
    });

    it('should handle RPC connection errors', async () => {
      // Arrange
      vi.mocked(mockSuiBalanceService.validateAddress).mockResolvedValue(true);
      vi.mocked(mockSuiBalanceService.getBalance).mockRejectedValue(
        new Error('RPC connection failed'),
      );

      // Act
      const result = await useCase.execute(validAddress);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('RPC_CONNECTION_ERROR');
      expect(result.error?.message).toContain('RPC connection failed');
      expect(result.data).toBeUndefined();
    });

    it('should handle service errors during coin retrieval', async () => {
      // Arrange
      vi.mocked(mockSuiBalanceService.validateAddress).mockResolvedValue(true);
      vi.mocked(mockSuiBalanceService.getBalance).mockResolvedValue(
        mockBalance,
      );
      vi.mocked(mockSuiBalanceService.getCoins).mockRejectedValue(
        new Error('Failed to fetch coins'),
      );

      // Act
      const result = await useCase.execute(validAddress);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('BALANCE_RETRIEVAL_FAILED');
      expect(result.error?.message).toContain('Failed to fetch coins');
    });

    it('should handle unexpected errors', async () => {
      // Arrange
      vi.mocked(mockSuiBalanceService.validateAddress).mockResolvedValue(true);
      vi.mocked(mockSuiBalanceService.getBalance).mockRejectedValue(
        'Unexpected error',
      );

      // Act
      const result = await useCase.execute(validAddress);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.code).toBe('BALANCE_RETRIEVAL_FAILED');
      expect(result.error?.message).toBe('Unexpected error');
    });

    it('should correctly convert MIST to SUI', async () => {
      // Arrange
      const largeBalance = {
        totalBalance: '123456789012345678', // Large amount in MIST
        coinObjectCount: 1,
        lockedBalance: undefined,
      };
      vi.mocked(mockSuiBalanceService.validateAddress).mockResolvedValue(true);
      vi.mocked(mockSuiBalanceService.getBalance).mockResolvedValue(
        largeBalance,
      );
      vi.mocked(mockSuiBalanceService.getCoins).mockResolvedValue([]);

      // Act
      const result = await useCase.execute(validAddress);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.totalBalance).toBe('123456789012345678');
      expect(result.data?.totalBalanceInSui).toBeCloseTo(
        123456789.012345678,
        6,
      );
    });

    it('should handle zero balance', async () => {
      // Arrange
      const zeroBalance = {
        totalBalance: '0',
        coinObjectCount: 0,
        lockedBalance: undefined,
      };
      vi.mocked(mockSuiBalanceService.validateAddress).mockResolvedValue(true);
      vi.mocked(mockSuiBalanceService.getBalance).mockResolvedValue(
        zeroBalance,
      );
      vi.mocked(mockSuiBalanceService.getCoins).mockResolvedValue([]);

      // Act
      const result = await useCase.execute(validAddress);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data?.totalBalance).toBe('0');
      expect(result.data?.totalBalanceInSui).toBe(0);
      expect(result.data?.coinObjectCount).toBe(0);
      expect(result.data?.coins).toEqual([]);
    });
  });
});
