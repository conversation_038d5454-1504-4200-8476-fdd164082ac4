import { z } from 'zod';

export const requestFaucetQuerySchema = z.object({
  address: z
    .string()
    .min(1, 'Address is required')
    .regex(/^0x[a-fA-F0-9]{64}$/, 'Invalid SUI address format'),
});

export type RequestFaucetQuery = z.infer<typeof requestFaucetQuerySchema>;

export const requestFaucetResponseSchema = z.object({
  success: z.boolean(),
  data: z
    .object({
      address: z.string(),
      transferredGasObjects: z.array(
        z.object({
          id: z.string(),
          transferTxDigest: z.string(),
          amount: z.number(),
        }),
      ),
      totalAmount: z.number(),
      timestamp: z.string(),
    })
    .optional(),
  error: z
    .object({
      code: z.string(),
      message: z.string(),
      details: z.unknown().optional(),
    })
    .optional(),
});

export type RequestFaucetResponse = z.infer<typeof requestFaucetResponseSchema>;
