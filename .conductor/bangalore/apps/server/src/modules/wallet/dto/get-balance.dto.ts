import { z } from 'zod';

export const GetBalanceRequestSchema = z.object({
  address: z
    .string()
    .min(1, 'Address is required')
    .regex(/^0x[a-fA-F0-9]{64}$/, 'Invalid SUI address format'),
});

export type GetBalanceRequest = z.infer<typeof GetBalanceRequestSchema>;

export interface GetBalanceResponseDto {
  success: boolean;
  data?: {
    address: string;
    totalBalance: string;
    totalBalanceInSui: number;
    coinObjectCount: number;
    lockedBalance?: Record<string, string>;
    coins?: {
      coinType: string;
      coinObjectId: string;
      version: string;
      digest: string;
      balance: string;
      previousTransaction: string;
    }[];
    timestamp: string;
  };
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
}
