export interface IGetBalanceUseCase {
  execute(address: string): Promise<GetBalanceResponse>;
}

export interface ISuiBalanceService {
  getBalance(address: string): Promise<SuiBalance>;
  getCoins(address: string): Promise<CoinDetails[]>;
  validateAddress(address: string): Promise<boolean>;
}

export interface SuiBalance {
  totalBalance: string;
  coinObjectCount: number;
  lockedBalance?: Record<string, string>;
}

export interface CoinDetails {
  coinType: string;
  coinObjectId: string;
  version: string;
  digest: string;
  balance: string;
  previousTransaction: string;
}

export interface GetBalanceResponse {
  success: boolean;
  data?: BalanceData;
  error?: ErrorResponse;
}

export interface BalanceData {
  address: string;
  totalBalance: string;
  totalBalanceInSui: number;
  coinObjectCount: number;
  lockedBalance?: Record<string, string>;
  coins?: CoinDetails[];
  timestamp: string;
}

export interface ErrorResponse {
  code: string;
  message: string;
  details?: unknown;
}

export interface WalletConfig {
  suiRpcUrl: string;
  network?: 'MAINNET' | 'TESTNET';
}

export interface IRequestFaucetUseCase {
  execute(address: string): Promise<RequestFaucetResponse>;
}

export interface ISuiFaucetService {
  requestFaucet(address: string): Promise<FaucetResult>;
}

export interface FaucetResult {
  transferredGasObjects: {
    id: string;
    transferTxDigest: string;
    amount: number;
  }[];
  error?: string | null;
}

export interface RequestFaucetResponse {
  success: boolean;
  data?: FaucetData;
  error?: ErrorResponse;
}

export interface FaucetData {
  address: string;
  transferredGasObjects: {
    id: string;
    transferTxDigest: string;
    amount: number;
  }[];
  totalAmount: number;
  timestamp: string;
}
