import { logger } from '@hopfun/logger';
import { getFaucetHost, requestSuiFromFaucetV2 } from '@mysten/sui/faucet';

import type {
  FaucetResult,
  ISuiFaucetService,
} from '../interfaces/wallet.interfaces';

export class SuiFaucetService implements ISuiFaucetService {
  private readonly network: 'MAINNET' | 'TESTNET' | 'DEVNET';

  constructor(network?: string) {
    this.network = this.getNetworkType(network);
    logger.info(`SuiFaucetService initialized with network: ${this.network}`);
  }

  private getNetworkType(network?: string): 'MAINNET' | 'TESTNET' | 'DEVNET' {
    const normalizedNetwork = network?.toUpperCase();
    if (normalizedNetwork === 'MAINNET') {
      return 'MAINNET';
    } else if (normalizedNetwork === 'TESTNET') {
      return 'TESTNET';
    }
    return 'DEVNET';
  }

  async requestFaucet(address: string): Promise<FaucetResult> {
    try {
      if (this.network === 'MAINNET') {
        throw new Error('Faucet is not available on Mainnet');
      }

      logger.info(
        `Requesting faucet for address: ${address} on ${this.network}`,
      );

      const faucetNetwork = this.network.toLowerCase() as 'testnet' | 'devnet';
      const faucetHost = getFaucetHost(faucetNetwork);

      logger.debug(`Using faucet host: ${faucetHost}`);

      const result = await requestSuiFromFaucetV2({
        host: faucetHost,
        recipient: address,
      });

      // Check if the faucet request was successful
      if (!result.coins_sent || result.coins_sent.length === 0) {
        logger.error('Faucet request failed: No coins received');
        throw new Error('Faucet request failed: No coins received');
      }

      // Transform coins_sent to our internal format
      const transferredGasObjects = result.coins_sent.map((coin) => ({
        id: coin.id,
        transferTxDigest: coin.transferTxDigest,
        amount: coin.amount,
      }));

      logger.info(
        `Successfully received ${transferredGasObjects.length} gas objects from faucet`,
      );
      logger.debug(transferredGasObjects, 'Transferred gas objects');

      return {
        transferredGasObjects,
        error: null,
      };
    } catch (error) {
      logger.error(error, 'Failed to request faucet');

      if (error instanceof Error) {
        if (error.message.includes('limit exceeded')) {
          throw new Error(
            'Faucet request limit exceeded. Please wait a few minutes before trying again.',
          );
        }
        if (error.message.includes('not available')) {
          throw new Error(error.message);
        }
        if (error.message.includes('Invalid')) {
          throw new Error(`Invalid address format: ${address}`);
        }
        throw error;
      }

      throw new Error('Unknown error occurred while requesting faucet');
    }
  }
}
