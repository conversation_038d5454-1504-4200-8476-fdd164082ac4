import { SuiClient } from '@mysten/sui/client';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import { SuiBalanceService } from './sui-balance.service';

vi.mock('@mysten/sui/client');

describe('SuiBalanceService', () => {
  let service: SuiBalanceService;
  let mockClient: any;

  beforeEach(() => {
    // Mock SuiClient
    mockClient = {
      getBalance: vi.fn(),
      getCoins: vi.fn(),
      getAllBalances: vi.fn(),
      getOwnedObjects: vi.fn(),
    };

    vi.mocked(SuiClient).mockImplementation(() => mockClient);

    // Set environment variable
    process.env.SUI_RPC_URL = 'https://fullnode.testnet.sui.io:443';

    // Create service
    service = new SuiBalanceService();

    // Mock console methods to avoid logs in tests
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'info').mockImplementation(() => {});
    vi.spyOn(console, 'debug').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
    delete process.env.SUI_RPC_URL;
  });

  describe('constructor', () => {
    it('should initialize with provided config', () => {
      const customService = new SuiBalanceService({
        suiRpcUrl: 'https://custom.rpc.url',
        network: 'MAINNET',
      });

      expect(customService).toBeDefined();
      expect(vi.mocked(SuiClient)).toHaveBeenCalledWith({
        url: 'https://custom.rpc.url',
      });
    });

    it('should throw error if no RPC URL is provided', () => {
      delete process.env.SUI_RPC_URL;

      expect(() => new SuiBalanceService()).toThrow('SUI_RPC_URL is required');
    });

    it('should use environment variables as defaults', () => {
      process.env.NETWORK = 'MAINNET';

      const envService = new SuiBalanceService();

      expect(envService).toBeDefined();
      expect(vi.mocked(SuiClient)).toHaveBeenCalledWith({
        url: 'https://fullnode.testnet.sui.io:443',
      });
    });
  });

  describe('getBalance', () => {
    const validAddress = '0x' + 'a'.repeat(64);

    it('should successfully get balance', async () => {
      // Arrange
      const mockBalance = {
        totalBalance: '1000000000',
        coinObjectCount: 5,
        lockedBalance: {},
      };
      mockClient.getBalance.mockResolvedValue(mockBalance);

      // Act
      const result = await service.getBalance(validAddress);

      // Assert
      expect(result).toEqual({
        totalBalance: '1000000000',
        coinObjectCount: 5,
      });
      expect(mockClient.getBalance).toHaveBeenCalledWith({
        owner: validAddress,
        coinType: '0x2::sui::SUI',
      });
    });

    it('should include locked balances when present', async () => {
      // Arrange
      const mockBalance = {
        totalBalance: '1000000000',
        coinObjectCount: 5,
        lockedBalance: {
          '0xStakePool1': '100000000',
          '0xStakePool2': '200000000',
        },
      };
      mockClient.getBalance.mockResolvedValue(mockBalance);

      // Act
      const result = await service.getBalance(validAddress);

      // Assert
      expect(result).toEqual({
        totalBalance: '1000000000',
        coinObjectCount: 5,
        lockedBalance: {
          '0xStakePool1': '100000000',
          '0xStakePool2': '200000000',
        },
      });
    });

    it('should handle RPC errors', async () => {
      // Arrange
      mockClient.getBalance.mockRejectedValue(new Error('RPC error'));

      // Act & Assert
      await expect(service.getBalance(validAddress)).rejects.toThrow(
        'Failed to fetch balance: RPC error',
      );
    });

    it('should handle non-Error objects', async () => {
      // Arrange
      mockClient.getBalance.mockRejectedValue('String error');

      // Act & Assert
      await expect(service.getBalance(validAddress)).rejects.toThrow(
        'Failed to fetch balance: String error',
      );
    });
  });

  describe('getCoins', () => {
    const validAddress = '0x' + 'a'.repeat(64);

    it('should successfully get coins', async () => {
      // Arrange
      const mockCoins = {
        data: [
          {
            coinType: '0x2::sui::SUI',
            coinObjectId: '0x123',
            version: '1',
            digest: 'digest123',
            balance: '500000000',
            previousTransaction: 'tx123',
          },
          {
            coinType: '0x2::sui::SUI',
            coinObjectId: '0x456',
            version: '2',
            digest: 'digest456',
            balance: '300000000',
            previousTransaction: 'tx456',
          },
        ],
        nextCursor: null,
        hasNextPage: false,
      };
      mockClient.getCoins.mockResolvedValue(mockCoins);

      // Act
      const result = await service.getCoins(validAddress);

      // Assert
      expect(result).toEqual([
        {
          coinType: '0x2::sui::SUI',
          coinObjectId: '0x123',
          version: '1',
          digest: 'digest123',
          balance: '500000000',
          previousTransaction: 'tx123',
        },
        {
          coinType: '0x2::sui::SUI',
          coinObjectId: '0x456',
          version: '2',
          digest: 'digest456',
          balance: '300000000',
          previousTransaction: 'tx456',
        },
      ]);
      expect(mockClient.getCoins).toHaveBeenCalledWith({
        owner: validAddress,
        coinType: '0x2::sui::SUI',
      });
    });

    it('should handle empty coin list', async () => {
      // Arrange
      const mockCoins = {
        data: [],
        nextCursor: null,
        hasNextPage: false,
      };
      mockClient.getCoins.mockResolvedValue(mockCoins);

      // Act
      const result = await service.getCoins(validAddress);

      // Assert
      expect(result).toEqual([]);
    });

    it('should handle RPC errors', async () => {
      // Arrange
      mockClient.getCoins.mockRejectedValue(new Error('RPC error'));

      // Act & Assert
      await expect(service.getCoins(validAddress)).rejects.toThrow(
        'Failed to fetch coins: RPC error',
      );
    });
  });

  describe('getAllBalances', () => {
    const validAddress = '0x' + 'a'.repeat(64);

    it('should successfully get all balances', async () => {
      // Arrange
      const mockBalances = [
        {
          coinType: '0x2::sui::SUI',
          totalBalance: '1000000000',
          coinObjectCount: 5,
        },
        {
          coinType: '0xCustomToken::token::TOKEN',
          totalBalance: '5000000',
          coinObjectCount: 2,
        },
      ];
      mockClient.getAllBalances.mockResolvedValue(mockBalances);

      // Act
      const result = await service.getAllBalances(validAddress);

      // Assert
      expect(result).toEqual(mockBalances);
      expect(mockClient.getAllBalances).toHaveBeenCalledWith({
        owner: validAddress,
      });
    });

    it('should handle empty balances', async () => {
      // Arrange
      mockClient.getAllBalances.mockResolvedValue([]);

      // Act
      const result = await service.getAllBalances(validAddress);

      // Assert
      expect(result).toEqual([]);
    });

    it('should handle RPC errors', async () => {
      // Arrange
      mockClient.getAllBalances.mockRejectedValue(new Error('RPC error'));

      // Act & Assert
      await expect(service.getAllBalances(validAddress)).rejects.toThrow(
        'Failed to fetch all balances: RPC error',
      );
    });
  });

  describe('validateAddress', () => {
    const validAddress = '0x' + 'a'.repeat(64);

    it('should return true for valid address', async () => {
      // Arrange
      const mockObjects = {
        data: [],
        nextCursor: null,
        hasNextPage: false,
      };
      mockClient.getOwnedObjects.mockResolvedValue(mockObjects);

      // Act
      const result = await service.validateAddress(validAddress);

      // Assert
      expect(result).toBe(true);
      expect(mockClient.getOwnedObjects).toHaveBeenCalledWith({
        owner: validAddress,
        options: {
          showContent: false,
        },
        limit: 1,
      });
    });

    it('should return true for address with objects', async () => {
      // Arrange
      const mockObjects = {
        data: [{ objectId: '0x123' }],
        nextCursor: null,
        hasNextPage: false,
      };
      mockClient.getOwnedObjects.mockResolvedValue(mockObjects);

      // Act
      const result = await service.validateAddress(validAddress);

      // Assert
      expect(result).toBe(true);
    });

    it('should return false for invalid address', async () => {
      // Arrange
      mockClient.getOwnedObjects.mockRejectedValue(
        new Error('Invalid address'),
      );

      // Act
      const result = await service.validateAddress('invalid');

      // Assert
      expect(result).toBe(false);
    });

    it('should return false on any error', async () => {
      // Arrange
      mockClient.getOwnedObjects.mockRejectedValue('String error');

      // Act
      const result = await service.validateAddress(validAddress);

      // Assert
      expect(result).toBe(false);
    });
  });
});
