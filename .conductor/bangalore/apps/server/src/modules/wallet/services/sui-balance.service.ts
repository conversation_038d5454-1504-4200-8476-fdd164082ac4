import { logger } from '@hopfun/logger';
import type { CoinBalance } from '@mysten/sui/client';
import { SuiClient } from '@mysten/sui/client';

import type {
  CoinDetails,
  ISuiBalanceService,
  SuiBalance,
  WalletConfig,
} from '../interfaces/wallet.interfaces';

export class SuiBalanceService implements ISuiBalanceService {
  private client: SuiClient;
  private config: WalletConfig;

  constructor(config?: Partial<WalletConfig>) {
    this.config = {
      suiRpcUrl: config?.suiRpcUrl ?? process.env.SUI_RPC_URL ?? '',
      network: (config?.network ?? process.env.NETWORK ?? 'TESTNET') as
        | 'MAINNET'
        | 'TESTNET',
    };

    if (!this.config.suiRpcUrl) {
      throw new Error('SUI_RPC_URL is required');
    }

    this.client = new SuiClient({ url: this.config.suiRpcUrl });

    logger.info(
      {
        rpcUrl: this.config.suiRpcUrl,
        network: this.config.network,
      },
      'Initialized SUI balance service',
    );
  }

  async getBalance(address: string): Promise<SuiBalance> {
    const startTime = Date.now();

    try {
      logger.debug({ address }, 'Fetching SUI balance');

      // Get SUI balance (native token)
      const balance = await this.client.getBalance({
        owner: address,
        coinType: '0x2::sui::SUI',
      });

      const result: SuiBalance = {
        totalBalance: balance.totalBalance,
        coinObjectCount: balance.coinObjectCount,
      };

      // Check if there are locked balances
      if (Object.keys(balance.lockedBalance).length > 0) {
        result.lockedBalance = balance.lockedBalance;
      }

      const duration = Date.now() - startTime;
      logger.info(
        {
          address,
          totalBalance: balance.totalBalance,
          coinObjectCount: balance.coinObjectCount,
          duration,
        },
        'Successfully fetched SUI balance',
      );

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          address,
          error: errorMessage,
          duration,
        },
        'Failed to fetch SUI balance',
      );

      throw new Error(`Failed to fetch balance: ${errorMessage}`);
    }
  }

  async getCoins(address: string): Promise<CoinDetails[]> {
    const startTime = Date.now();

    try {
      logger.debug({ address }, 'Fetching coin details');

      // Get all coins owned by the address
      const coins = await this.client.getCoins({
        owner: address,
        coinType: '0x2::sui::SUI',
      });

      const coinDetails: CoinDetails[] = coins.data.map((coin) => ({
        coinType: coin.coinType,
        coinObjectId: coin.coinObjectId,
        version: coin.version,
        digest: coin.digest,
        balance: coin.balance,
        previousTransaction: coin.previousTransaction,
      }));

      const duration = Date.now() - startTime;
      logger.info(
        {
          address,
          coinCount: coinDetails.length,
          duration,
        },
        'Successfully fetched coin details',
      );

      return coinDetails;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          address,
          error: errorMessage,
          duration,
        },
        'Failed to fetch coin details',
      );

      throw new Error(`Failed to fetch coins: ${errorMessage}`);
    }
  }

  async getAllBalances(address: string): Promise<CoinBalance[]> {
    const startTime = Date.now();

    try {
      logger.debug({ address }, 'Fetching all coin balances');

      // Get balances for all coin types
      const balances = await this.client.getAllBalances({
        owner: address,
      });

      const duration = Date.now() - startTime;
      logger.info(
        {
          address,
          coinTypeCount: balances.length,
          duration,
        },
        'Successfully fetched all coin balances',
      );

      return balances;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          address,
          error: errorMessage,
          duration,
        },
        'Failed to fetch all balances',
      );

      throw new Error(`Failed to fetch all balances: ${errorMessage}`);
    }
  }

  async validateAddress(address: string): Promise<boolean> {
    try {
      // Check if the address exists by trying to get its objects
      const _objects = await this.client.getOwnedObjects({
        owner: address,
        options: {
          showContent: false,
        },
        limit: 1,
      });

      // Even if an address has no objects, the request will succeed
      // Invalid addresses will throw an error
      return true;
    } catch (error) {
      logger.debug(
        {
          address,
          error: error instanceof Error ? error.message : String(error),
        },
        'Address validation failed',
      );
      return false;
    }
  }
}
