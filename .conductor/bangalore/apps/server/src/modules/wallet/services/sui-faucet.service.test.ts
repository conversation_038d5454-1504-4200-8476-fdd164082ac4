import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { SuiFaucetService } from './sui-faucet.service';

vi.mock('@mysten/sui/faucet', () => ({
  getFaucetHost: vi.fn((network: string) => `https://faucet.${network}.sui.io`),
  requestSuiFromFaucetV2: vi.fn(),
}));

vi.mock('@hopfun/logger', () => ({
  logger: {
    info: vi.fn(),
    debug: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}));

describe('SuiFaucetService', () => {
  let service: SuiFaucetService;
  const mockAddress =
    '0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef';

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with TESTNET by default', () => {
      service = new SuiFaucetService('TESTNET');
      expect(service).toBeDefined();
    });

    it('should initialize with DEVNET when specified', () => {
      service = new SuiFaucetService('DEVNET');
      expect(service).toBeDefined();
    });

    it('should default to DEVNET for unknown network', () => {
      service = new SuiFaucetService('UNKNOWN');
      expect(service).toBeDefined();
    });
  });

  describe('requestFaucet', () => {
    it('should successfully request faucet tokens', async () => {
      const { requestSuiFromFaucetV2 } = await import('@mysten/sui/faucet');
      const mockResult = {
        status: 'Success' as const,
        coins_sent: [
          {
            id: '0xabc123',
            transferTxDigest: 'digest123',
            amount: 1000000000,
          },
          {
            id: '0xdef456',
            transferTxDigest: 'digest456',
            amount: 2000000000,
          },
        ],
      };

      vi.mocked(requestSuiFromFaucetV2).mockResolvedValue(mockResult);

      service = new SuiFaucetService('TESTNET');
      const result = await service.requestFaucet(mockAddress);

      expect(result).toEqual({
        transferredGasObjects: [
          {
            id: '0xabc123',
            transferTxDigest: 'digest123',
            amount: 1000000000,
          },
          {
            id: '0xdef456',
            transferTxDigest: 'digest456',
            amount: 2000000000,
          },
        ],
        error: null,
      });
      expect(requestSuiFromFaucetV2).toHaveBeenCalledWith({
        host: 'https://faucet.testnet.sui.io',
        recipient: mockAddress,
      });
    });

    it('should throw error for Mainnet network', async () => {
      service = new SuiFaucetService('MAINNET');

      await expect(service.requestFaucet(mockAddress)).rejects.toThrow(
        'Faucet is not available on Mainnet',
      );
    });

    it('should handle faucet error response', async () => {
      const { requestSuiFromFaucetV2 } = await import('@mysten/sui/faucet');
      const mockResult = {
        status: 'Success' as const,
        coins_sent: [],
      };

      vi.mocked(requestSuiFromFaucetV2).mockResolvedValue(mockResult);

      service = new SuiFaucetService('TESTNET');

      await expect(service.requestFaucet(mockAddress)).rejects.toThrow(
        'Faucet request failed: No coins received',
      );
    });

    it('should handle rate limit error', async () => {
      const { requestSuiFromFaucetV2 } = await import('@mysten/sui/faucet');
      vi.mocked(requestSuiFromFaucetV2).mockRejectedValue(
        new Error('Request limit exceeded'),
      );

      service = new SuiFaucetService('TESTNET');

      await expect(service.requestFaucet(mockAddress)).rejects.toThrow(
        'Faucet request limit exceeded. Please wait a few minutes before trying again.',
      );
    });

    it('should handle invalid address error', async () => {
      const { requestSuiFromFaucetV2 } = await import('@mysten/sui/faucet');
      vi.mocked(requestSuiFromFaucetV2).mockRejectedValue(
        new Error('Invalid address'),
      );

      service = new SuiFaucetService('TESTNET');

      await expect(service.requestFaucet('invalid')).rejects.toThrow(
        'Invalid address format: invalid',
      );
    });

    it('should handle network unavailable error', async () => {
      const { requestSuiFromFaucetV2 } = await import('@mysten/sui/faucet');
      vi.mocked(requestSuiFromFaucetV2).mockRejectedValue(
        new Error('Faucet not available'),
      );

      service = new SuiFaucetService('TESTNET');

      await expect(service.requestFaucet(mockAddress)).rejects.toThrow(
        'Faucet not available',
      );
    });

    it('should handle unknown errors', async () => {
      const { requestSuiFromFaucetV2 } = await import('@mysten/sui/faucet');
      vi.mocked(requestSuiFromFaucetV2).mockRejectedValue('Unknown error');

      service = new SuiFaucetService('TESTNET');

      await expect(service.requestFaucet(mockAddress)).rejects.toThrow(
        'Unknown error occurred while requesting faucet',
      );
    });

    it('should work with DEVNET network', async () => {
      const { requestSuiFromFaucetV2 } = await import('@mysten/sui/faucet');
      const mockResult = {
        status: 'Success' as const,
        coins_sent: [
          {
            id: '0xabc123',
            transferTxDigest: 'digest123',
            amount: 1000000000,
          },
        ],
      };

      vi.mocked(requestSuiFromFaucetV2).mockResolvedValue(mockResult);

      service = new SuiFaucetService('DEVNET');
      const result = await service.requestFaucet(mockAddress);

      expect(result).toEqual({
        transferredGasObjects: [
          {
            id: '0xabc123',
            transferTxDigest: 'digest123',
            amount: 1000000000,
          },
        ],
        error: null,
      });
      expect(requestSuiFromFaucetV2).toHaveBeenCalledWith({
        host: 'https://faucet.devnet.sui.io',
        recipient: mockAddress,
      });
    });
  });
});
