import dotenv from 'dotenv';
import path from 'path';
import { afterAll, beforeAll, beforeEach, vi } from 'vitest';

// Load test environment variables
dotenv.config({
  path: path.resolve(process.cwd(), '.env.test'),
});

// Set default test environment variables
process.env.NODE_ENV = 'test';
process.env.NETWORK = 'TESTNET';
process.env.SUI_RPC_URL =
  process.env.SUI_RPC_URL ?? 'https://fullnode.testnet.sui.io:443';
process.env.HOPFUN_PACKAGE_ID =
  process.env.HOPFUN_PACKAGE_ID ?? '0xtest_package';
process.env.MEME_CONFIG_ID = process.env.MEME_CONFIG_ID ?? '0xtest_config';

// Mock logger to reduce noise in tests
vi.mock('@hopfun/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
}));

beforeAll(() => {
  // Setup any global test fixtures
});

afterAll(() => {
  // Cleanup any global test fixtures
});

beforeEach(() => {
  // Reset mocks before each test
  vi.clearAllMocks();
});
