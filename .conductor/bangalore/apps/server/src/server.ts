import { serve } from '@hono/node-server';
import { logger } from '@hopfun/logger';

import { app } from '@/app';
import { Env } from '@/configs/env';
import { db } from '@/libs/database';

const connectDatabase = async () => {
  try {
    logger.info('🔗 Connecting to the database...');
    await db.$connect();
    logger.info('📚 Database connection established successfully');
  } catch (error) {
    logger.error({ error }, '❌ Failed to connect to the database');
    process.exit(1);
  }
};

const startServer = async () => {
  Env.validate();
  await connectDatabase();

  return serve(
    {
      fetch: app.fetch,
      port: Env.get('PORT'),
    },
    (info) => {
      logger.info(
        {
          context: 'server-startup',
          port: info.port,
          env: Env.get('NODE_ENV'),
        },
        `🚀 Server is running on http://localhost:${info.port}`,
      );
    },
  );
};

// Start the server
const server = await startServer();

// Graceful shutdown handling
const gracefulShutdown = async (signal: string) => {
  logger.info(`🔌 Received ${signal}, shutting down server...`);

  // Set a timeout to force exit if shutdown takes too long
  const forceExitTimeout = setTimeout(() => {
    logger.error('❌ Forced shutdown due to timeout');
    process.exit(1);
  }, 10000); // 10 seconds timeout

  try {
    // Close the server first to stop accepting new connections
    await new Promise<void>((resolve, reject) => {
      server.close((err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });

    logger.info('🌐 HTTP server closed');

    // Then disconnect from the database
    await db.$disconnect();
    logger.info('📚 Database disconnected');

    clearTimeout(forceExitTimeout);
    logger.info('✅ Server shut down gracefully');
    process.exit(0);
  } catch (error) {
    clearTimeout(forceExitTimeout);
    logger.error({ error }, '❌ Error during shutdown');
    process.exit(1);
  }
};

process.on('SIGINT', () => {
  gracefulShutdown('SIGINT').catch((err) => {
    logger.error({ err }, '❌ Error during SIGINT shutdown');
    process.exit(1);
  });
});
process.on('SIGTERM', () => {
  gracefulShutdown('SIGTERM').catch((err) => {
    logger.error({ err }, '❌ Error during SIGTERM shutdown');
    process.exit(1);
  });
});

process.on('uncaughtException', (error) => {
  logger.error({ error }, '❌ Uncaught exception');
  gracefulShutdown('uncaughtException').catch((err) => {
    logger.error({ err }, '❌ Error during uncaughtException shutdown');
    process.exit(1);
  });
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error({ reason, promise }, '❌ Unhandled promise rejection');
  gracefulShutdown('unhandledRejection').catch((err) => {
    logger.error({ err }, '❌ Error during unhandledRejection shutdown');
    process.exit(1);
  });
});
