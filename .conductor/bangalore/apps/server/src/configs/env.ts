import 'dotenv/config';

import { z } from 'zod';

const ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test',
} as const;

export type Environment = (typeof ENVIRONMENTS)[keyof typeof ENVIRONMENTS];

const envSchema = z.object({
  PORT: z.coerce.number().default(3000),
  NODE_ENV: z.enum(Object.values(ENVIRONMENTS)).default('development'),
  DATABASE_URL: z.url(),
});

type EnvType = z.infer<typeof envSchema>;

class EnvValidator {
  private static validated: EnvType | null = null;

  private static getValidatedEnv(): EnvType {
    this.validated ??= this.validate();
    return this.validated;
  }

  static get<K extends keyof EnvType>(key: K): EnvType[K] {
    const env = this.getValidatedEnv();
    return env[key];
  }

  static get isDevelopment(): boolean {
    return this.get('NODE_ENV') === ENVIRONMENTS.DEVELOPMENT;
  }

  static get isProduction(): boolean {
    return this.get('NODE_ENV') === ENVIRONMENTS.PRODUCTION;
  }

  static get isTest(): boolean {
    return this.get('NODE_ENV') === ENVIRONMENTS.TEST;
  }

  static validate(): EnvType {
    try {
      return envSchema.parse(process.env);
    } catch (error) {
      throw new Error('Invalid environment configuration', {
        cause: z.flattenError(error as z.ZodError),
      });
    }
  }
}

export { EnvValidator as Env };
