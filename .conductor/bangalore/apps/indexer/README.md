# HopFun Indexer

A comprehensive, high-performance indexer for the HopFun meme coin trading platform on Sui blockchain. This indexer monitors smart contract events, processes trading data, and provides real-time aggregations and analytics.

## Features

- **Real-time Event Processing**: Monitors all HopFun smart contract events
- **Multi-Network Support**: Supports both MAINNET and TESTNET
- **Comprehensive Data Models**: Tracks tokens, transactions, users, and statistics
- **Automatic Retry Logic**: Robust error handling with exponential backoff
- **Performance Optimizations**: Batch processing, connection pooling, and aggregations
- **Health Monitoring**: Built-in health checks and metrics endpoints
- **Scalable Architecture**: Event-driven design with configurable concurrency

## Architecture

### Core Components

1. **Sui Client Manager**: Handles blockchain connections and event subscriptions
2. **Event Processing Engine**: Orchestrates event processing with retry logic
3. **Event Processors**: Specialized handlers for each event type
4. **Aggregation Service**: Real-time statistics and data aggregations
5. **HTTP Server**: Health checks and metrics endpoints

### Event Types Supported

- `ConnectorCreated`: New token connector creation
- `BondingCurveCreated`: New meme coin launch
- `BondingCurveBuy`: Token purchase transactions
- `BondingCurveSell`: Token sale transactions
- `BondingCurveComplete`: Bonding curve completion
- `BondingCurveMigrate`: Token migration to DEX

### Database Schema

The indexer uses MongoDB with the following key models:

- **Raw Events**: Original blockchain events for audit trail
- **Tokens**: Aggregated token information and current state
- **TokenStats**: Real-time statistics (volume, transactions, holders)
- **UserHoldings**: Individual user token balances and P&L
- **TokenTransactions**: Processed transaction data
- **UserStats**: Aggregated user trading statistics

## Quick Start

### Prerequisites

- Node.js 18+
- pnpm
- MongoDB database
- Sui network access (RPC endpoint)

### Installation

1. **Setup the indexer**:
   ```bash
   # Run the setup script
   ./scripts/setup.sh
   ```

2. **Configure environment**:
   ```bash
   # Edit .env file with your configuration
   cp .env.example .env
   # Update the following variables:
   # - DATABASE_URL
   # - SUI_RPC_URL
   # - HOPFUN_PACKAGE_ID
   # - HOPDEX_PACKAGE_ID
   ```

3. **Start the indexer**:
   ```bash
   # Development mode
   pnpm dev
   
   # Production mode
   pnpm build && pnpm start
   ```

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NETWORK` | Blockchain network (MAINNET/TESTNET) | TESTNET |
| `DATABASE_URL` | MongoDB connection string | Required |
| `SUI_RPC_URL` | Sui RPC endpoint | Required |
| `SUI_WS_URL` | Sui WebSocket endpoint | Optional |
| `HOPFUN_PACKAGE_ID` | HopFun contract package ID | Required |
| `HOPDEX_PACKAGE_ID` | HopDex contract package ID | Required |
| `START_CHECKPOINT` | Starting checkpoint for indexing | Optional |
| `BATCH_SIZE` | Event processing batch size | 100 |
| `PROCESSING_DELAY_MS` | Delay between processing batches | 1000 |
| `MAX_RETRIES` | Maximum retry attempts | 3 |
| `HEALTH_CHECK_PORT` | HTTP server port | 3001 |
| `LOG_LEVEL` | Logging level | info |

## API Endpoints

The indexer provides several HTTP endpoints for monitoring:

### Health Check
```bash
GET /health
```
Returns detailed health status including:
- Service running status
- Sui client connection
- Processing metrics
- Queue status
- System information

### Metrics
```bash
GET /metrics
```
Returns comprehensive metrics:
- Processing statistics
- Queue status
- Sui client info
- System metrics

### Status
```bash
GET /status
```
Simple status check for load balancers.

## Development

### Project Structure

```
src/
├── config/          # Environment configuration
├── processors/      # Event processors
│   ├── base.ts      # Base processor class
│   ├── engine.ts    # Processing engine
│   └── *.ts         # Individual event processors
├── services/        # Background services
│   └── aggregation.ts # Statistics aggregation
├── sui/             # Sui blockchain integration
│   ├── client.ts    # Sui client manager
│   └── events.ts    # Event definitions
├── http-server.ts   # HTTP API server
└── server.ts        # Main indexer service
```

### Adding New Event Processors

1. Create a new processor extending `BaseEventProcessor`:
   ```typescript
   import { BaseEventProcessor } from './base';
   
   export class MyEventProcessor extends BaseEventProcessor {
     getEventType(): string {
       return 'MyEvent';
     }
     
     async process(context: ProcessingContext): Promise<ProcessingResult> {
       // Implementation
     }
   }
   ```

2. Register the processor in `server.ts`:
   ```typescript
   this.processingEngine.registerProcessor(
     'MyEvent',
     new MyEventProcessor(env.NETWORK)
   );
   ```

### Database Migrations

The indexer automatically handles database schema through Prisma:

```bash
# Generate Prisma client
cd packages/database
pnpm db:generate

# Format schema
pnpm db:format

# Validate schema
pnpm db:validate
```

## Monitoring

### Logs

The indexer uses structured logging with different levels:
- `error`: Critical errors requiring attention
- `warn`: Warnings and recoverable errors
- `info`: General information and metrics
- `debug`: Detailed debugging information

### Metrics

Key metrics tracked:
- Events processed per minute
- Processing latency
- Error rates
- Queue depth
- Memory usage
- Database connection health

### Alerts

Recommended alerting thresholds:
- Error rate > 5%
- Processing delay > 30 seconds
- Queue depth > 1000 events
- Memory usage > 80%

## Performance Tuning

### Configuration Options

- **Concurrency**: Adjust `concurrency` in EventProcessingEngine
- **Batch Size**: Tune `BATCH_SIZE` for optimal throughput
- **Processing Delay**: Adjust `PROCESSING_DELAY_MS` for rate limiting
- **Connection Pooling**: Configure database connection pools

### Scaling Considerations

- **Horizontal Scaling**: Run multiple indexer instances with different event filters
- **Database Sharding**: Shard by network or time periods
- **Caching**: Implement Redis for frequently accessed data
- **Queue Management**: Use external queue systems for high load

## Troubleshooting

### Common Issues

1. **Connection Errors**:
   ```bash
   # Check Sui network connectivity
   curl -X POST $SUI_RPC_URL -H "Content-Type: application/json" \
     -d '{"jsonrpc":"2.0","id":1,"method":"sui_getLatestCheckpointSequenceNumber"}'
   ```

2. **Database Issues**:
   ```bash
   # Test MongoDB connection
   mongosh $DATABASE_URL --eval "db.adminCommand('ping')"
   ```

3. **Event Processing Delays**:
   - Check queue status via `/metrics` endpoint
   - Monitor error logs for processing failures
   - Verify network connectivity and RPC limits

### Debug Mode

Enable debug logging:
```bash
LOG_LEVEL=debug pnpm dev
```

## Contributing

1. Follow the existing code style and patterns
2. Add tests for new functionality
3. Update documentation for API changes
4. Ensure proper error handling and logging

## License

This project is part of the HopFun platform.
