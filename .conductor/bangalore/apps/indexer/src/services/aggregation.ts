import { createPrismaClient } from '@hopfun/database';
import { logger } from '@hopfun/logger';
import { ulid } from 'ulid';

import type { Network } from '@/config/environment';
import { env } from '@/config/environment';

export interface AggregationJob {
  name: string;
  schedule: string; // cron expression
  handler: () => Promise<void>;
}

export class AggregationService {
  private db = createPrismaClient(env.DATABASE_URL);
  private network: Network;
  private jobs = new Map<string, NodeJS.Timeout>();
  private isRunning = false;

  constructor(network: Network = env.NETWORK) {
    this.network = network;
  }

  start(): void {
    if (this.isRunning) {
      logger.warn({}, 'Aggregation service already running');
      return;
    }

    this.isRunning = true;
    this.scheduleJobs();

    logger.info(
      {
        network: this.network,
        jobs: Array.from(this.jobs.keys()),
      },
      'Aggregation service started',
    );
  }

  stop(): void {
    if (!this.isRunning) {
      return;
    }

    // Clear all scheduled jobs
    for (const [name, timeout] of this.jobs) {
      clearInterval(timeout);
      logger.debug({ name }, 'Stopped aggregation job');
    }

    this.jobs.clear();
    this.isRunning = false;

    logger.info({ network: this.network }, 'Aggregation service stopped');
  }

  private scheduleJobs(): void {
    // Update 24h statistics every 5 minutes
    this.scheduleJob('update-24h-stats', 5 * 60 * 1000, () =>
      this.update24hStats(),
    );

    // Update token market caps every minute
    this.scheduleJob('update-market-caps', 60 * 1000, () =>
      this.updateMarketCaps(),
    );

    // Clean up old processing queue entries every hour
    this.scheduleJob('cleanup-queue', 60 * 60 * 1000, () =>
      this.cleanupProcessingQueue(),
    );

    // Update user statistics every 10 minutes
    this.scheduleJob('update-user-stats', 10 * 60 * 1000, () =>
      this.updateUserStats(),
    );

    // Update holder counts every 15 minutes
    this.scheduleJob('update-holder-counts', 15 * 60 * 1000, () =>
      this.updateHolderCounts(),
    );
  }

  private scheduleJob(
    name: string,
    intervalMs: number,
    handler: () => Promise<void>,
  ): void {
    const timeout = setInterval(async () => {
      try {
        const startTime = Date.now();
        await handler();
        const duration = Date.now() - startTime;

        logger.debug(
          {
            name,
            durationMs: duration,
            network: this.network,
          },
          'Aggregation job completed',
        );
      } catch (error) {
        logger.error(
          {
            name,
            error: error instanceof Error ? error.message : String(error),
            network: this.network,
          },
          'Aggregation job failed',
        );
      }
    }, intervalMs);

    this.jobs.set(name, timeout);
    logger.debug({ name, intervalMs }, 'Scheduled aggregation job');
  }

  private async update24hStats(): Promise<void> {
    const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    // Get all tokens for this network
    const tokens = await this.db.token.findMany({
      where: { network: this.network },
      include: { stats: true },
    });

    for (const token of tokens) {
      try {
        // Get 24h transactions
        const transactions24h = await this.db.tokenTransaction.findMany({
          where: {
            tokenId: token.id,
            createdAt: { gte: twentyFourHoursAgo },
          },
        });

        // Calculate aggregations
        let volume24h = BigInt(0);
        let buyVolume24h = BigInt(0);
        let sellVolume24h = BigInt(0);
        let buyCount24h = 0;
        let sellCount24h = 0;
        const uniqueTraders = new Set<string>();

        let priceHigh24h = BigInt(0);
        let priceLow24h = BigInt(token.currentPrice);

        for (const tx of transactions24h) {
          const suiAmount = BigInt(tx.suiAmount);
          const price = BigInt(tx.postPrice);

          volume24h += suiAmount;
          uniqueTraders.add(tx.sender);

          if (tx.eventType === 'BUY') {
            buyVolume24h += suiAmount;
            buyCount24h++;
          } else {
            sellVolume24h += suiAmount;
            sellCount24h++;
          }

          // Track price range
          if (price > priceHigh24h) priceHigh24h = price;
          if (price < priceLow24h) priceLow24h = price;
        }

        // Calculate price change
        const currentPrice = BigInt(token.currentPrice);
        const oldestTx = transactions24h.sort(
          (a, b) => a.createdAt.getTime() - b.createdAt.getTime(),
        )[0];

        const price24hAgo =
          typeof oldestTx !== 'undefined'
            ? BigInt(oldestTx.prePrice)
            : currentPrice;
        const priceChange24h = currentPrice - price24hAgo;
        const priceChangePercent24h =
          price24hAgo > BigInt(0)
            ? (priceChange24h * BigInt(10000)) / price24hAgo
            : BigInt(0);

        // Update token stats
        if (token.stats) {
          await this.db.tokenStats.update({
            where: { id: token.stats.id },
            data: {
              volume24h: volume24h.toString(),
              buyVolume24h: buyVolume24h.toString(),
              sellVolume24h: sellVolume24h.toString(),
              transactions24h: transactions24h.length,
              buyCount24h,
              sellCount24h,
              uniqueHolders24h: uniqueTraders.size,
              priceHigh24h: priceHigh24h.toString(),
              priceLow24h: priceLow24h.toString(),
              priceChange24h: priceChange24h.toString(),
              priceChangePercent24h: priceChangePercent24h.toString(),
              lastUpdated: new Date(),
            },
          });
        }
      } catch (error) {
        logger.error(
          {
            tokenId: token.id,
            curveId: token.curveId,
            error: error instanceof Error ? error.message : String(error),
          },
          'Failed to update 24h stats for token',
        );
      }
    }
  }

  private async updateMarketCaps(): Promise<void> {
    const tokens = await this.db.token.findMany({
      where: { network: this.network },
    });

    for (const token of tokens) {
      try {
        // Calculate market cap = total supply * current price
        const totalSupply = BigInt(token.totalSupply);
        const currentPrice = BigInt(token.currentPrice);
        const marketCap = (totalSupply * currentPrice) / BigInt(1000000000);

        await this.db.token.update({
          where: { id: token.id },
          data: {
            marketCap: marketCap.toString(),
            updatedAt: new Date(),
          },
        });
      } catch (error) {
        logger.error(
          {
            tokenId: token.id,
            error: error instanceof Error ? error.message : String(error),
          },
          'Failed to update market cap for token',
        );
      }
    }
  }

  private async cleanupProcessingQueue(): Promise<void> {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const deleted = await this.db.processingQueue.deleteMany({
      where: {
        network: this.network,
        processed: true,
        processedAt: { lt: oneDayAgo },
      },
    });

    logger.info(
      {
        deletedCount: deleted.count,
        network: this.network,
      },
      'Cleaned up processing queue',
    );
  }

  private async updateUserStats(): Promise<void> {
    // Get all users with holdings in this network
    const users = await this.db.userHolding.groupBy({
      by: ['userAddress'],
      where: { network: this.network },
      _count: true,
    });

    for (const user of users) {
      try {
        // Get user's holdings
        const holdings = await this.db.userHolding.findMany({
          where: {
            userAddress: user.userAddress,
            network: this.network,
          },
        });

        // Get user's transactions
        const transactions = await this.db.tokenTransaction.findMany({
          where: {
            sender: user.userAddress,
            network: this.network,
          },
        });

        // Calculate stats
        let totalInvested = BigInt(0);
        let totalRealizedPnl = BigInt(0);
        let totalTokensOwned = 0;
        let totalVolumeTraded = BigInt(0);
        let buyCount = 0;
        let sellCount = 0;

        for (const holding of holdings) {
          totalInvested += BigInt(holding.totalInvested);
          totalRealizedPnl += BigInt(holding.realizedPnl);
          if (BigInt(holding.tokenAmount) > BigInt(0)) {
            totalTokensOwned++;
          }
        }

        for (const tx of transactions) {
          totalVolumeTraded += BigInt(tx.suiAmount);
          if (tx.eventType === 'BUY') buyCount++;
          else sellCount++;
        }

        // Get 24h volume
        const twentyFourHoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
        const transactions24h = await this.db.tokenTransaction.findMany({
          where: {
            sender: user.userAddress,
            network: this.network,
            createdAt: { gte: twentyFourHoursAgo },
          },
        });

        let volume24h = BigInt(0);
        for (const tx of transactions24h) {
          volume24h += BigInt(tx.suiAmount);
        }

        const firstTrade = transactions.sort(
          (a, b) => a.createdAt.getTime() - b.createdAt.getTime(),
        )[0];

        // Upsert user stats
        await this.db.userStats.upsert({
          where: { userAddress: user.userAddress },
          update: {
            totalInvested: totalInvested.toString(),
            totalRealizedPnl: totalRealizedPnl.toString(),
            totalTokensOwned,
            totalTrades: transactions.length,
            buyCount,
            sellCount,
            totalVolumeTraded: totalVolumeTraded.toString(),
            volume24h: volume24h.toString(),
            lastActivity: new Date(),
          },
          create: {
            id: ulid(),
            userAddress: user.userAddress,
            network: this.network,
            totalInvested: totalInvested.toString(),
            totalRealizedPnl: totalRealizedPnl.toString(),
            totalTokensOwned,
            totalTrades: transactions.length,
            buyCount,
            sellCount,
            totalVolumeTraded: totalVolumeTraded.toString(),
            volume24h: volume24h.toString(),
            firstTrade: firstTrade.createdAt,
            lastActivity: new Date(),
          },
        });
      } catch (error) {
        logger.error(
          {
            userAddress: user.userAddress,
            error: error instanceof Error ? error.message : String(error),
          },
          'Failed to update user stats',
        );
      }
    }
  }

  private async updateHolderCounts(): Promise<void> {
    const tokens = await this.db.token.findMany({
      where: { network: this.network },
      include: { stats: true },
    });

    for (const token of tokens) {
      try {
        const holderCount = await this.db.userHolding.count({
          where: {
            tokenId: token.id,
            tokenAmount: { gt: '0' },
          },
        });

        if (token.stats) {
          await this.db.tokenStats.update({
            where: { id: token.stats.id },
            data: {
              totalHolders: holderCount,
              lastUpdated: new Date(),
            },
          });
        }
      } catch (error) {
        logger.error(
          {
            tokenId: token.id,
            error: error instanceof Error ? error.message : String(error),
          },
          'Failed to update holder count for token',
        );
      }
    }
  }

  async cleanup(): Promise<void> {
    this.stop();
    await this.db.$disconnect();
  }
}

export default AggregationService;
