/* eslint-disable max-params */
import { logger } from '@hopfun/logger';
import express from 'express';

import { env } from './config/environment';
import type HopfunIndexer from './server';

export class IndexerHttpServer {
  private app = express();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private server: any;
  private indexer: HopfunIndexer;

  constructor(indexer: HopfunIndexer) {
    this.indexer = indexer;
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    this.app.use(express.json());
    this.app.use((req, res, next) => {
      logger.debug(
        {
          method: req.method,
          url: req.url,
          userAgent: req.get('User-Agent'),
        },
        'HTTP request',
      );
      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      try {
        const health = this.indexer.getHealthStatus();
        const statusCode =
          health.isRunning && health.suiClientConnected ? 200 : 503;

        res.status(statusCode).json({
          status: statusCode === 200 ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString(),
          ...health,
        });
      } catch (error) {
        logger.error({ error }, 'Health check error');
        res.status(500).json({
          status: 'error',
          timestamp: new Date().toISOString(),
          error: error instanceof Error ? error.message : String(error),
        });
      }
    });

    // Metrics endpoint
    this.app.get('/metrics', (req, res) => {
      try {
        const metrics = this.indexer.getMetrics();
        res.json({
          timestamp: new Date().toISOString(),
          network: env.NETWORK,
          ...metrics,
        });
      } catch (error) {
        logger.error({ error }, 'Metrics endpoint error');
        res.status(500).json({
          error: error instanceof Error ? error.message : String(error),
        });
      }
    });

    // Status endpoint (simple check)
    this.app.get('/status', (req, res) => {
      res.json({
        status: 'running',
        timestamp: new Date().toISOString(),
        network: env.NETWORK,
        version: process.env.npm_package_version ?? '1.0.0',
      });
    });

    // Graceful shutdown endpoint (for development)
    if (env.NODE_ENV === 'development') {
      this.app.post('/shutdown', (req, res) => {
        logger.info({}, 'Shutdown requested via HTTP endpoint');
        res.json({ message: 'Shutting down...' });

        // Give response time to send before shutting down
        setTimeout(() => {
          void this.indexer.stop();
        }, 100);
      });
    }

    // 404 handler
    this.app.use((req, res) => {
      res.status(404).json({
        error: 'Not found',
        path: req.originalUrl,
      });
    });

    // Error handler
    this.app.use(
      (
        error: Error,
        req: express.Request,
        res: express.Response,
        _next: express.NextFunction,
      ) => {
        logger.error(
          {
            error: error.message,
            stack: error.stack,
            url: req.url,
            method: req.method,
          },
          'HTTP server error',
        );

        res.status(500).json({
          error: 'Internal server error',
          timestamp: new Date().toISOString(),
        });
      },
    );
  }

  start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const port = env.HEALTH_CHECK_PORT;

        this.server = this.app.listen(port, () => {
          logger.info(
            {
              port,
              endpoints: ['/health', '/metrics', '/status'],
            },
            'HTTP server started',
          );
          resolve();
        });

        this.server.on('error', (error: Error) => {
          logger.error({ error: error.message }, 'HTTP server error');
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          logger.info({}, 'HTTP server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

export default IndexerHttpServer;
