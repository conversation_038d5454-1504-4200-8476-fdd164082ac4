import { logger } from '@hopfun/logger';
import type { SuiEvent } from '@mysten/sui/client';
import { ulid } from 'ulid';

import type { Network } from '@/config/environment';
import { EventValidator } from '@/sui/events';

import type { ProcessingContext, ProcessingResult } from './base';
import { BaseEventProcessor } from './base';

export class ConnectorCreatedProcessor extends BaseEventProcessor {
  constructor(network: Network) {
    super(network);
  }

  getEventType(): string {
    return 'ConnectorCreated';
  }

  protected validateEvent(event: SuiEvent): boolean {
    return (
      event.parsedJson != null &&
      typeof event.parsedJson === 'object' &&
      EventValidator.isValidConnectorCreatedEvent(
        event.parsedJson as Record<string, unknown>,
      )
    );
  }

  protected async checkEventSpecificProcessing(
    event: SuiEvent,
  ): Promise<boolean> {
    try {
      const existingEvent = await this.db.connectorCreatedEvent.findUnique({
        where: {
          transactionId: event.id.txDigest,
        },
      });

      return !!existingEvent;
    } catch (error) {
      logger.error(
        {
          eventId: event.id,
          error: error instanceof Error ? error.message : String(error),
        },
        'Error checking connector created event processing',
      );
      return false;
    }
  }

  async process(context: ProcessingContext): Promise<ProcessingResult> {
    const { event, network } = context;

    if (
      !event.parsedJson ||
      !EventValidator.isValidConnectorCreatedEvent(event.parsedJson)
    ) {
      return {
        success: false,
        error: 'Invalid ConnectorCreated event data',
        shouldRetry: false,
      };
    }

    const eventData = event.parsedJson;

    try {
      await this.withTransaction(async (tx) => {
        // Store the raw connector created event
        await tx.connectorCreatedEvent.create({
          data: {
            id: ulid(),
            connectorId: eventData.connector_id,
            transactionId: event.id.txDigest,
            createdAt: new Date(Number(event.timestampMs)),
            package: event.packageId,
            module: event.type.split('::')[1] || 'events',
            eventType: this.getEventType(),
            network,
          },
        });

        logger.info(
          {
            connectorId: eventData.connector_id,
            transactionId: event.id.txDigest,
            network,
          },
          'Stored ConnectorCreated event',
        );
      });

      // Mark as processed
      await this.markEventProcessed(event);

      return {
        success: true,
        data: {
          connectorId: eventData.connector_id,
          transactionId: event.id.txDigest,
        },
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      logger.error(
        {
          connectorId: eventData.connector_id,
          transactionId: event.id.txDigest,
          error: errorMessage,
          network,
        },
        'Failed to process ConnectorCreated event',
      );

      // Add to processing queue for retry
      await this.addToProcessingQueue(event, errorMessage);

      return {
        success: false,
        error: errorMessage,
        shouldRetry: this.shouldRetryError(error),
      };
    }
  }
}

export default ConnectorCreatedProcessor;
