#!/bin/bash

# HopFun Indexer Setup Script
set -e

echo "🚀 Setting up HopFun Indexer..."

# Check if .env exists, if not copy from example
if [ ! -f .env ]; then
    echo "📝 Creating .env file from template..."
    cp .env.example .env
    echo "⚠️  Please edit .env file with your configuration before running the indexer"
fi

# Build the database package
echo "🔨 Building database package..."
cd ../../packages/database
pnpm build

# Go back to indexer directory
cd ../../apps/indexer

# Install dependencies
echo "📦 Installing dependencies..."
pnpm install

# Build the indexer
echo "🔨 Building indexer..."
pnpm build

echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Configure your .env file with:"
echo "   - DATABASE_URL (MongoDB connection string)"
echo "   - SUI_RPC_URL (Sui network RPC endpoint)"
echo "   - HOPFUN_PACKAGE_ID (deployed hopfun contract package ID)"
echo "   - HOPDEX_PACKAGE_ID (deployed hopdex contract package ID)"
echo ""
echo "2. Run the indexer:"
echo "   pnpm dev     # Development mode"
echo "   pnpm start   # Production mode"
echo ""
echo "3. Check health:"
echo "   curl http://localhost:3001/health"