# HopFun Deployment Guide

## Overview

HopFun uses a **Config Registry** pattern to manage contract addresses dynamically. This eliminates circular dependencies and allows address updates without redeployment.

## Architecture

```
     Config Registry (Shared Object)
              ↓
    Stores all addresses
              ↓
    ┌─────────┴─────────┐
    ↓                   ↓
Coin Modules      Frontend App
    ↓
Creates Connector → Sends to MemeConfig
```

## Quick Deployment

### One-Command Deployment
```bash
./scripts/deploy.sh
```

This script automatically:
1. Deploys Config Registry
2. Deploys HopFun and HopDex packages
3. Updates registry with all addresses
4. Updates frontend configurations
5. Builds and extracts bytecode

## Manual Deployment Steps

### Step 1: Deploy Config Registry
```bash
sui client publish contracts/config_registry --gas-budget 100000000
```

Save these IDs:
- Registry Package ID
- Registry ID (ConfigRegistry object)
- Admin Cap ID

### Step 2: Deploy HopFun
```bash
# Set address to 0x0 for fresh deployment
sed -i '' 's/hopfun = "0x[a-f0-9]*"/hopfun = "0x0"/' contracts/hopfun/Move.toml
sui client publish contracts/hopfun --gas-budget 200000000
```

Save:
- HopFun Package ID
- MemeConfig ID

### Step 3: Update Registry
```bash
sui client call \
  --package $REGISTRY_PACKAGE_ID \
  --module registry \
  --function update_all_addresses \
  --args $REGISTRY_ID $ADMIN_CAP_ID $MEME_CONFIG_ID $DEX_CONFIG_ID $HOPFUN_PKG $HOPDEX_PKG \
  --gas-budget 10000000
```

### Step 4: Build Coin Template
```bash
# Build template with registry support
sui move build --path contracts/coin_template

# Extract bytecode for frontend
node scripts/extract-bytecode.js
```

## Frontend Configuration

### Environment Variables (.env.local)
```env
# Network
NEXT_PUBLIC_NETWORK=devnet

# Registry Configuration
NEXT_PUBLIC_REGISTRY_ID_DEVNET=0x...
NEXT_PUBLIC_REGISTRY_PACKAGE_ID_DEVNET=0x...

# Contract IDs (optional, can be read from registry)
NEXT_PUBLIC_HOPFUN_PACKAGE_ID_DEVNET=0x...
NEXT_PUBLIC_MEME_CONFIG_ID_DEVNET=0x...
```

### Network Config Service
The frontend automatically uses the registry if configured:
```typescript
// apps/frontend/src/services/network-config.service.ts
interface ContractConfig {
  hopfunPackageId: string;
  memeConfigId: string;
  hopfunAdminCap: string;
  hopdexPackageId: string;
  hopdexConfigId: string;
  registryId?: string;        // Config Registry ID
  registryPackageId?: string; // Registry Package ID
}
```

## Token Creation Flow

### With Registry (Current Approach)
1. **Publish coin module** - Creates WitnessHolder
2. **Call create_connector** - Passes registry and creates Connector
3. **Create bonding curve** - Uses the Connector

### Transaction Example
```typescript
// Step 1: Publish
const publishTx = new Transaction();
publishTx.publish({
  modules: [bytecodeData.bytecode],
  dependencies: bytecodeData.dependencies,
});

// Step 2: Create connector with registry
const createTx = new Transaction();
createTx.moveCall({
  target: `${packageId}::template::create_connector`,
  arguments: [
    createTx.object(witnessHolderId),
    createTx.object(registryId), // Registry as shared object
  ]
});
```

## Contract Structure

### Config Registry (`contracts/config_registry/`)
- Stores all contract addresses
- Admin-controlled updates
- Single source of truth

### HopFun Connector (`contracts/hopfun/sources/connector.move`)
```move
public fun new<T: drop>(
    // ... parameters
    registry: &ConfigRegistry,  // Takes registry
    ctx: &mut TxContext,
) {
    // ... create connector
    let meme_config_address = registry::get_meme_config_address(registry);
    transfer::public_transfer(connector, meme_config_address);
}
```

### Coin Template (`contracts/coin_template/`)
```move
fun init(witness: TEMPLATE, ctx: &mut TxContext) {
    // Creates WitnessHolder for second transaction
    let witness_holder = WitnessHolder { 
        id: object::new(ctx),
        witness 
    };
    transfer::transfer(witness_holder, tx_context::sender(ctx));
}

public entry fun create_connector(
    holder: WitnessHolder,
    registry: &ConfigRegistry,  // Uses registry
    ctx: &mut TxContext
) {
    connector::new<TEMPLATE>(/* ... */, registry, ctx);
}
```

## Registry Management

### Update Single Address
```bash
sui client call \
  --package $REGISTRY_PKG \
  --module registry \
  --function set_meme_config_address \
  --args $REGISTRY_ID $ADMIN_CAP $NEW_ADDRESS
```

### Update All Addresses
```bash
sui client call \
  --package $REGISTRY_PKG \
  --module registry \
  --function update_all_addresses \
  --args $REGISTRY_ID $ADMIN_CAP $MEME_CONFIG $DEX_CONFIG $HOPFUN_PKG $HOPDEX_PKG
```

### Check Registry State
```bash
sui client object $REGISTRY_ID
```

## Benefits

✅ **No circular dependencies** - Registry breaks the cycle  
✅ **Deploy once** - No redeployment for address changes  
✅ **Dynamic resolution** - Addresses fetched at runtime  
✅ **Easy updates** - Change addresses via registry calls  
✅ **Clean architecture** - Separation of concerns  

## Troubleshooting

### Registry Not Initialized
```
Error: ERegistryNotInitialized
```
**Fix**: Update registry with addresses first

### Not Admin
```
Error: ENotAdmin
```
**Fix**: Use correct AdminCap ID

### Invalid Address
```
Error: EInvalidAddress
```
**Fix**: Don't use 0x0 as an address

## Important Files

- `scripts/deploy.sh` - Automated deployment
- `scripts/extract-bytecode.js` - Bytecode extraction
- `contracts/config_registry/` - Registry package
- `contracts/hopfun/sources/connector.move` - Registry-aware connector
- `contracts/coin_template/` - Template using registry
- `apps/frontend/src/services/network-config.service.ts` - Frontend config

## Security Notes

- **Keep AdminCap secure** - Controls registry updates
- **Document Registry IDs** - Critical for operations
- **Test on devnet first** - Before mainnet deployment
- **Monitor registry events** - Track all changes

## Support

For issues:
1. Check registry state: `sui client object $REGISTRY_ID`
2. Verify correct IDs in frontend config
3. Ensure bytecode is up to date: `node scripts/extract-bytecode.js`
4. Check transaction logs for detailed errors