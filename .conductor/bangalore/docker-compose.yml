services:
  mongodb:
    image: mongo:6.0
    container_name: hopfun-mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: hopfun
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
      - ./scripts/mongodb-keyfile:/etc/mongodb-keyfile:ro
    networks:
      - hopfun-network
    command: ["--replSet", "rs0", "--bind_ip", "0.0.0.0", "--keyFile", "/etc/mongodb-keyfile"]
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  mongodb-replica-setup:
    image: mongo:6.0
    container_name: hopfun-mongodb-setup
    restart: "no"
    networks:
      - hopfun-network
    depends_on:
      mongodb:
        condition: service_healthy
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
    volumes:
      - ./scripts/rs-init.sh:/scripts/rs-init.sh
    entrypoint: ["bash", "/scripts/rs-init.sh"]

volumes:
  mongodb_data:

networks:
  hopfun-network:
    driver: bridge