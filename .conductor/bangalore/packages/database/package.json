{"name": "@hopfun/database", "version": "1.0.0", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "npm run db:validate && npm run db:format && npm run db:generate && tsc && npm run copy-generated", "copy-generated": "cp -r src/generated dist/generated/", "db:generate": "npx prisma generate", "db:format": "npx prisma format", "db:validate": "npx prisma validate", "db:migrate": "npx prisma db push", "db:studio": "npx prisma studio", "lint": "npm run db:validate && eslint . --ext .ts", "format": "npm run db:format && prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit"}, "dependencies": {"@prisma/client": "catalog:"}, "devDependencies": {"@hopfun/eslint": "workspace:*", "@hopfun/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "prisma": "catalog:", "rimraf": "catalog:", "typescript": "catalog:"}}