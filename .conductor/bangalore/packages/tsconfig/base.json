{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2021", "module": "CommonJS", "moduleResolution": "Node", "lib": ["ES2020"], "strict": true, "declaration": true, "declarationMap": true, "sourceMap": true, "skipLibCheck": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "noImplicitThis": true, "alwaysStrict": true, "typeRoots": ["./node_modules/@types", "./src/types"], "noEmit": false}, "exclude": ["node_modules"]}