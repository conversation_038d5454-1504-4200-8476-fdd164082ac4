export function getFeeAmount(amountIn: bigint, feeBps: bigint): bigint {
  return (amountIn * feeBps) / 10_000n;
}

export function findAmountWithFee(forAmountIn: bigint, feeBps: bigint): bigint {
  // Determines what the input amount has to be including the fee to get the forAmountIn after deducting the fee
  return (forAmountIn * 10_000n) / (10_000n - feeBps);
}

export function getAmountOut(amountIn: bigint, reserveIn: bigint, reserveOut: bigint): bigint {
  const numerator = amountIn * reserveOut;
  const denominator = reserveIn + amountIn;

  return numerator / denominator;
}

export function getAmountIn(amountOut: bigint, reserveIn: bigint, reserveOut: bigint): bigint {
  const numerator = reserveIn * amountOut;
  const denominator = reserveOut - amountOut;

  return numerator / denominator;
}