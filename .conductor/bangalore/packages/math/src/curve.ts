import { findAmountWith<PERSON>ee, getAmountIn, getAmountOut, getFeeAmount } from "./math";

class BondingCurve {

  private sui_balance: bigint;
  private virtual_sui_amount: bigint;
  private token_balance: bigint;
  private pool_supply: bigint;
  private total_supply: bigint;
  private swap_fee_bps: bigint;

  constructor(
    sui_balance: bigint,
    virtual_sui_amount: bigint,
    token_balance: bigint,
    pool_supply: bigint,
    total_supply: bigint,
    swap_fee_bps: bigint
  ) {
    this.sui_balance = sui_balance;
    this.virtual_sui_amount = virtual_sui_amount;
    this.token_balance = token_balance;
    this.pool_supply = pool_supply;
    this.total_supply = total_supply;
    this.swap_fee_bps = swap_fee_bps;
  }

  getBuyAmountOut(sui_amount_in: bigint): bigint {
    let reserve_a = this.token_balance;
    let reserve_sui = this.virtual_sui_amount + this.sui_balance;

    // calculate max amount_in
    let max_amount_out = reserve_a - this.pool_supply;
    let max_amount_in = getAmountOut(max_amount_out, reserve_sui, reserve_a);

    // factor fees into calculation
    max_amount_in = findAmountWithFee(max_amount_in, this.swap_fee_bps);

    // only lets buys up to 700M tokens on bonding curve
    sui_amount_in = sui_amount_in < max_amount_in ? sui_amount_in : max_amount_in;

    // take fee_amount
    let fee_amount = getFeeAmount(sui_amount_in, this.swap_fee_bps);
    sui_amount_in = sui_amount_in - fee_amount;

    // amount out
    return getAmountOut(sui_amount_in, reserve_sui, reserve_a);
  }

  getSellAmountOut(token_in: bigint): bigint {
    // total reserve
    let reserve_a = this.token_balance;
    let reserve_sui = this.virtual_sui_amount + this.sui_balance;

    let max_amount_out = reserve_sui - this.virtual_sui_amount;
    let max_amount_in = getAmountIn(max_amount_out, reserve_a, reserve_sui);

    // only sell as much sui as is available
    token_in = token_in < max_amount_in ? token_in : max_amount_in;

    // amount out
    let amount_out = getAmountOut(token_in, reserve_a, reserve_sui);

    // take fee - only charge in SUI for sells
    let fee_amount = getFeeAmount(amount_out, this.swap_fee_bps);

    return amount_out - fee_amount;
  }

  getMarketCap(): bigint {
    if (this.token_balance === 0n) {
      return 0n;
    }

    const price_per_token = (this.sui_balance + this.virtual_sui_amount) / this.token_balance;

    // Market Cap = price_per_token * total_supply
    return price_per_token * this.total_supply;
  }

  getFinalMarketCap(): bigint {
    // TODO
  }

}