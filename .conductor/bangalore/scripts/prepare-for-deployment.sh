#!/bin/bash

#######################################
# Prepare Contracts for Deployment
# Description: Resets Move.toml addresses to 0x0 for fresh deployment
#######################################

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'
BOLD='\033[1m'

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONTRACTS_DIR="$PROJECT_ROOT/contracts"

echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║${BOLD}          Prepare Contracts for Deployment                   ${NC}${CYAN}║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Function to backup Move.toml
backup_move_toml() {
    local package_name=$1
    local move_toml="$CONTRACTS_DIR/$package_name/Move.toml"
    local backup_file="$move_toml.backup_$(date +%Y%m%d_%H%M%S)"
    
    if [ -f "$move_toml" ]; then
        cp "$move_toml" "$backup_file"
        echo -e "${GREEN}✅ Backed up $package_name/Move.toml to $(basename $backup_file)${NC}"
        return 0
    else
        echo -e "${RED}❌ Move.toml not found for $package_name${NC}"
        return 1
    fi
}

# Function to reset Move.toml for deployment
reset_move_toml() {
    local package_name=$1
    local move_toml="$CONTRACTS_DIR/$package_name/Move.toml"
    
    if [ ! -f "$move_toml" ]; then
        echo -e "${RED}❌ Move.toml not found for $package_name${NC}"
        return 1
    fi
    
    echo -e "${BLUE}Resetting $package_name/Move.toml for deployment...${NC}"
    
    # Remove published-at field
    if grep -q "^published-at = " "$move_toml"; then
        sed -i.tmp '/^published-at = /d' "$move_toml"
        echo -e "  ${CYAN}Removed published-at field${NC}"
    fi
    
    # Reset package address to 0x0
    if grep -q "^$package_name = " "$move_toml"; then
        sed -i.tmp "s/^$package_name = .*/$package_name = \"0x0\"/" "$move_toml"
        echo -e "  ${CYAN}Reset $package_name address to 0x0${NC}"
    fi
    
    # Clean up temp files
    rm -f "$move_toml.tmp"
    
    echo -e "${GREEN}✅ Reset $package_name for deployment${NC}"
    return 0
}

# Function to check if package is ready for deployment
check_deployment_ready() {
    local package_name=$1
    local move_toml="$CONTRACTS_DIR/$package_name/Move.toml"
    local ready=true
    
    echo -e "${BLUE}Checking $package_name...${NC}"
    
    # Check for published-at field
    if grep -q "^published-at = " "$move_toml" 2>/dev/null; then
        local published_at=$(grep "^published-at = " "$move_toml" | cut -d'"' -f2)
        echo -e "  ${YELLOW}⚠️  Has published-at: $published_at${NC}"
        ready=false
    fi
    
    # Check for non-zero address
    if grep -q "^$package_name = " "$move_toml" 2>/dev/null; then
        local address=$(grep "^$package_name = " "$move_toml" | cut -d'"' -f2)
        if [ "$address" != "0x0" ] && [ "$address" != "0" ]; then
            echo -e "  ${YELLOW}⚠️  Has non-zero address: $address${NC}"
            ready=false
        else
            echo -e "  ${GREEN}✓ Address is 0x0${NC}"
        fi
    fi
    
    if [ "$ready" = true ]; then
        echo -e "  ${GREEN}✅ Ready for deployment${NC}"
    else
        echo -e "  ${RED}❌ Not ready for deployment${NC}"
    fi
    
    return $([ "$ready" = true ] && echo 0 || echo 1)
}

# Main logic
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BOLD}Current Status:${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# Check all packages
all_ready=true
check_deployment_ready "hopdex" || all_ready=false
check_deployment_ready "hopfun" || all_ready=false
check_deployment_ready "coin_template" || all_ready=false

echo ""

if [ "$all_ready" = true ]; then
    echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${GREEN}✅ All contracts are ready for deployment!${NC}"
    echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
else
    echo -e "${YELLOW}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${YELLOW}⚠️  Some contracts need preparation${NC}"
    echo -e "${YELLOW}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo ""
    
    # Ask to fix
    if [ "$1" != "--auto" ]; then
        read -p "Do you want to prepare them for deployment? (y/N): " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo -e "${CYAN}Exiting without changes${NC}"
            exit 0
        fi
    fi
    
    echo ""
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${BOLD}Preparing Contracts:${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    
    # Backup and reset each package
    for package in hopdex hopfun coin_template; do
        if ! check_deployment_ready "$package" 2>/dev/null; then
            backup_move_toml "$package"
            reset_move_toml "$package"
            echo ""
        fi
    done
    
    echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${GREEN}✅ Contracts prepared for deployment!${NC}"
    echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
fi

echo ""
echo -e "${CYAN}Next steps:${NC}"
echo "1. Deploy contracts: ${BOLD}./scripts/sui-deploy.sh deploy devnet${NC}"
echo "2. Or deploy individually:"
echo "   ${BOLD}cd contracts/hopdex && sui client publish --gas-budget 500000000${NC}"
echo "   ${BOLD}cd contracts/hopfun && sui client publish --gas-budget 500000000${NC}"
echo ""

# Show backup locations if any were created
if ls "$CONTRACTS_DIR"/*/Move.toml.backup_* 2>/dev/null | head -1 > /dev/null; then
    echo -e "${YELLOW}Backup files created:${NC}"
    ls -la "$CONTRACTS_DIR"/*/Move.toml.backup_* 2>/dev/null | while read -r file; do
        echo "  $(basename $(dirname $file))/$(basename $file)"
    done
    echo ""
fi