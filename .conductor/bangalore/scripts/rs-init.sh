#!/bin/bash

echo "Waiting for MongoDB to be ready..."
until mongosh --host mongodb:27017 --username admin --password password --authenticationDatabase admin --eval "print(\"waited for connection\")" --quiet; do
  sleep 2
done

echo "Initializing replica set..."
mongosh --host mongodb:27017 --username admin --password password --authenticationDatabase admin --eval "
  rs.initiate({
    _id: 'rs0',
    members: [
      {
        _id: 0,
        host: 'mongodb:27017'
      }
    ]
  })
"

echo "Waiting for replica set to be ready..."
until mongosh --host mongodb:27017 --username admin --password password --authenticationDatabase admin --eval "rs.isMaster().ismaster" --quiet | grep -q "true"; do
  sleep 2
done

echo "Replica set initialization completed!"