#!/bin/bash
# save as: check-token-status.sh

PACKAGE_ID="$1"

if [ -z "$PACKAGE_ID" ]; then
    echo "Usage: $0 <PACKAGE_ID>"
    echo "Example: $0 0xa289cfb68ab8afadb854ef0f07bdf9485577eb3402545d65961fa3cb8b2b1a9d"
    exit 1
fi

echo "🔍 Token Verification Report"
echo "================================"
echo "Package ID: $PACKAGE_ID"
echo "Network: $(sui client active-env)"
echo "Time: $(date)"
echo ""

# Check 1: Package exists
echo "1️⃣ Package Deployment"
PACKAGE_CHECK=$(sui client object $PACKAGE_ID --json 2>/dev/null)
if echo "$PACKAGE_CHECK" | jq -e '.content.dataType == "package"' > /dev/null; then
    echo "   ✅ Package deployed"
    echo "   📦 Version: $(echo "$PACKAGE_CHECK" | jq -r '.version')"
else
    echo "   ❌ Package not found"
    exit 1
fi

# Check 2: CoinMetadata
echo ""
echo "2️⃣ Token Metadata"
METADATA_ID=$(sui client objects --json 2>/dev/null | \
    jq -r ".[] | select(.type | contains(\"CoinMetadata\")) | select(.type | contains(\"$PACKAGE_ID\")) | .objectId" | \
    head -1)

if [ -n "$METADATA_ID" ] && [ "$METADATA_ID" != "null" ]; then
    echo "   ✅ Metadata created"
    echo "   🆔 ID: $METADATA_ID"
    
    METADATA_DETAILS=$(sui client object $METADATA_ID --json 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo "   📝 Name: $(echo "$METADATA_DETAILS" | jq -r '.data.content.fields.name')"
        echo "   🏷️  Symbol: $(echo "$METADATA_DETAILS" | jq -r '.data.content.fields.symbol')"
        echo "   🔢 Decimals: $(echo "$METADATA_DETAILS" | jq -r '.data.content.fields.decimals')"
    fi
else
    echo "   ❌ Metadata not found"
fi

# Check 3: Connector
echo ""
echo "3️⃣ Bonding Curve"
CONNECTOR_ID=$(sui client objects --json 2>/dev/null | \
    jq -r ".[] | select(.type | contains(\"Connector\")) | select(.type | contains(\"$PACKAGE_ID\")) | .objectId" | \
    head -1)

if [ -n "$CONNECTOR_ID" ] && [ "$CONNECTOR_ID" != "null" ]; then
    echo "   ✅ Connector created"
    echo "   🔗 ID: $CONNECTOR_ID"
    
    CONNECTOR_DETAILS=$(sui client object $CONNECTOR_ID --json 2>/dev/null)
    OWNER=$(echo "$CONNECTOR_DETAILS" | jq -r '.data.owner.AddressOwner // .data.owner')
    
    if [ "$OWNER" = "0xf0d970d4459922d14862d10cfb6464abd84a90e86e394574f7e474b7aa5e85ef" ]; then
        echo "   📈 Status: ACTIVE (bonding curve ready)"
    else
        echo "   ⏳ Status: PENDING (owner: $OWNER)"
    fi
else
    echo "   ❌ Connector not found"
fi

echo ""
echo "================================"
echo "✅ Verification complete"