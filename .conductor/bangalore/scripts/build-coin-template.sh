#!/bin/bash

#######################################
# Build Coin Template with Dependencies
# Description: Builds the coin_template package with unpublished dependencies
#######################################

set -e

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'
BOLD='\033[1m'

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COIN_TEMPLATE_DIR="$PROJECT_ROOT/contracts/coin_template"

echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║${BOLD}             Coin Template Build Script                      ${NC}${CYAN}║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

cd "$COIN_TEMPLATE_DIR"

echo -e "${BLUE}Building coin_template with unpublished dependencies...${NC}"
echo ""

# Build with unpublished dependencies flag
if sui move build --dump-bytecode-as-base64 --with-unpublished-dependencies; then
    echo ""
    echo -e "${GREEN}✅ Successfully built coin_template!${NC}"
    echo ""
    echo -e "${CYAN}The bytecode has been generated and can be used for:${NC}"
    echo "  • Testing the module locally"
    echo "  • Extracting bytecode for template generation"
    echo "  • Publishing as part of a larger package"
    echo ""
    echo -e "${YELLOW}Note: This includes unpublished dependencies (hopfun).${NC}"
    echo -e "${YELLOW}To deploy, you'll need to first deploy hopfun to the network.${NC}"
else
    echo ""
    echo -e "${YELLOW}⚠️  Build failed. Trying alternative approach...${NC}"
    echo ""
    
    # Alternative: Build for testing/development
    echo -e "${BLUE}Building in dev mode...${NC}"
    if sui move build --dev; then
        echo -e "${GREEN}✅ Successfully built in dev mode!${NC}"
    else
        echo -e "${RED}❌ Build failed. Check your Move code for errors.${NC}"
        exit 1
    fi
fi

echo ""
echo -e "${CYAN}Next steps:${NC}"
echo "1. Deploy hopfun first: ${BOLD}./scripts/sui-deploy.sh deploy devnet${NC}"
echo "2. Update hopfun address in Move.toml with the deployed address"
echo "3. Then build coin_template normally"
echo ""