#!/bin/bash

#######################################
# Build Contracts for Bytecode Extraction
# Description: Builds Move contracts for bytecode extraction without network dependencies
#######################################

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'
BOLD='\033[1m'

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║${BOLD}         Build Contracts for Bytecode Extraction             ${NC}${CYAN}║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Function to build a package
build_package() {
    local package_name=$1
    local package_dir="$PROJECT_ROOT/contracts/$package_name"
    
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${BOLD}Building $package_name${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    
    if [ ! -d "$package_dir" ]; then
        echo -e "${RED}❌ Directory not found: $package_dir${NC}"
        return 1
    fi
    
    # Build with different approaches based on the package
    case $package_name in
        coin_template)
            echo -e "${CYAN}Building with unpublished dependencies...${NC}"
            if sui move build --dump-bytecode-as-base64 --with-unpublished-dependencies --path "$package_dir" 2>&1 | grep -q "BUILDING"; then
                echo -e "${GREEN}✅ Successfully built $package_name${NC}"
            else
                echo -e "${YELLOW}⚠️  Trying with --dev flag...${NC}"
                if sui move build --dev --path "$package_dir"; then
                    echo -e "${GREEN}✅ Successfully built $package_name in dev mode${NC}"
                else
                    echo -e "${RED}❌ Failed to build $package_name${NC}"
                    return 1
                fi
            fi
            ;;
        *)
            echo -e "${CYAN}Building normally...${NC}"
            if sui move build --dump-bytecode-as-base64 --path "$package_dir"; then
                echo -e "${GREEN}✅ Successfully built $package_name${NC}"
            else
                echo -e "${YELLOW}⚠️  Trying with --dev flag...${NC}"
                if sui move build --dev --path "$package_dir"; then
                    echo -e "${GREEN}✅ Successfully built $package_name in dev mode${NC}"
                else
                    echo -e "${RED}❌ Failed to build $package_name${NC}"
                    return 1
                fi
            fi
            ;;
    esac
    
    # Check for bytecode output
    local build_dir="$package_dir/build/$package_name"
    if [ -d "$build_dir" ]; then
        echo -e "${CYAN}Build artifacts located at:${NC}"
        echo "  $build_dir"
        
        # List bytecode files
        local bytecode_files=$(find "$build_dir/bytecode_modules" -name "*.mv" 2>/dev/null | wc -l)
        if [ "$bytecode_files" -gt 0 ]; then
            echo -e "${GREEN}  Found $bytecode_files bytecode module(s)${NC}"
        fi
    fi
    
    echo ""
    return 0
}

# Option to build specific package or all
PACKAGE=${1:-all}

if [ "$PACKAGE" = "all" ]; then
    # Build all packages in order
    build_package "hopdex"
    build_package "hopfun"
    build_package "coin_template"
    
    echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${GREEN}✅ Build process complete!${NC}"
    echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
else
    # Build specific package
    build_package "$PACKAGE"
fi

echo ""
echo -e "${CYAN}Next steps:${NC}"
echo "1. Extract bytecode from build directories"
echo "2. Use bytecode for template generation in frontend"
echo "3. Or deploy contracts: ${BOLD}./scripts/sui-deploy.sh deploy devnet${NC}"
echo ""
echo -e "${YELLOW}Note:${NC}"
echo "The --with-unpublished-dependencies flag allows building"
echo "without requiring dependencies to be published on-chain."
echo ""