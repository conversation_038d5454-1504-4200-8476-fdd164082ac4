#!/bin/bash

#######################################
# Sui Smart Contract Deployment Script
# Description: Manages deployment and status checking of Sui smart contracts
# Author: HopFun Team
# Version: 1.0.0
#######################################

set -e  # Exit on error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color
BOLD='\033[1m'

# Default values
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONTRACTS_DIR="$PROJECT_ROOT/contracts"
CONFIG_DIR="$PROJECT_ROOT/config"
DEPLOYMENT_LOG="$PROJECT_ROOT/deployment.log"
NETWORK="devnet"
GAS_BUDGET=500000000  # 0.5 SUI

# Contract directories
HOPFUN_DIR="$CONTRACTS_DIR/hopfun"
HOPDEX_DIR="$CONTRACTS_DIR/hopdex"
COIN_TEMPLATE_DIR="$CONTRACTS_DIR/coin_template"

# Configuration files
DEPLOYMENT_CONFIG="$CONFIG_DIR/deployments.json"

#######################################
# Helper Functions
#######################################

print_header() {
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║${BOLD}                 Sui Smart Contract Manager                  ${NC}${CYAN}║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

print_section() {
    echo ""
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${BOLD}$1${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${CYAN}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${MAGENTA}▶ $1${NC}"
}

# Check if required commands are installed
check_dependencies() {
    print_section "Checking Dependencies"
    
    local deps_ok=true
    
    # Check for Sui CLI
    if command -v sui &> /dev/null; then
        local sui_version=$(sui --version 2>/dev/null || echo "unknown")
        print_success "Sui CLI installed: $sui_version"
    else
        print_error "Sui CLI not installed"
        echo "    Please install Sui CLI: https://docs.sui.io/guides/developer/getting-started/sui-install"
        deps_ok=false
    fi
    
    # Check for jq (for JSON processing)
    if command -v jq &> /dev/null; then
        print_success "jq installed"
    else
        print_warning "jq not installed (optional but recommended)"
        echo "    Install with: brew install jq (macOS) or apt-get install jq (Linux)"
    fi
    
    # Check for git
    if command -v git &> /dev/null; then
        print_success "git installed"
    else
        print_error "git not installed"
        deps_ok=false
    fi
    
    if [ "$deps_ok" = false ]; then
        echo ""
        print_error "Missing required dependencies. Please install them and try again."
        exit 1
    fi
    
    echo ""
}

# Initialize configuration
init_config() {
    if [ ! -d "$CONFIG_DIR" ]; then
        mkdir -p "$CONFIG_DIR"
    fi
    
    if [ ! -f "$DEPLOYMENT_CONFIG" ]; then
        cat > "$DEPLOYMENT_CONFIG" << EOF
{
  "deployments": {
    "devnet": {
      "hopfun": {
        "packageId": null,
        "memeConfigId": null,
        "adminCapId": null,
        "upgradeCapId": null,
        "deployedAt": null,
        "transactionDigest": null
      },
      "hopdex": {
        "packageId": null,
        "dexConfigId": null,
        "adminCapId": null,
        "upgradeCapId": null,
        "deployedAt": null,
        "transactionDigest": null
      }
    },
    "testnet": {
      "hopfun": {
        "packageId": null,
        "memeConfigId": null,
        "adminCapId": null,
        "upgradeCapId": null,
        "deployedAt": null,
        "transactionDigest": null
      },
      "hopdex": {
        "packageId": null,
        "dexConfigId": null,
        "adminCapId": null,
        "upgradeCapId": null,
        "deployedAt": null,
        "transactionDigest": null
      }
    },
    "mainnet": {
      "hopfun": {
        "packageId": null,
        "memeConfigId": null,
        "adminCapId": null,
        "upgradeCapId": null,
        "deployedAt": null,
        "transactionDigest": null
      },
      "hopdex": {
        "packageId": null,
        "dexConfigId": null,
        "adminCapId": null,
        "upgradeCapId": null,
        "deployedAt": null,
        "transactionDigest": null
      }
    }
  }
}
EOF
        print_success "Created deployment configuration file"
    fi
}

# Get current network
get_current_network() {
    local current_env=$(sui client active-env 2>/dev/null || echo "")
    echo "$current_env"
}

# Switch network
switch_network() {
    local target_network=$1
    print_step "Switching to $target_network..."
    
    # Check if environment exists
    if ! sui client envs | grep -q "$target_network"; then
        print_warning "Network $target_network not configured. Adding it..."
        
        case "$target_network" in
            devnet)
                sui client new-env --alias devnet --rpc https://fullnode.devnet.sui.io:443
                ;;
            testnet)
                sui client new-env --alias testnet --rpc https://fullnode.testnet.sui.io:443
                ;;
            mainnet)
                sui client new-env --alias mainnet --rpc https://fullnode.mainnet.sui.io:443
                ;;
            *)
                print_error "Unknown network: $target_network"
                return 1
                ;;
        esac
    fi
    
    sui client switch --env "$target_network" &>/dev/null
    print_success "Switched to $target_network"
}

# Check account balance
check_balance() {
    print_section "Account Balance"
    
    local address=$(sui client active-address 2>/dev/null)
    if [ -z "$address" ]; then
        print_error "No active address found"
        return 1
    fi
    
    print_info "Active address: $address"
    echo ""
    
    # Get gas coins
    sui client gas "$address" 2>/dev/null || {
        print_warning "Could not fetch balance. The account might be new or have no funds."
        echo ""
        
        if [ "$NETWORK" = "devnet" ] || [ "$NETWORK" = "testnet" ]; then
            print_info "You can request test tokens from the faucet:"
            echo "    curl --location --request POST 'https://faucet.$NETWORK.sui.io/gas' \\"
            echo "         --header 'Content-Type: application/json' \\"
            echo "         --data-raw '{\"FixedAmountRequest\":{\"recipient\":\"'$address'\"}}'"
        fi
    }
    echo ""
}

# Check if package is deployed
check_package_deployment() {
    local package_name=$1
    local network=$2
    
    print_step "Checking $package_name on $network..."
    
    # Read from deployment config if available
    if [ -f "$DEPLOYMENT_CONFIG" ] && command -v jq &> /dev/null; then
        local package_id=$(jq -r ".deployments.$network.$package_name.packageId // null" "$DEPLOYMENT_CONFIG")
        local deployed_at=$(jq -r ".deployments.$network.$package_name.deployedAt // null" "$DEPLOYMENT_CONFIG")
        local tx_digest=$(jq -r ".deployments.$network.$package_name.transactionDigest // null" "$DEPLOYMENT_CONFIG")
        
        if [ "$package_id" != "null" ] && [ -n "$package_id" ]; then
            print_success "$package_name is deployed on $network"
            echo "    Package ID: ${CYAN}$package_id${NC}"
            [ "$deployed_at" != "null" ] && echo "    Deployed at: $deployed_at"
            [ "$tx_digest" != "null" ] && echo "    Transaction: $tx_digest"
            
            # Try to verify on-chain
            if sui client object "$package_id" &>/dev/null; then
                echo "    Status: ${GREEN}Verified on-chain${NC}"
            else
                echo "    Status: ${YELLOW}Not found on-chain (may have been deleted or incorrect network)${NC}"
            fi
        else
            print_warning "$package_name not found in deployment records for $network"
        fi
    else
        # Check Move.toml for published-at field
        local move_toml="$CONTRACTS_DIR/$package_name/Move.toml"
        if [ -f "$move_toml" ]; then
            local published_at=$(grep "^published-at" "$move_toml" | cut -d'"' -f2 | head -1)
            if [ -n "$published_at" ] && [ "$published_at" != "0x0" ]; then
                print_info "$package_name has published-at in Move.toml: $published_at"
                echo "    (Note: This might be from a different network)"
            else
                print_warning "$package_name not deployed (no published-at in Move.toml)"
            fi
        else
            print_error "Move.toml not found for $package_name"
        fi
    fi
    echo ""
}

# Check deployment status
check_status() {
    local target_network=${1:-$NETWORK}
    
    print_section "Deployment Status Check"
    print_info "Network: $target_network"
    echo ""
    
    # Switch to target network
    switch_network "$target_network"
    
    # Check balance
    check_balance
    
    # Check each package
    print_section "Package Deployments"
    check_package_deployment "hopfun" "$target_network"
    check_package_deployment "hopdex" "$target_network"
    
    # Summary
    print_section "Summary"
    if [ -f "$DEPLOYMENT_CONFIG" ] && command -v jq &> /dev/null; then
        local hopfun_deployed=$(jq -r ".deployments.$target_network.hopfun.packageId // null" "$DEPLOYMENT_CONFIG")
        local hopdex_deployed=$(jq -r ".deployments.$target_network.hopdex.packageId // null" "$DEPLOYMENT_CONFIG")
        
        if [ "$hopfun_deployed" != "null" ] && [ "$hopdex_deployed" != "null" ]; then
            print_success "All packages deployed on $target_network ✨"
        elif [ "$hopfun_deployed" != "null" ] || [ "$hopdex_deployed" != "null" ]; then
            print_warning "Some packages deployed on $target_network"
        else
            print_warning "No packages deployed on $target_network"
        fi
    else
        print_info "Run deployments to populate configuration"
    fi
}

# Build package
build_package() {
    local package_dir=$1
    local package_name=$(basename "$package_dir")
    
    print_step "Building $package_name..."
    cd "$package_dir"
    
    # Build with bytecode dump for verification
    if sui move build --dump-bytecode-as-base64 &>/dev/null; then
        print_success "$package_name built successfully"
        return 0
    else
        print_error "Failed to build $package_name"
        sui move build 2>&1 | head -20
        return 1
    fi
}

# Test package
test_package() {
    local package_dir=$1
    local package_name=$(basename "$package_dir")
    
    print_step "Testing $package_name..."
    cd "$package_dir"
    
    if sui move test &>/dev/null; then
        print_success "$package_name tests passed"
        return 0
    else
        print_warning "$package_name tests failed or no tests found"
        return 1
    fi
}

# Deploy package
deploy_package() {
    local package_dir=$1
    local package_name=$(basename "$package_dir")
    local network=$2
    
    print_section "Deploying $package_name to $network"
    
    # Build package first
    if ! build_package "$package_dir"; then
        return 1
    fi
    
    # Test package (optional, continue even if tests fail)
    test_package "$package_dir"
    
    cd "$package_dir"
    
    print_step "Publishing $package_name to $network..."
    print_info "Gas budget: $GAS_BUDGET MIST"
    
    # Publish and capture output
    local temp_output="/tmp/sui_deploy_output_$$.txt"
    if sui client publish --gas-budget "$GAS_BUDGET" . 2>&1 | tee "$temp_output"; then
        print_success "$package_name published successfully!"
        
        # Parse output for important information
        local package_id=$(grep -oE "PackageID: 0x[a-f0-9]{64}" "$temp_output" | cut -d' ' -f2 | head -1)
        local tx_digest=$(grep -oE "Transaction Digest: [A-Za-z0-9]{44}" "$temp_output" | cut -d' ' -f3 | head -1)
        
        if [ -n "$package_id" ]; then
            print_success "Package ID: $package_id"
            
            # Update deployment configuration
            if command -v jq &> /dev/null && [ -f "$DEPLOYMENT_CONFIG" ]; then
                local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
                
                # Update JSON config
                local temp_config="/tmp/deployment_config_$$.json"
                jq ".deployments.$network.$package_name.packageId = \"$package_id\" |
                    .deployments.$network.$package_name.deployedAt = \"$timestamp\" |
                    .deployments.$network.$package_name.transactionDigest = \"$tx_digest\"" \
                    "$DEPLOYMENT_CONFIG" > "$temp_config"
                mv "$temp_config" "$DEPLOYMENT_CONFIG"
                
                print_success "Updated deployment configuration"
            fi
            
            # Update Move.toml with published-at
            if [ "$package_name" = "hopdex" ]; then
                # HopDex needs to be updated in Move.toml for HopFun dependency
                sed -i.bak "s/^hopdex = .*/hopdex = \"$package_id\"/" "$package_dir/Move.toml"
                sed -i.bak "s/^published-at = .*/published-at = \"$package_id\"/" "$package_dir/Move.toml"
                rm -f "$package_dir/Move.toml.bak"
                
                # Also update HopFun's dependency
                sed -i.bak "s/^hopdex = { local = .*/hopdex = { git = \"$package_id\" }/" "$HOPFUN_DIR/Move.toml"
                rm -f "$HOPFUN_DIR/Move.toml.bak"
                
                print_success "Updated Move.toml files"
            fi
            
            # Log deployment
            echo "[$(date)] Deployed $package_name to $network: $package_id (tx: $tx_digest)" >> "$DEPLOYMENT_LOG"
        fi
        
        rm -f "$temp_output"
        return 0
    else
        print_error "Failed to publish $package_name"
        cat "$temp_output"
        rm -f "$temp_output"
        return 1
    fi
}

# Deploy all contracts
deploy_all() {
    local target_network=${1:-$NETWORK}
    
    print_section "Full Deployment to $target_network"
    
    # Confirmation
    echo -e "${YELLOW}⚠️  Warning: This will deploy all contracts to $target_network${NC}"
    echo -e "${YELLOW}   Estimated gas cost: ~1 SUI per contract${NC}"
    echo ""
    read -p "Continue? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Deployment cancelled"
        return 0
    fi
    
    # Switch network
    switch_network "$target_network"
    
    # Check balance
    check_balance
    
    # Deploy in order (HopDex first, as HopFun depends on it)
    print_info "Deployment order: hopdex -> hopfun"
    echo ""
    
    # Deploy HopDex
    if deploy_package "$HOPDEX_DIR" "$target_network"; then
        print_success "HopDex deployed successfully"
    else
        print_error "HopDex deployment failed"
        return 1
    fi
    
    echo ""
    
    # Deploy HopFun
    if deploy_package "$HOPFUN_DIR" "$target_network"; then
        print_success "HopFun deployed successfully"
    else
        print_error "HopFun deployment failed"
        return 1
    fi
    
    print_section "Deployment Complete! 🎉"
    check_status "$target_network"
}

# Update frontend environment
update_frontend_env() {
    local network=$1
    local env_file="$PROJECT_ROOT/apps/frontend/.env.local"
    
    print_section "Updating Frontend Environment"
    
    if [ ! -f "$DEPLOYMENT_CONFIG" ] || ! command -v jq &> /dev/null; then
        print_warning "Cannot update frontend environment (missing config or jq)"
        return 1
    fi
    
    # Read deployment data
    local hopfun_package=$(jq -r ".deployments.$network.hopfun.packageId // \"0x0\"" "$DEPLOYMENT_CONFIG")
    local hopfun_config=$(jq -r ".deployments.$network.hopfun.memeConfigId // \"0x0\"" "$DEPLOYMENT_CONFIG")
    local hopdex_package=$(jq -r ".deployments.$network.hopdex.packageId // \"0x0\"" "$DEPLOYMENT_CONFIG")
    local hopdex_config=$(jq -r ".deployments.$network.hopdex.dexConfigId // \"0x0\"" "$DEPLOYMENT_CONFIG")
    
    # Create or update env file
    if [ -f "$env_file" ]; then
        cp "$env_file" "$env_file.backup"
        print_info "Backed up existing .env.local"
    fi
    
    # Update network-specific variables
    local network_upper=$(echo "$network" | tr '[:lower:]' '[:upper:]')
    
    cat > "$env_file" << EOF
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000

# Network Configuration
NEXT_PUBLIC_NETWORK=$network

# HopFun Contract Configuration ($network_upper)
NEXT_PUBLIC_HOPFUN_PACKAGE_ID_${network_upper}=$hopfun_package
NEXT_PUBLIC_MEME_CONFIG_ID_${network_upper}=$hopfun_config

# HopDex Contract Configuration ($network_upper)
NEXT_PUBLIC_HOPDEX_PACKAGE_ID_${network_upper}=$hopdex_package
NEXT_PUBLIC_HOPDEX_CONFIG_ID_${network_upper}=$hopdex_config
EOF
    
    print_success "Updated frontend environment for $network"
    echo "    File: $env_file"
}

# Show help
show_help() {
    print_header
    echo "Usage: $0 [command] [options]"
    echo ""
    echo "Commands:"
    echo "  check [network]     Check deployment status (default: current network)"
    echo "  deploy [network]    Deploy all contracts to network"
    echo "  build              Build all contracts locally"
    echo "  test               Run tests for all contracts"
    echo "  update-env [network] Update frontend .env.local with deployment addresses"
    echo "  help               Show this help message"
    echo ""
    echo "Networks:"
    echo "  devnet   Development network (default)"
    echo "  testnet  Test network"
    echo "  mainnet  Production network"
    echo ""
    echo "Options:"
    echo "  --gas-budget <amount>  Set gas budget in MIST (default: 500000000)"
    echo ""
    echo "Examples:"
    echo "  $0 check              # Check current network status"
    echo "  $0 check mainnet      # Check mainnet deployment status"
    echo "  $0 deploy devnet      # Deploy all contracts to devnet"
    echo "  $0 build              # Build all contracts"
    echo "  $0 update-env devnet  # Update frontend env for devnet"
    echo ""
}

#######################################
# Main Script Logic
#######################################

main() {
    # Parse command line arguments
    COMMAND=${1:-help}
    shift || true
    
    # Initialize
    init_config
    
    case "$COMMAND" in
        check)
            check_dependencies
            check_status "${1:-$(get_current_network)}"
            ;;
        deploy)
            check_dependencies
            deploy_all "${1:-devnet}"
            ;;
        build)
            check_dependencies
            print_section "Building All Contracts"
            build_package "$HOPDEX_DIR"
            echo ""
            build_package "$HOPFUN_DIR"
            ;;
        test)
            check_dependencies
            print_section "Testing All Contracts"
            test_package "$HOPDEX_DIR"
            echo ""
            test_package "$HOPFUN_DIR"
            ;;
        update-env)
            update_frontend_env "${1:-devnet}"
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_error "Unknown command: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# Run main function
print_header
main "$@"