#!/bin/bash

#######################################
# Sequential Deployment Script
# Description: Deploys contracts in the correct order with proper dependency handling
#######################################

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'
BOLD='\033[1m'

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONTRACTS_DIR="$PROJECT_ROOT/contracts"
CONFIG_DIR="$PROJECT_ROOT/config"
DEPLOYMENT_CONFIG="$CONFIG_DIR/deployments.json"

NETWORK=${1:-devnet}
GAS_BUDGET=500000000  # 0.5 SUI

echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║${BOLD}           Sequential Contract Deployment                    ${NC}${CYAN}║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""
echo -e "${BLUE}Network: ${CYAN}$NETWORK${NC}"
echo -e "${BLUE}Gas Budget: ${CYAN}$GAS_BUDGET MIST${NC}"
echo ""

# Initialize config directory if needed
if [ ! -d "$CONFIG_DIR" ]; then
    mkdir -p "$CONFIG_DIR"
fi

# Step 1: Deploy HopDex
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BOLD}Step 1: Deploy HopDex${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# Make sure hopdex is at 0x0
echo -e "${CYAN}Checking hopdex configuration...${NC}"
HOPDEX_TOML="$CONTRACTS_DIR/hopdex/Move.toml"

# Remove published-at if exists
if grep -q "^published-at = " "$HOPDEX_TOML"; then
    sed -i.tmp '/^published-at = /d' "$HOPDEX_TOML"
    rm -f "$HOPDEX_TOML.tmp"
    echo -e "  ${GREEN}✓ Removed published-at field${NC}"
fi

# Set address to 0x0
sed -i.tmp "s/^hopdex = .*/hopdex = \"0x0\"/" "$HOPDEX_TOML"
rm -f "$HOPDEX_TOML.tmp"
echo -e "  ${GREEN}✓ Set hopdex address to 0x0${NC}"

# Build hopdex
echo -e "${CYAN}Building hopdex...${NC}"
if sui move build --path "$CONTRACTS_DIR/hopdex" 2>&1 | grep -q "BUILDING hopdex"; then
    echo -e "${GREEN}✅ HopDex built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build hopdex${NC}"
    exit 1
fi

# Deploy hopdex
echo -e "${CYAN}Deploying hopdex...${NC}"
cd "$CONTRACTS_DIR/hopdex"
TEMP_OUTPUT="/tmp/hopdex_deploy_$$.txt"

if sui client publish --gas-budget "$GAS_BUDGET" . 2>&1 | tee "$TEMP_OUTPUT"; then
    # Extract package ID
    HOPDEX_PACKAGE_ID=$(grep -oE "PackageID: 0x[a-f0-9]{64}" "$TEMP_OUTPUT" | cut -d' ' -f2 | head -1)
    
    if [ -n "$HOPDEX_PACKAGE_ID" ]; then
        echo -e "${GREEN}✅ HopDex deployed successfully!${NC}"
        echo -e "${CYAN}Package ID: ${BOLD}$HOPDEX_PACKAGE_ID${NC}"
        
        # Update hopdex Move.toml with deployed address
        sed -i.tmp "s/^hopdex = .*/hopdex = \"$HOPDEX_PACKAGE_ID\"/" "$HOPDEX_TOML"
        # Add published-at field
        echo "published-at = \"$HOPDEX_PACKAGE_ID\"" >> "$HOPDEX_TOML"
        rm -f "$HOPDEX_TOML.tmp"
        
        echo -e "${GREEN}✓ Updated hopdex/Move.toml with deployed address${NC}"
    else
        echo -e "${RED}❌ Could not extract HopDex package ID${NC}"
        cat "$TEMP_OUTPUT"
        rm -f "$TEMP_OUTPUT"
        exit 1
    fi
else
    echo -e "${RED}❌ Failed to deploy hopdex${NC}"
    cat "$TEMP_OUTPUT"
    rm -f "$TEMP_OUTPUT"
    exit 1
fi

rm -f "$TEMP_OUTPUT"
echo ""

# Step 2: Update HopFun dependency and deploy
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BOLD}Step 2: Deploy HopFun${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

echo -e "${CYAN}Updating hopfun configuration...${NC}"
HOPFUN_TOML="$CONTRACTS_DIR/hopfun/Move.toml"

# Remove published-at if exists
if grep -q "^published-at = " "$HOPFUN_TOML"; then
    sed -i.tmp '/^published-at = /d' "$HOPFUN_TOML"
    rm -f "$HOPFUN_TOML.tmp"
    echo -e "  ${GREEN}✓ Removed published-at field${NC}"
fi

# Set hopfun address to 0x0
sed -i.tmp "s/^hopfun = .*/hopfun = \"0x0\"/" "$HOPFUN_TOML"
rm -f "$HOPFUN_TOML.tmp"
echo -e "  ${GREEN}✓ Set hopfun address to 0x0${NC}"

# Build hopfun
echo -e "${CYAN}Building hopfun...${NC}"
if sui move build --path "$CONTRACTS_DIR/hopfun" 2>&1 | grep -q "BUILDING hopfun"; then
    echo -e "${GREEN}✅ HopFun built successfully${NC}"
else
    echo -e "${RED}❌ Failed to build hopfun${NC}"
    echo "Try running: sui move build --path $CONTRACTS_DIR/hopfun"
    exit 1
fi

# Deploy hopfun
echo -e "${CYAN}Deploying hopfun...${NC}"
cd "$CONTRACTS_DIR/hopfun"
TEMP_OUTPUT="/tmp/hopfun_deploy_$$.txt"

if sui client publish --gas-budget "$GAS_BUDGET" . 2>&1 | tee "$TEMP_OUTPUT"; then
    # Extract package ID
    HOPFUN_PACKAGE_ID=$(grep -oE "PackageID: 0x[a-f0-9]{64}" "$TEMP_OUTPUT" | cut -d' ' -f2 | head -1)
    
    if [ -n "$HOPFUN_PACKAGE_ID" ]; then
        echo -e "${GREEN}✅ HopFun deployed successfully!${NC}"
        echo -e "${CYAN}Package ID: ${BOLD}$HOPFUN_PACKAGE_ID${NC}"
        
        # Update hopfun Move.toml
        sed -i.tmp "s/^hopfun = .*/hopfun = \"$HOPFUN_PACKAGE_ID\"/" "$HOPFUN_TOML"
        # Add published-at field
        echo "published-at = \"$HOPFUN_PACKAGE_ID\"" >> "$HOPFUN_TOML"
        rm -f "$HOPFUN_TOML.tmp"
        
        echo -e "${GREEN}✓ Updated hopfun/Move.toml with deployed address${NC}"
    else
        echo -e "${RED}❌ Could not extract HopFun package ID${NC}"
        cat "$TEMP_OUTPUT"
        rm -f "$TEMP_OUTPUT"
        exit 1
    fi
else
    echo -e "${RED}❌ Failed to deploy hopfun${NC}"
    cat "$TEMP_OUTPUT"
    rm -f "$TEMP_OUTPUT"
    exit 1
fi

rm -f "$TEMP_OUTPUT"

# Step 3: Save deployment configuration
echo ""
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BOLD}Step 3: Save Configuration${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# Create or update deployment config
if command -v jq &> /dev/null; then
    if [ ! -f "$DEPLOYMENT_CONFIG" ]; then
        echo '{"deployments": {}}' > "$DEPLOYMENT_CONFIG"
    fi
    
    TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Update config with jq
    jq ".deployments.$NETWORK = {
        \"hopdex\": {
            \"packageId\": \"$HOPDEX_PACKAGE_ID\",
            \"deployedAt\": \"$TIMESTAMP\"
        },
        \"hopfun\": {
            \"packageId\": \"$HOPFUN_PACKAGE_ID\",
            \"deployedAt\": \"$TIMESTAMP\"
        }
    }" "$DEPLOYMENT_CONFIG" > "$DEPLOYMENT_CONFIG.tmp"
    
    mv "$DEPLOYMENT_CONFIG.tmp" "$DEPLOYMENT_CONFIG"
    echo -e "${GREEN}✅ Saved deployment configuration${NC}"
fi

# Step 4: Update frontend environment
echo ""
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BOLD}Step 4: Update Frontend Environment${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

ENV_FILE="$PROJECT_ROOT/apps/frontend/.env.local"
NETWORK_UPPER=$(echo "$NETWORK" | tr '[:lower:]' '[:upper:]')

# Update or create .env.local
cat > "$ENV_FILE" << EOF
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000

# Network Configuration
NEXT_PUBLIC_NETWORK=$NETWORK

# HopFun Contract Configuration ($NETWORK_UPPER)
NEXT_PUBLIC_HOPFUN_PACKAGE_ID_${NETWORK_UPPER}=$HOPFUN_PACKAGE_ID
NEXT_PUBLIC_MEME_CONFIG_ID_${NETWORK_UPPER}=0x0

# HopDex Contract Configuration ($NETWORK_UPPER)
NEXT_PUBLIC_HOPDEX_PACKAGE_ID_${NETWORK_UPPER}=$HOPDEX_PACKAGE_ID
NEXT_PUBLIC_HOPDEX_CONFIG_ID_${NETWORK_UPPER}=0x0
EOF

echo -e "${GREEN}✅ Updated frontend environment${NC}"

# Success summary
echo ""
echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${GREEN}🎉 Deployment Complete!${NC}"
echo -e "${GREEN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo ""
echo -e "${BOLD}Deployed Packages:${NC}"
echo -e "  HopDex: ${CYAN}$HOPDEX_PACKAGE_ID${NC}"
echo -e "  HopFun: ${CYAN}$HOPFUN_PACKAGE_ID${NC}"
echo ""
echo -e "${BOLD}Configuration Files Updated:${NC}"
echo -e "  ✓ $HOPDEX_TOML"
echo -e "  ✓ $HOPFUN_TOML"
echo -e "  ✓ $ENV_FILE"
if [ -f "$DEPLOYMENT_CONFIG" ]; then
    echo -e "  ✓ $DEPLOYMENT_CONFIG"
fi
echo ""
echo -e "${CYAN}Next steps:${NC}"
echo "1. Restart your frontend development server"
echo "2. The contracts are now available on $NETWORK"
echo "3. You can interact with them using the package IDs above"
echo ""