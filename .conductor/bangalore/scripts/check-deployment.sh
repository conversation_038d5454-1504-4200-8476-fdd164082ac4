#!/bin/bash

#######################################
# Deployment Status Checker with On-Chain Verification
# Description: Checks deployment status from config/deployments.json and verifies on-chain
# Usage: ./check-deployment.sh
#######################################

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m'
BOLD='\033[1m'
DIM='\033[2m'

# Paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_FILE="$PROJECT_ROOT/config/deployments.json"

# Check for --latest flag to use latest deployment results
USE_LATEST=false
if [ "$1" = "--latest" ]; then
    USE_LATEST=true
    # Find the most recent deployment results file
    LATEST_DEPLOYMENT=$(ls -t "$PROJECT_ROOT"/deployment-results-*.json 2>/dev/null | head -1)
    if [ -n "$LATEST_DEPLOYMENT" ]; then
        echo -e "${YELLOW}Using latest deployment results: $(basename "$LATEST_DEPLOYMENT")${NC}"
        CONFIG_FILE="$LATEST_DEPLOYMENT"
    else
        echo -e "${RED}No deployment results files found${NC}"
        exit 1
    fi
fi

# Show usage if --help is provided
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo ""
    echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${CYAN}║${BOLD}        Sui Contract Deployment Status Checker               ${NC}${CYAN}║${NC}"
    echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo -e "${BOLD}Usage:${NC}"
    echo "  bash scripts/check-deployment.sh [OPTIONS]"
    echo ""
    echo -e "${BOLD}Options:${NC}"
    echo "  --latest    Use the latest deployment results file instead of config/deployments.json"
    echo "  --help, -h  Show this help message"
    echo ""
    echo -e "${BOLD}Examples:${NC}"
    echo "  bash scripts/check-deployment.sh           # Check using config/deployments.json"
    echo "  bash scripts/check-deployment.sh --latest  # Check using latest deployment results"
    echo ""
    exit 0
fi

echo ""
echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║${BOLD}        Sui Contract Deployment Status (with Verification)   ${NC}${CYAN}║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo -e "${RED}❌ jq is required but not installed${NC}"
    echo "   Install: brew install jq (macOS) or apt-get install jq (Linux)"
    exit 1
fi

# Check if deployments.json exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${RED}❌ config/deployments.json not found${NC}"
    echo "   Run deployment script first: bash scripts/deploy-all-contracts.sh"
    exit 1
fi

# Check current network
if command -v sui &> /dev/null; then
    CURRENT_NET=$(sui client active-env 2>/dev/null || echo "not configured")
    CURRENT_ADDR=$(sui client active-address 2>/dev/null || echo "no address")
    echo -e "${BOLD}Current Sui Configuration:${NC}"
    echo -e "  Network: ${CYAN}$CURRENT_NET${NC}"
    echo -e "  Address: ${CYAN}$CURRENT_ADDR${NC}"
    
    # Check balance
    SUI_BALANCE=$(sui client gas --json 2>/dev/null | jq -r '.[0].suiBalance // "0"' 2>/dev/null || echo "0")
    if [ -n "$SUI_BALANCE" ] && [ "$SUI_BALANCE" != "0" ]; then
        echo -e "  Balance: ${CYAN}$SUI_BALANCE SUI${NC}"
    fi
    echo ""
else
    echo -e "${RED}❌ Sui CLI not installed${NC}"
    echo "   Please install: https://docs.sui.io/guides/developer/getting-started/sui-install"
    echo ""
    exit 1
fi

# Function to check if object exists on chain
check_on_chain() {
    local object_id=$1
    local network=$2

    # Only check if we're on the right network
    if [ "$CURRENT_NET" = "$network" ]; then
        # Check if object exists (packages have 'content', other objects have 'data')
        if sui client object "$object_id" --json 2>/dev/null | jq -e '.objectId' > /dev/null 2>&1; then
            echo "true"
        else
            echo "false"
        fi
    else
        echo "skip"
    fi
}

# Function to check a package
check_package() {
    local network=$1
    local package_name=$2
    local package_id=$3
    local config_id=$4
    local admin_cap=$5
    local date=$6
    local config_name=$7
    
    echo -e "  ${BOLD}$package_name:${NC}"
    
    if [ "$package_id" != "null" ] && [ -n "$package_id" ]; then
        echo -e "    Package ID: ${CYAN}${package_id:0:20}...${NC}"
        
        # Check if package exists on chain (only if we're on the right network)
        if [ "$CURRENT_NET" = "$network" ]; then
            ON_CHAIN=$(check_on_chain "$package_id" "$network")
            if [ "$ON_CHAIN" = "true" ]; then
                echo -e "    Status:     ${GREEN}✅ Exists on-chain${NC}"
            else
                echo -e "    Status:     ${RED}❌ NOT FOUND on-chain${NC}"
            fi
        else
            echo -e "    Status:     ${DIM}(switch to $network to verify)${NC}"
        fi
        
        # Show config object if exists
        if [ "$config_id" != "null" ] && [ -n "$config_id" ] && [ "$config_id" != "" ]; then
            echo -e "    $config_name: ${CYAN}${config_id:0:20}...${NC}"
            
            # Check if config exists on chain (only if we're on the right network)
            if [ "$CURRENT_NET" = "$network" ]; then
                CONFIG_ON_CHAIN=$(check_on_chain "$config_id" "$network")
                if [ "$CONFIG_ON_CHAIN" = "true" ]; then
                    echo -e "                ${GREEN}✅ Exists${NC}"
                else
                    echo -e "                ${RED}❌ NOT FOUND${NC}"
                fi
            fi
        elif [ "$config_name" != "" ]; then
            echo -e "    $config_name: ${YELLOW}⚠️ Not found in config${NC}"
        fi
        
        # Show admin cap if exists
        if [ "$admin_cap" != "null" ] && [ -n "$admin_cap" ] && [ "$admin_cap" != "" ]; then
            echo -e "    Admin Cap:  ${DIM}${admin_cap:0:20}...${NC}"
        fi
        
        # Show date if exists
        if [ "$date" != "null" ] && [ -n "$date" ]; then
            echo -e "    Deployed:   ${DIM}$date${NC}"
        fi
    else
        echo -e "    Status:     ${YELLOW}⚠️ Not deployed${NC}"
    fi
    echo ""
}

# Function to check a network
check_network() {
    local network=$1
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${BOLD}Network: $network${NC}"
    if [ "$CURRENT_NET" = "$network" ]; then
        echo -e "${GREEN}(Currently connected)${NC}"
    fi
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

    if [ -f "$CONFIG_FILE" ] && command -v jq &> /dev/null; then
        # Determine config file format (deployment results vs deployments.json)
        local registry_id registry_obj registry_admin registry_date
        local hopfun_id hopfun_config hopfun_admin hopfun_date
        local hopdex_id hopdex_config hopdex_admin hopdex_date

        if [ "$USE_LATEST" = "true" ]; then
            # Using deployment results file format
            registry_id=$(jq -r ".deployments.registry.packageId // null" "$CONFIG_FILE")
            registry_obj=$(jq -r ".deployments.registry.registryId // null" "$CONFIG_FILE")
            registry_admin=$(jq -r ".deployments.registry.adminCapId // null" "$CONFIG_FILE")
            registry_date=$(jq -r ".timestamp // null" "$CONFIG_FILE")

            hopfun_id=$(jq -r ".deployments.hopfun.packageId // null" "$CONFIG_FILE")
            hopfun_config=$(jq -r ".deployments.hopfun.memeConfigId // null" "$CONFIG_FILE")
            hopfun_admin=$(jq -r ".deployments.hopfun.adminCapId // null" "$CONFIG_FILE")
            hopfun_date=$(jq -r ".timestamp // null" "$CONFIG_FILE")

            hopdex_id=$(jq -r ".deployments.hopdex.packageId // null" "$CONFIG_FILE")
            hopdex_config=$(jq -r ".deployments.hopdex.dexConfigId // null" "$CONFIG_FILE")
            hopdex_admin=$(jq -r ".deployments.hopdex.adminCapId // null" "$CONFIG_FILE")
            hopdex_date=$(jq -r ".timestamp // null" "$CONFIG_FILE")
        else
            # Using deployments.json format
            registry_id=$(jq -r ".deployments.$network.registry.packageId // null" "$CONFIG_FILE")
            registry_obj=$(jq -r ".deployments.$network.registry.registryId // null" "$CONFIG_FILE")
            registry_admin=$(jq -r ".deployments.$network.registry.adminCapId // null" "$CONFIG_FILE")
            registry_date=$(jq -r ".deployments.$network.registry.deployedAt // null" "$CONFIG_FILE")

            hopfun_id=$(jq -r ".deployments.$network.hopfun.packageId // null" "$CONFIG_FILE")
            hopfun_config=$(jq -r ".deployments.$network.hopfun.memeConfigId // null" "$CONFIG_FILE")
            hopfun_admin=$(jq -r ".deployments.$network.hopfun.adminCapId // null" "$CONFIG_FILE")
            hopfun_date=$(jq -r ".deployments.$network.hopfun.deployedAt // null" "$CONFIG_FILE")

            hopdex_id=$(jq -r ".deployments.$network.hopdex.packageId // null" "$CONFIG_FILE")
            hopdex_config=$(jq -r ".deployments.$network.hopdex.dexConfigId // null" "$CONFIG_FILE")
            hopdex_admin=$(jq -r ".deployments.$network.hopdex.adminCapId // null" "$CONFIG_FILE")
            hopdex_date=$(jq -r ".deployments.$network.hopdex.deployedAt // null" "$CONFIG_FILE")
        fi

        check_package "$network" "Registry" "$registry_id" "$registry_obj" "$registry_admin" "$registry_date" "Registry ID"
        check_package "$network" "HopFun" "$hopfun_id" "$hopfun_config" "$hopfun_admin" "$hopfun_date" "MemeConfig"
        check_package "$network" "HopDex" "$hopdex_id" "$hopdex_config" "$hopdex_admin" "$hopdex_date" "DexConfig"
    else
        echo -e "  ${RED}Cannot read deployment file${NC}"
    fi
}

# Check all networks
check_network "devnet"
check_network "testnet"
check_network "mainnet"

# Check bytecode dependencies
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BOLD}Bytecode Dependencies${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

BYTECODE_FILE="$PROJECT_ROOT/apps/frontend/src/services/bytecode-data.json"
if [ -f "$BYTECODE_FILE" ]; then
    BYTECODE_DEPS=$(jq -r '.dependencies[]' "$BYTECODE_FILE" 2>/dev/null)
    if [ -n "$BYTECODE_DEPS" ]; then
        echo -e "  Current bytecode dependencies:"
        i=1
        echo "$BYTECODE_DEPS" | while read -r dep; do
            case $i in
                1) echo -e "    1. Sui stdlib:      ${DIM}$dep${NC}" ;;
                2) echo -e "    2. Sui framework:   ${DIM}$dep${NC}" ;;
                3) 
                    REGISTRY_IN_CONFIG=$(jq -r ".deployments.$CURRENT_NET.registry.packageId // null" "$CONFIG_FILE")
                    if [ "$dep" = "$REGISTRY_IN_CONFIG" ]; then
                        echo -e "    3. Registry:        ${GREEN}✅ Matches config${NC}"
                    else
                        echo -e "    3. Registry:        ${RED}❌ Mismatch!${NC}"
                        echo -e "       Bytecode: ${CYAN}$dep${NC}"
                        echo -e "       Config:   ${CYAN}$REGISTRY_IN_CONFIG${NC}"
                    fi
                    ;;
                4)
                    HOPDEX_IN_CONFIG=$(jq -r ".deployments.$CURRENT_NET.hopdex.packageId // null" "$CONFIG_FILE")
                    if [ "$dep" = "$HOPDEX_IN_CONFIG" ]; then
                        echo -e "    4. HopDex:          ${GREEN}✅ Matches config${NC}"
                    else
                        echo -e "    4. HopDex:          ${RED}❌ Mismatch!${NC}"
                        echo -e "       Bytecode: ${CYAN}$dep${NC}"
                        echo -e "       Config:   ${CYAN}$HOPDEX_IN_CONFIG${NC}"
                    fi
                    ;;
                5)
                    HOPFUN_IN_CONFIG=$(jq -r ".deployments.$CURRENT_NET.hopfun.packageId // null" "$CONFIG_FILE")
                    if [ "$dep" = "$HOPFUN_IN_CONFIG" ]; then
                        echo -e "    5. HopFun:          ${GREEN}✅ Matches config${NC}"
                    else
                        echo -e "    5. HopFun:          ${RED}❌ Mismatch!${NC}"
                        echo -e "       Bytecode: ${CYAN}$dep${NC}"
                        echo -e "       Config:   ${CYAN}$HOPFUN_IN_CONFIG${NC}"
                    fi
                    ;;
            esac
            i=$((i+1))
        done
        
        # Check when bytecode was last generated
        BYTECODE_DATE=$(jq -r '.generatedAt // null' "$BYTECODE_FILE" 2>/dev/null)
        if [ "$BYTECODE_DATE" != "null" ] && [ -n "$BYTECODE_DATE" ]; then
            echo -e "\n  Last generated: ${DIM}$BYTECODE_DATE${NC}"
        fi
    else
        echo -e "  ${YELLOW}⚠️ No dependencies found in bytecode${NC}"
    fi
else
    echo -e "  ${RED}❌ bytecode-data.json not found${NC}"
fi

echo ""

# Summary and recommendations
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BOLD}Status Summary${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# Check if packages exist on current network
if [ "$CURRENT_NET" = "devnet" ] || [ "$CURRENT_NET" = "testnet" ] || [ "$CURRENT_NET" = "mainnet" ]; then
    REGISTRY_ID=$(jq -r ".deployments.$CURRENT_NET.registry.packageId // null" "$CONFIG_FILE")
    HOPFUN_ID=$(jq -r ".deployments.$CURRENT_NET.hopfun.packageId // null" "$CONFIG_FILE")
    HOPDEX_ID=$(jq -r ".deployments.$CURRENT_NET.hopdex.packageId // null" "$CONFIG_FILE")
    
    ISSUES_FOUND=false
    
    if [ "$REGISTRY_ID" != "null" ] && [ -n "$REGISTRY_ID" ]; then
        ON_CHAIN=$(check_on_chain "$REGISTRY_ID" "$CURRENT_NET")
        if [ "$ON_CHAIN" = "false" ]; then
            echo -e "  ${RED}⚠️ Registry package not found on $CURRENT_NET${NC}"
            ISSUES_FOUND=true
        fi
    fi
    
    if [ "$HOPFUN_ID" != "null" ] && [ -n "$HOPFUN_ID" ]; then
        ON_CHAIN=$(check_on_chain "$HOPFUN_ID" "$CURRENT_NET")
        if [ "$ON_CHAIN" = "false" ]; then
            echo -e "  ${RED}⚠️ HopFun package not found on $CURRENT_NET${NC}"
            ISSUES_FOUND=true
        fi
    fi
    
    if [ "$HOPDEX_ID" != "null" ] && [ -n "$HOPDEX_ID" ]; then
        ON_CHAIN=$(check_on_chain "$HOPDEX_ID" "$CURRENT_NET")
        if [ "$ON_CHAIN" = "false" ]; then
            echo -e "  ${RED}⚠️ HopDex package not found on $CURRENT_NET${NC}"
            ISSUES_FOUND=true
        fi
    fi
    
    if [ "$ISSUES_FOUND" = "true" ]; then
        echo ""
        echo -e "  ${YELLOW}Action Required:${NC}"
        echo -e "  The packages in config/deployments.json don't exist on $CURRENT_NET."
        echo -e "  You need to redeploy the contracts:"
        echo ""
        echo -e "  ${CYAN}bash scripts/deploy-all-contracts.sh${NC}"
    else
        # Check if all packages are deployed
        if [ "$REGISTRY_ID" != "null" ] && [ "$HOPFUN_ID" != "null" ] && [ "$HOPDEX_ID" != "null" ]; then
            echo -e "  ${GREEN}✅ All packages are deployed and verified on $CURRENT_NET${NC}"
            
            # Check bytecode dependencies (now 5 dependencies: stdlib, framework, registry, hopdex, hopfun)
            if [ -f "$BYTECODE_FILE" ]; then
                BYTECODE_REGISTRY=$(jq -r '.dependencies[2] // null' "$BYTECODE_FILE" 2>/dev/null)
                BYTECODE_HOPDEX=$(jq -r '.dependencies[3] // null' "$BYTECODE_FILE" 2>/dev/null)
                BYTECODE_HOPFUN=$(jq -r '.dependencies[4] // null' "$BYTECODE_FILE" 2>/dev/null)

                if [ "$BYTECODE_REGISTRY" != "$REGISTRY_ID" ] || [ "$BYTECODE_HOPDEX" != "$HOPDEX_ID" ] || [ "$BYTECODE_HOPFUN" != "$HOPFUN_ID" ]; then
                    echo ""
                    echo -e "  ${YELLOW}⚠️ Bytecode dependencies don't match deployed packages${NC}"
                    echo -e "  Rebuild the bytecode:"
                    echo -e "  ${CYAN}node scripts/extract-bytecode.js${NC}"
                fi
            fi
        else
            echo -e "  ${YELLOW}Some packages are not deployed on $CURRENT_NET${NC}"
        fi
    fi
else
    echo -e "  ${DIM}Connect to a network to verify deployment status${NC}"
fi

echo ""

# Quick actions
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BOLD}Quick Actions${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo ""
echo "  Deploy contracts:       bash scripts/deploy-all-contracts.sh"
echo "  Check deployment:       bash scripts/check-deployment.sh"
echo "  Check latest results:   bash scripts/check-deployment.sh --latest"
echo "  Rebuild bytecode:       node scripts/extract-bytecode.js"
echo "  Test token creation:    node scripts/test-final-token-creation.mjs"
echo ""
echo "  Switch to devnet:       sui client switch --env devnet"
echo "  Switch to testnet:      sui client switch --env testnet"
echo "  Get devnet SUI:         sui client faucet"
echo ""