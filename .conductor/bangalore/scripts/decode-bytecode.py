#!/usr/bin/env python3

import base64
import json
import sys

# Read the bytecode data
with open('apps/frontend/src/services/bytecode-data.json', 'r') as f:
    data = json.load(f)

# Decode the base64 bytecode
bytecode_base64 = data['bytecode']
bytecode_bytes = base64.b64decode(bytecode_base64)

# Convert to hex for analysis
hex_data = bytecode_bytes.hex()

print("Bytecode Analysis:")
print("=" * 50)
print(f"Total bytecode size: {len(bytecode_bytes)} bytes")
print(f"Hex representation length: {len(hex_data)}")
print()

# Look for potential addresses (32-byte sequences that look like addresses)
# Addresses in Move bytecode are typically 32 bytes (64 hex chars)
print("Looking for embedded addresses in bytecode...")
print("=" * 50)

# Find all 32-byte sequences that could be addresses
# Look specifically for known prefixes
known_prefixes = ['2f00ce42', 'ee6b9a80', 'b8de4579']

found_addresses = []
for prefix in known_prefixes:
    index = hex_data.find(prefix)
    if index != -1 and index % 2 == 0:  # Make sure we're at a byte boundary
        # Extract 32 bytes (64 hex chars)
        addr = hex_data[index:index+64]
        if len(addr) == 64:
            found_addresses.append('0x' + addr)
            print(f"Found address: 0x{addr}")

# Also look for the addresses in the dependencies
print()
print("Declared dependencies:")
print("=" * 50)
for i, dep in enumerate(data['dependencies']):
    print(f"{i+1}. {dep}")

# Check if found addresses match declared dependencies
print()
print("Address matching:")
print("=" * 50)
for addr in found_addresses:
    if addr in data['dependencies']:
        print(f"✅ {addr} is in dependencies")
    else:
        print(f"❌ {addr} is NOT in dependencies!")

# Look for any other potential addresses
print()
print("Scanning for other potential addresses...")
print("=" * 50)

# Look for sequences that start with common patterns
i = 0
potential_addrs = set()
while i < len(hex_data) - 64:
    # Check if this could be an address (starts with 0x00... or known patterns)
    segment = hex_data[i:i+64]
    # Check if it looks like an address (all hex chars)
    if all(c in '0123456789abcdef' for c in segment):
        # Check for patterns that look like addresses
        if segment.startswith('00000000') or any(segment.startswith(p) for p in known_prefixes):
            if len(segment) == 64:
                addr = '0x' + segment
                if addr not in found_addresses and addr != '0x' + '0' * 64:
                    potential_addrs.add(addr)
    i += 2

for addr in potential_addrs:
    print(f"Potential: {addr[:20]}...")