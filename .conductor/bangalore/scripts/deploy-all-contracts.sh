#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Deploying HopFun Smart Contracts to Devnet ===${NC}"

# Function to extract package ID from deployment output
extract_package_id() {
    local output="$1"
    # Look for the package ID in the output
    echo "$output" | grep -oE '0x[a-f0-9]{64}' | head -1
}

# Function to extract object IDs from deployment output
extract_object_id() {
    local output="$1"
    local object_type="$2"
    # This is simplified - you may need to parse the JSON output more carefully
    echo "$output" | grep -A2 "$object_type" | grep -oE '0x[a-f0-9]{64}' | head -1
}

# Check if we're on devnet
CURRENT_ENV=$(sui client active-env)
if [ "$CURRENT_ENV" != "devnet" ]; then
    echo -e "${YELLOW}Warning: Current environment is $CURRENT_ENV, not devnet${NC}"
    echo "Switching to devnet..."
    sui client switch --env devnet || {
        echo -e "${RED}Failed to switch to devnet. Please configure devnet first:${NC}"
        echo "sui client new-env --alias devnet --rpc https://fullnode.devnet.sui.io:443"
        exit 1
    }
fi

# Get the active address
ACTIVE_ADDRESS=$(sui client active-address)
echo -e "${BLUE}Deploying with address: $ACTIVE_ADDRESS${NC}"

# Check balance
echo -e "${YELLOW}Checking balance...${NC}"
# Get both MIST balance (for checking) and SUI balance (for display)
MIST_BALANCE=$(sui client gas --json 2>/dev/null | jq -r '.[0].mistBalance // "0"' 2>/dev/null || echo "0")
SUI_BALANCE=$(sui client gas --json 2>/dev/null | jq -r '.[0].suiBalance // "0"' 2>/dev/null || echo "0")

# Check if we have sufficient balance (using MIST balance for accuracy)
if [ "$MIST_BALANCE" = "0" ] || [ -z "$MIST_BALANCE" ] || [ "$MIST_BALANCE" = "null" ]; then
    echo -e "${RED}❌ Error: No SUI balance available!${NC}"
    echo -e "${YELLOW}You need SUI to deploy contracts. Please get test SUI from the faucet:${NC}"
    echo ""
    echo -e "${CYAN}Option 1: Use the web faucet${NC}"
    echo "  1. Visit: https://faucet.sui.io/"
    echo "  2. Select 'Devnet' network"
    echo "  3. Enter your address: $ACTIVE_ADDRESS"
    echo "  4. Click 'Request SUI Tokens'"
    echo ""
    echo -e "${CYAN}Option 2: Use Discord faucet${NC}"
    echo "  1. Join Sui Discord: https://discord.gg/sui"
    echo "  2. Go to #devnet-faucet channel"
    echo "  3. Type: !faucet $ACTIVE_ADDRESS"
    echo ""
    echo -e "${CYAN}Option 3: Try CLI (may not work):${NC}"
    echo "  sui client faucet"
    echo ""
    exit 1
fi

# Display balance (suiBalance is already in SUI units)
echo -e "${GREEN}Balance: $SUI_BALANCE SUI${NC}"

# Create deployment results file
DEPLOYMENT_RESULTS="deployment-results-$(date +%s).json"
echo "{" > "$DEPLOYMENT_RESULTS"
echo '  "network": "devnet",' >> "$DEPLOYMENT_RESULTS"
echo '  "deployer": "'$ACTIVE_ADDRESS'",' >> "$DEPLOYMENT_RESULTS"
echo '  "timestamp": "'$(date -u +"%Y-%m-%dT%H:%M:%SZ")'",' >> "$DEPLOYMENT_RESULTS"
echo '  "deployments": {' >> "$DEPLOYMENT_RESULTS"

# Step 1: Reset all Move.toml files to 0x0 for deployment
echo -e "\n${YELLOW}Step 1: Resetting Move.toml files for deployment...${NC}"

# Reset config_registry Move.toml
cat > contracts/config_registry/Move.toml << EOF
[package]
name = "config_registry"
edition = "2024.beta"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }

[addresses]
config_registry = "0x0"
EOF

# Reset hopdex Move.toml
cat > contracts/hopdex/Move.toml << EOF
[package]
name = "hopdex"
edition = "2024.beta"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }

[addresses]
hopdex = "0x0"
EOF

# Reset hopfun Move.toml
cat > contracts/hopfun/Move.toml << EOF
[package]
name = "hopfun"
edition = "2024.beta"
authors = ["Bonkman (@bonkman22)"]

[dependencies]
hopdex = { local = "../hopdex" }
config_registry = { local = "../config_registry" }

[addresses]
hopfun = "0x0"
EOF

echo "✅ All Move.toml files reset to 0x0"

# Step 2: Deploy config_registry
echo -e "\n${YELLOW}Step 2: Deploying config_registry...${NC}"
cd contracts/config_registry

echo "Building config_registry..."
sui move build --skip-fetch-latest-git-deps 2>&1

echo "Publishing config_registry..."
# Store output to a file first to handle mixed output
TEMP_OUTPUT="/tmp/sui_publish_output_$$.json"
sui client publish --gas-budget 100000000 --json 2>/dev/null > "$TEMP_OUTPUT" || {
    echo -e "${RED}Failed to publish config_registry${NC}"
    cat "$TEMP_OUTPUT"
    rm -f "$TEMP_OUTPUT"
    exit 1
}

# Extract JSON from the output (it might have warnings before the JSON)
# Try to find where the JSON starts (usually with '{')
JSON_OUTPUT=$(cat "$TEMP_OUTPUT" | sed -n '/^{/,$p')
rm -f "$TEMP_OUTPUT"

if [ -z "$JSON_OUTPUT" ]; then
    echo -e "${RED}No JSON output from sui client publish${NC}"
    exit 1
fi

# Parse the package ID from the JSON
REGISTRY_PACKAGE_ID=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "published") | .packageId' 2>/dev/null || echo "")

if [ -z "$REGISTRY_PACKAGE_ID" ] || [ "$REGISTRY_PACKAGE_ID" = "null" ]; then
    echo -e "${RED}Failed to extract package ID for config_registry${NC}"
    echo "Output was:"
    echo "$JSON_OUTPUT" | head -20
    exit 1
fi

echo -e "${GREEN}✅ config_registry deployed: $REGISTRY_PACKAGE_ID${NC}"

# Extract the ConfigRegistry shared object ID
REGISTRY_ID=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.objectType | contains("ConfigRegistry")) | .objectId' 2>/dev/null || echo "")
REGISTRY_ADMIN_CAP=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.objectType | contains("AdminCap")) | .objectId' 2>/dev/null || echo "")

# If we can't find the objects, list all created objects
if [ -z "$REGISTRY_ID" ] || [ "$REGISTRY_ID" = "null" ]; then
    echo "Looking for ConfigRegistry object..."
    REGISTRY_ID=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "created") | select(.objectType | contains("registry")) | .objectId' 2>/dev/null | head -1)
fi

if [ -z "$REGISTRY_ADMIN_CAP" ] || [ "$REGISTRY_ADMIN_CAP" = "null" ]; then
    echo "Looking for AdminCap object..."
    REGISTRY_ADMIN_CAP=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "created") | select(.objectType | contains("AdminCap")) | .objectId' 2>/dev/null | head -1)
fi

echo "  Registry ID: ${REGISTRY_ID:-not_found}"
echo "  Admin Cap: ${REGISTRY_ADMIN_CAP:-not_found}"

echo '    "registry": {' >> "../../$DEPLOYMENT_RESULTS"
echo '      "packageId": "'$REGISTRY_PACKAGE_ID'",' >> "../../$DEPLOYMENT_RESULTS"
echo '      "registryId": "'${REGISTRY_ID:-}'",' >> "../../$DEPLOYMENT_RESULTS"
echo '      "adminCapId": "'${REGISTRY_ADMIN_CAP:-}'"' >> "../../$DEPLOYMENT_RESULTS"
echo '    },' >> "../../$DEPLOYMENT_RESULTS"

cd ../..

# Step 3: Deploy hopdex
echo -e "\n${YELLOW}Step 3: Deploying hopdex...${NC}"
cd contracts/hopdex

# Reset Move.toml
cat > Move.toml << EOF
[package]
name = "hopdex"
edition = "2024.beta"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }

[addresses]
hopdex = "0x0"
EOF

echo "Building hopdex..."
sui move build --skip-fetch-latest-git-deps 2>&1

echo "Publishing hopdex..."
TEMP_OUTPUT="/tmp/sui_publish_output_$$.json"
sui client publish --gas-budget 100000000 --json 2>/dev/null > "$TEMP_OUTPUT" || {
    echo -e "${RED}Failed to publish hopdex${NC}"
    cat "$TEMP_OUTPUT"
    rm -f "$TEMP_OUTPUT"
    exit 1
}

JSON_OUTPUT=$(cat "$TEMP_OUTPUT" | sed -n '/^{/,$p')
rm -f "$TEMP_OUTPUT"

if [ -z "$JSON_OUTPUT" ]; then
    echo -e "${RED}No JSON output from sui client publish${NC}"
    exit 1
fi

HOPDEX_PACKAGE_ID=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "published") | .packageId' 2>/dev/null || echo "")

if [ -z "$HOPDEX_PACKAGE_ID" ] || [ "$HOPDEX_PACKAGE_ID" = "null" ]; then
    echo -e "${RED}Failed to extract package ID for hopdex${NC}"
    echo "Output was:"
    echo "$JSON_OUTPUT" | head -20
    exit 1
fi

echo -e "${GREEN}✅ hopdex deployed: $HOPDEX_PACKAGE_ID${NC}"

# Extract hopdex objects
# Look for DexConfig (shared object) and AdminCap (owned object)
HOPDEX_CONFIG_ID=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "created") | select(.objectType | contains("config::DexConfig")) | .objectId' 2>/dev/null || echo "")
HOPDEX_ADMIN_CAP=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "created") | select(.objectType | contains("config::AdminCap")) | .objectId' 2>/dev/null || echo "")

# If we can't find the objects with full path, try shorter patterns
if [ -z "$HOPDEX_CONFIG_ID" ] || [ "$HOPDEX_CONFIG_ID" = "null" ]; then
    echo "Looking for DexConfig object..."
    HOPDEX_CONFIG_ID=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "created") | select(.objectType | contains("DexConfig")) | .objectId' 2>/dev/null | head -1)
fi

if [ -z "$HOPDEX_ADMIN_CAP" ] || [ "$HOPDEX_ADMIN_CAP" = "null" ]; then
    echo "Looking for AdminCap object..."
    HOPDEX_ADMIN_CAP=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "created") | select(.objectType | contains("AdminCap")) | .objectId' 2>/dev/null | head -1)
fi

# Debug: Show all created objects if we still can't find DexConfig
if [ -z "$HOPDEX_CONFIG_ID" ] || [ "$HOPDEX_CONFIG_ID" = "null" ]; then
    echo "Debug: All created objects:"
    echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "created") | "\(.objectType): \(.objectId)"' 2>/dev/null || echo "No objects found"
fi

echo "  DEX Config: ${HOPDEX_CONFIG_ID:-not_found}"
echo "  Admin Cap: ${HOPDEX_ADMIN_CAP:-not_found}"

echo '    "hopdex": {' >> "../../$DEPLOYMENT_RESULTS"
echo '      "packageId": "'$HOPDEX_PACKAGE_ID'",' >> "../../$DEPLOYMENT_RESULTS"
echo '      "dexConfigId": "'${HOPDEX_CONFIG_ID:-}'",' >> "../../$DEPLOYMENT_RESULTS"
echo '      "adminCapId": "'${HOPDEX_ADMIN_CAP:-}'"' >> "../../$DEPLOYMENT_RESULTS"
echo '    },' >> "../../$DEPLOYMENT_RESULTS"

cd ../..

# Step 4: Deploy hopfun with dependencies
echo -e "\n${YELLOW}Step 4: Deploying hopfun...${NC}"
cd contracts/hopfun

# Update Move.toml with deployed addresses
cat > Move.toml << EOF
[package]
name = "hopfun"
edition = "2024.beta"
authors = ["Bonkman (@bonkman22)"]

[dependencies]
hopdex = { local = "../hopdex" }
config_registry = { local = "../config_registry" }

[addresses]
hopfun = "0x0"
EOF

# Update the dependency Move.tomls with their deployed addresses
cat > ../config_registry/Move.toml << EOF
[package]
name = "config_registry"
edition = "2024.beta"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }

[addresses]
config_registry = "$REGISTRY_PACKAGE_ID"
EOF

cat > ../hopdex/Move.toml << EOF
[package]
name = "hopdex"
edition = "2024.beta"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }

[addresses]
hopdex = "$HOPDEX_PACKAGE_ID"
EOF

echo "Building hopfun..."
sui move build --skip-fetch-latest-git-deps 2>&1

echo "Publishing hopfun..."
TEMP_OUTPUT="/tmp/sui_publish_output_$$.json"
sui client publish --gas-budget 200000000 --json 2>/dev/null > "$TEMP_OUTPUT" || {
    echo -e "${RED}Failed to publish hopfun${NC}"
    cat "$TEMP_OUTPUT"
    rm -f "$TEMP_OUTPUT"
    exit 1
}

JSON_OUTPUT=$(cat "$TEMP_OUTPUT" | sed -n '/^{/,$p')
rm -f "$TEMP_OUTPUT"

if [ -z "$JSON_OUTPUT" ]; then
    echo -e "${RED}No JSON output from sui client publish${NC}"
    exit 1
fi

HOPFUN_PACKAGE_ID=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "published") | .packageId' 2>/dev/null || echo "")

if [ -z "$HOPFUN_PACKAGE_ID" ] || [ "$HOPFUN_PACKAGE_ID" = "null" ]; then
    echo -e "${RED}Failed to extract package ID for hopfun${NC}"
    echo "Output was:"
    echo "$JSON_OUTPUT" | head -20
    exit 1
fi

echo -e "${GREEN}✅ hopfun deployed: $HOPFUN_PACKAGE_ID${NC}"

# Extract hopfun objects
# Look for MemeConfig (shared object) and AdminCap (owned object)
MEME_CONFIG_ID=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "created") | select(.objectType | contains("config::MemeConfig")) | .objectId' 2>/dev/null || echo "")
HOPFUN_ADMIN_CAP=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "created") | select(.objectType | contains("config::AdminCap")) | .objectId' 2>/dev/null || echo "")

# If we can't find the objects with full path, try shorter patterns
if [ -z "$MEME_CONFIG_ID" ] || [ "$MEME_CONFIG_ID" = "null" ]; then
    echo "Looking for MemeConfig object..."
    MEME_CONFIG_ID=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "created") | select(.objectType | contains("MemeConfig")) | .objectId' 2>/dev/null | head -1)
fi

if [ -z "$HOPFUN_ADMIN_CAP" ] || [ "$HOPFUN_ADMIN_CAP" = "null" ]; then
    echo "Looking for AdminCap object..."
    HOPFUN_ADMIN_CAP=$(echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "created") | select(.objectType | contains("AdminCap")) | .objectId' 2>/dev/null | head -1)
fi

# Debug: Show all created objects if we still can't find MemeConfig
if [ -z "$MEME_CONFIG_ID" ] || [ "$MEME_CONFIG_ID" = "null" ]; then
    echo "Debug: All created objects:"
    echo "$JSON_OUTPUT" | jq -r '.objectChanges[] | select(.type == "created") | "\(.objectType): \(.objectId)"' 2>/dev/null || echo "No objects found"
fi

echo "  Meme Config: ${MEME_CONFIG_ID:-not_found}"
echo "  Admin Cap: ${HOPFUN_ADMIN_CAP:-not_found}"

echo '    "hopfun": {' >> "../../$DEPLOYMENT_RESULTS"
echo '      "packageId": "'$HOPFUN_PACKAGE_ID'",' >> "../../$DEPLOYMENT_RESULTS"
echo '      "memeConfigId": "'${MEME_CONFIG_ID:-}'",' >> "../../$DEPLOYMENT_RESULTS"
echo '      "adminCapId": "'${HOPFUN_ADMIN_CAP:-}'"' >> "../../$DEPLOYMENT_RESULTS"
echo '    }' >> "../../$DEPLOYMENT_RESULTS"

cd ../..

# Close the JSON
echo '  }' >> "$DEPLOYMENT_RESULTS"
echo '}' >> "$DEPLOYMENT_RESULTS"

# Step 5: Update the deployments.json file
echo -e "\n${YELLOW}Step 5: Updating deployments.json...${NC}"

# Create a new deployments.json with the actual deployed addresses
cat > config/deployments.json << EOF
{
  "deployments": {
    "devnet": {
      "registry": {
        "packageId": "$REGISTRY_PACKAGE_ID",
        "registryId": "${REGISTRY_ID:-}",
        "adminCapId": "${REGISTRY_ADMIN_CAP:-}",
        "deployedAt": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
      },
      "hopfun": {
        "packageId": "$HOPFUN_PACKAGE_ID",
        "memeConfigId": "${MEME_CONFIG_ID:-}",
        "adminCapId": "${HOPFUN_ADMIN_CAP:-}",
        "deployedAt": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
      },
      "hopdex": {
        "packageId": "$HOPDEX_PACKAGE_ID",
        "dexConfigId": "${HOPDEX_CONFIG_ID:-}",
        "adminCapId": "${HOPDEX_ADMIN_CAP:-}",
        "deployedAt": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
      }
    },
    "testnet": {
      "registry": { "packageId": null },
      "hopfun": { "packageId": null },
      "hopdex": { "packageId": null }
    },
    "mainnet": {
      "registry": { "packageId": null },
      "hopfun": { "packageId": null },
      "hopdex": { "packageId": null }
    }
  }
}
EOF

# Step 6: Update all Move.toml files with final deployed addresses
echo -e "\n${YELLOW}Step 6: Updating all Move.toml files with deployed addresses...${NC}"

# Function to update Move.toml files
update_move_toml() {
    local contract_dir=$1
    local package_name=$2
    local package_id=$3

    echo "Updating $contract_dir/Move.toml with $package_name = $package_id"

    if [ "$package_name" = "config_registry" ]; then
        cat > "$contract_dir/Move.toml" << EOF
[package]
name = "config_registry"
edition = "2024.beta"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }

[addresses]
config_registry = "$package_id"
EOF
    elif [ "$package_name" = "hopdex" ]; then
        cat > "$contract_dir/Move.toml" << EOF
[package]
name = "hopdex"
edition = "2024.beta"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }

[addresses]
hopdex = "$package_id"
EOF
    elif [ "$package_name" = "hopfun" ]; then
        cat > "$contract_dir/Move.toml" << EOF
[package]
name = "hopfun"
edition = "2024.beta"
authors = ["Bonkman (@bonkman22)"]

[dependencies]
hopdex = { local = "../hopdex" }
config_registry = { local = "../config_registry" }

[addresses]
hopfun = "$package_id"
EOF
    fi
}

# Update all Move.toml files with the final deployed addresses
update_move_toml "contracts/config_registry" "config_registry" "$REGISTRY_PACKAGE_ID"
update_move_toml "contracts/hopdex" "hopdex" "$HOPDEX_PACKAGE_ID"
update_move_toml "contracts/hopfun" "hopfun" "$HOPFUN_PACKAGE_ID"

# Step 7: Rebuild the coin_template bytecode with new addresses
echo -e "\n${YELLOW}Step 7: Rebuilding coin_template bytecode...${NC}"

# Clean build cache to ensure fresh build
echo "Cleaning build cache..."
rm -rf contracts/*/build/

# Now rebuild the coin_template bytecode
echo "Rebuilding coin_template bytecode..."
if node scripts/extract-bytecode.js; then
    echo -e "${GREEN}✅ Bytecode rebuilt successfully${NC}"
else
    echo -e "${RED}❌ Failed to rebuild bytecode${NC}"
    exit 1
fi

# Step 8: Validate deployment consistency
echo -e "\n${YELLOW}Step 8: Validating deployment consistency...${NC}"

# Check that config/deployments.json has the correct package IDs
DEPLOYED_REGISTRY=$(jq -r ".deployments.devnet.registry.packageId" config/deployments.json 2>/dev/null)
DEPLOYED_HOPFUN=$(jq -r ".deployments.devnet.hopfun.packageId" config/deployments.json 2>/dev/null)
DEPLOYED_HOPDEX=$(jq -r ".deployments.devnet.hopdex.packageId" config/deployments.json 2>/dev/null)

VALIDATION_PASSED=true

if [ "$DEPLOYED_REGISTRY" != "$REGISTRY_PACKAGE_ID" ]; then
    echo -e "${RED}❌ Registry package ID mismatch in config/deployments.json${NC}"
    VALIDATION_PASSED=false
fi

if [ "$DEPLOYED_HOPFUN" != "$HOPFUN_PACKAGE_ID" ]; then
    echo -e "${RED}❌ HopFun package ID mismatch in config/deployments.json${NC}"
    VALIDATION_PASSED=false
fi

if [ "$DEPLOYED_HOPDEX" != "$HOPDEX_PACKAGE_ID" ]; then
    echo -e "${RED}❌ HopDex package ID mismatch in config/deployments.json${NC}"
    VALIDATION_PASSED=false
fi

# Check bytecode dependencies
if [ -f "apps/frontend/src/services/bytecode-data.json" ]; then
    BYTECODE_REGISTRY=$(jq -r '.dependencies[2]' apps/frontend/src/services/bytecode-data.json 2>/dev/null)
    BYTECODE_HOPFUN=$(jq -r '.dependencies[3]' apps/frontend/src/services/bytecode-data.json 2>/dev/null)

    if [ "$BYTECODE_REGISTRY" != "$REGISTRY_PACKAGE_ID" ]; then
        echo -e "${RED}❌ Bytecode registry dependency mismatch${NC}"
        VALIDATION_PASSED=false
    fi

    if [ "$BYTECODE_HOPFUN" != "$HOPFUN_PACKAGE_ID" ]; then
        echo -e "${RED}❌ Bytecode hopfun dependency mismatch${NC}"
        VALIDATION_PASSED=false
    fi
else
    echo -e "${RED}❌ Bytecode file not found${NC}"
    VALIDATION_PASSED=false
fi

if [ "$VALIDATION_PASSED" = "true" ]; then
    echo -e "${GREEN}✅ All deployment files are consistent${NC}"
else
    echo -e "${RED}❌ Deployment validation failed${NC}"
    exit 1
fi

echo -e "\n${GREEN}=== Deployment Complete! ===${NC}"
echo -e "${BLUE}Deployment results saved to: $DEPLOYMENT_RESULTS${NC}"
echo ""
echo -e "${GREEN}Deployed Packages:${NC}"
echo "  Registry: $REGISTRY_PACKAGE_ID"
echo "  HopDex: $HOPDEX_PACKAGE_ID"
echo "  HopFun: $HOPFUN_PACKAGE_ID"
echo ""
echo -e "${GREEN}Shared Objects:${NC}"
echo "  Registry ID: ${REGISTRY_ID:-not_found}"
echo "  Meme Config: ${MEME_CONFIG_ID:-not_found}"
echo "  DEX Config: ${HOPDEX_CONFIG_ID:-not_found}"
echo ""
echo -e "${GREEN}Updated Files:${NC}"
echo "  ✅ config/deployments.json"
echo "  ✅ contracts/config_registry/Move.toml"
echo "  ✅ contracts/hopdex/Move.toml"
echo "  ✅ contracts/hopfun/Move.toml"
echo "  ✅ apps/frontend/src/services/bytecode-data.json"
echo ""
echo -e "${YELLOW}Next steps:${NC}"
echo "1. All files have been automatically updated with the correct addresses"
echo "2. Run 'bash scripts/check-deployment.sh' to verify everything is working"
echo "3. Try creating a token from the frontend now"
echo "4. If you still get errors, run: node scripts/test-final-token-creation.mjs"