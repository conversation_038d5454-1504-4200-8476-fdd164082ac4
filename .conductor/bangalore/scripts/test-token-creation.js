#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for better output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(cmd, options = {}) {
  try {
    return execSync(cmd, { encoding: 'utf8', ...options });
  } catch (error) {
    if (!options.ignoreError) {
      throw error;
    }
    return error.stdout || error.message;
  }
}

async function testTokenCreation() {
  log('\n========================================', 'magenta');
  log('   Token Creation Test', 'magenta');
  log('========================================\n', 'magenta');

  // Load configurations
  const bytecodePath = path.join(__dirname, '..', 'apps', 'frontend', 'src', 'services', 'bytecode-data.json');
  const deploymentsPath = path.join(__dirname, '..', 'config', 'deployments.json');
  
  const bytecodeData = JSON.parse(fs.readFileSync(bytecodePath, 'utf8'));
  const deployments = JSON.parse(fs.readFileSync(deploymentsPath, 'utf8'));
  const devnet = deployments.deployments.devnet;
  
  log('📦 Testing Token Creation Flow', 'blue');
  log('================================\n', 'blue');
  
  // Step 1: Verify bytecode is ready
  log('1️⃣  Checking bytecode configuration...', 'cyan');
  
  if (!bytecodeData.bytecode) {
    log('   ❌ No bytecode found', 'red');
    process.exit(1);
  }
  
  if (!bytecodeData.dependencies || bytecodeData.dependencies.length !== 4) {
    log('   ❌ Invalid dependencies', 'red');
    process.exit(1);
  }
  
  log('   ✅ Bytecode: Ready', 'green');
  log(`   ✅ Dependencies: ${bytecodeData.dependencies.length} packages`, 'green');
  log(`      - Sui Stdlib: 0x1`, 'cyan');
  log(`      - Sui Framework: 0x2`, 'cyan');
  log(`      - Config Registry: ${bytecodeData.dependencies[2].substring(0, 16)}...`, 'cyan');
  log(`      - HopFun: ${bytecodeData.dependencies[3].substring(0, 16)}...`, 'cyan');
  
  // Step 2: Verify registry is accessible
  log('\n2️⃣  Checking registry configuration...', 'cyan');
  
  const registryId = devnet.registry.registryId;
  if (!registryId) {
    log('   ❌ No registry ID found', 'red');
    process.exit(1);
  }
  
  const registryCheck = runCommand(
    `sui client object ${registryId} --json 2>/dev/null`,
    { ignoreError: true }
  );
  
  try {
    const registryData = JSON.parse(registryCheck);
    if (registryData.data && registryData.data.content) {
      log('   ✅ Registry: Accessible', 'green');
      const fields = registryData.data.content.fields;
      if (fields) {
        log(`   ✅ MemeConfig: ${fields.meme_config_address ? 'Set' : 'Not set'}`, 
            fields.meme_config_address ? 'green' : 'yellow');
        log(`   ✅ DexConfig: ${fields.dex_config_address ? 'Set' : 'Not set'}`,
            fields.dex_config_address ? 'green' : 'yellow');
      }
    } else {
      log('   ⚠️  Registry exists but cannot read content', 'yellow');
    }
  } catch (e) {
    log('   ❌ Registry not accessible', 'red');
  }
  
  // Step 3: Simulate publish transaction
  log('\n3️⃣  Simulating publish transaction...', 'cyan');
  
  log('   📝 Transaction structure:', 'cyan');
  log('      - Command: tx.publish()', 'cyan');
  log('      - Modules: [bytecode]', 'cyan');
  log('      - Dependencies: [0x1, 0x2, registry, hopfun]', 'cyan');
  
  // Create a test transaction (dry run)
  const testPublishCmd = `sui client dry-run --json '
  {
    "version": "1",
    "sender": "0x0",
    "kind": {
      "ProgrammableTransaction": {
        "inputs": [],
        "commands": [
          {
            "Publish": {
              "modules": ["${bytecodeData.bytecode}"],
              "dependencies": ${JSON.stringify(bytecodeData.dependencies)}
            }
          }
        ]
      }
    }
  }' 2>&1`;
  
  log('\n   🔄 Attempting dry run of publish transaction...', 'cyan');
  const dryRunResult = runCommand(testPublishCmd, { ignoreError: true });
  
  if (dryRunResult.includes('PublishUpgradeMissingDependency')) {
    log('   ❌ Publish would fail: Missing dependency', 'red');
    log('      This means one of the dependencies is not found on-chain', 'yellow');
    
    // Check each dependency
    log('\n   🔍 Checking each dependency on-chain...', 'cyan');
    for (let i = 2; i < bytecodeData.dependencies.length; i++) {
      const depId = bytecodeData.dependencies[i];
      const checkCmd = `sui client object ${depId} --json 2>/dev/null`;
      const result = runCommand(checkCmd, { ignoreError: true });
      
      try {
        const data = JSON.parse(result);
        if (data.data) {
          log(`      ✅ Dependency ${i - 1}: Found on-chain`, 'green');
        } else {
          log(`      ❌ Dependency ${i - 1}: Not found on-chain`, 'red');
          log(`         ID: ${depId}`, 'yellow');
        }
      } catch (e) {
        log(`      ❌ Dependency ${i - 1}: Not found on-chain`, 'red');
        log(`         ID: ${depId}`, 'yellow');
      }
    }
  } else if (dryRunResult.includes('success') || dryRunResult.includes('effects')) {
    log('   ✅ Publish transaction would succeed!', 'green');
  } else {
    log('   ⚠️  Could not determine publish status', 'yellow');
    log(`      Response: ${dryRunResult.substring(0, 200)}...`, 'yellow');
  }
  
  // Step 4: Summary and recommendations
  log('\n========================================', 'magenta');
  log('   Test Summary', 'magenta');
  log('========================================\n', 'magenta');
  
  const checksPass = {
    bytecode: !!bytecodeData.bytecode,
    dependencies: bytecodeData.dependencies && bytecodeData.dependencies.length === 4,
    registry: !!registryId,
    correctDeps: bytecodeData.dependencies &&
                 bytecodeData.dependencies[2] === devnet.registry.packageId &&
                 bytecodeData.dependencies[3] === devnet.hopfun.packageId
  };
  
  let allPass = true;
  for (const [check, pass] of Object.entries(checksPass)) {
    if (pass) {
      log(`✅ ${check}: PASSED`, 'green');
    } else {
      log(`❌ ${check}: FAILED`, 'red');
      allPass = false;
    }
  }
  
  if (allPass) {
    log('\n🎉 System is ready for token creation!', 'green');
    log('\n📝 Next steps:', 'blue');
    log('   1. Ensure your wallet is connected in the frontend', 'cyan');
    log('   2. Fill in token details in the launch dialog', 'cyan');
    log('   3. Click "Launch Token" to create your token', 'cyan');
    log('   4. Approve the two transactions (publish + create_connector)', 'cyan');
  } else {
    log('\n⚠️  System needs configuration', 'yellow');
    log('\n📝 To fix:', 'blue');
    log('   1. Run: ./scripts/deploy.sh', 'cyan');
    log('   2. Run: node scripts/extract-bytecode.js', 'cyan');
    log('   3. Run this test again', 'cyan');
  }
}

// Run the test
testTokenCreation().catch(error => {
  log(`\n❌ Test failed: ${error.message}`, 'red');
  process.exit(1);
});