#!/bin/bash

#######################################
# Fix Move Dependencies After Deployment
# Description: Updates Move.toml files with deployed package addresses
#######################################

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'
BOLD='\033[1m'

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONTRACTS_DIR="$PROJECT_ROOT/contracts"
CONFIG_FILE="$PROJECT_ROOT/config/deployments.json"

echo -e "${CYAN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${CYAN}║${BOLD}             Fix Move Dependencies                           ${NC}${CYAN}║${NC}"
echo -e "${CYAN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Get current network
NETWORK=$(sui client active-env 2>/dev/null || echo "devnet")
echo -e "${BLUE}Current network: ${CYAN}$NETWORK${NC}"
echo ""

# Function to update Move.toml with published address
update_move_toml() {
    local package_name=$1
    local package_id=$2
    local move_toml="$CONTRACTS_DIR/$package_name/Move.toml"
    
    if [ -f "$move_toml" ]; then
        echo -e "${BLUE}Updating $package_name/Move.toml...${NC}"
        
        # Add or update published-at field
        if grep -q "^published-at = " "$move_toml"; then
            # Update existing published-at
            sed -i.bak "s/^published-at = .*/published-at = \"$package_id\"/" "$move_toml"
        else
            # Add published-at after [package] section
            sed -i.bak "/^\[package\]/a\\
published-at = \"$package_id\"" "$move_toml"
        fi
        
        # Update address
        sed -i.bak "s/^$package_name = .*/hopfun = \"$package_id\"/" "$move_toml"
        
        # Clean up backup files
        rm -f "$move_toml.bak"
        
        echo -e "${GREEN}✅ Updated $package_name${NC}"
    else
        echo -e "${RED}❌ $move_toml not found${NC}"
    fi
}

# Check if jq is available
if ! command -v jq &> /dev/null; then
    echo -e "${RED}❌ jq is required but not installed${NC}"
    echo "   Install with: brew install jq (macOS) or apt-get install jq (Linux)"
    exit 1
fi

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${YELLOW}⚠️  No deployment configuration found${NC}"
    echo "   Run deployment first: ./scripts/sui-deploy.sh deploy $NETWORK"
    exit 1
fi

# Get deployed addresses
HOPFUN_ID=$(jq -r ".deployments.$NETWORK.hopfun.packageId // null" "$CONFIG_FILE")
HOPDEX_ID=$(jq -r ".deployments.$NETWORK.hopdex.packageId // null" "$CONFIG_FILE")

echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
echo -e "${BOLD}Deployed Packages:${NC}"
echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

if [ "$HOPDEX_ID" != "null" ] && [ -n "$HOPDEX_ID" ]; then
    echo -e "HopDex:  ${CYAN}$HOPDEX_ID${NC}"
else
    echo -e "HopDex:  ${YELLOW}Not deployed${NC}"
fi

if [ "$HOPFUN_ID" != "null" ] && [ -n "$HOPFUN_ID" ]; then
    echo -e "HopFun:  ${CYAN}$HOPFUN_ID${NC}"
else
    echo -e "HopFun:  ${YELLOW}Not deployed${NC}"
fi

echo ""

# Update Move.toml files
if [ "$HOPFUN_ID" != "null" ] && [ -n "$HOPFUN_ID" ]; then
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${BOLD}Updating Move.toml Files:${NC}"
    echo -e "${BLUE}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    
    # Update hopfun Move.toml
    update_move_toml "hopfun" "$HOPFUN_ID"
    
    # Update coin_template dependency to use published hopfun
    COIN_TEMPLATE_TOML="$CONTRACTS_DIR/coin_template/Move.toml"
    if [ -f "$COIN_TEMPLATE_TOML" ]; then
        echo -e "${BLUE}Updating coin_template/Move.toml dependency...${NC}"
        
        # Change local dependency to published address
        sed -i.bak "s|hopfun = { local = \"../hopfun\" }|hopfun = { address = \"$HOPFUN_ID\" }|" "$COIN_TEMPLATE_TOML"
        rm -f "$COIN_TEMPLATE_TOML.bak"
        
        echo -e "${GREEN}✅ Updated coin_template dependency${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}✅ Dependencies fixed!${NC}"
    echo ""
    echo -e "${CYAN}You can now build coin_template normally:${NC}"
    echo "  cd contracts/coin_template"
    echo "  sui move build --dump-bytecode-as-base64"
else
    echo -e "${YELLOW}⚠️  HopFun not deployed yet${NC}"
    echo ""
    echo -e "${CYAN}To fix this issue:${NC}"
    echo "1. Deploy hopfun first:"
    echo "   ${BOLD}./scripts/sui-deploy.sh deploy $NETWORK${NC}"
    echo ""
    echo "2. Then run this script again:"
    echo "   ${BOLD}./scripts/fix-dependencies.sh${NC}"
    echo ""
    echo "3. Build coin_template:"
    echo "   ${BOLD}cd contracts/coin_template && sui move build --dump-bytecode-as-base64${NC}"
fi

echo ""