#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}=== Fixing coin_template dependencies and rebuilding bytecode ===${NC}"

# Read the deployed addresses from config
DEPLOYMENTS_FILE="config/deployments.json"
if [ ! -f "$DEPLOYMENTS_FILE" ]; then
    echo -e "${RED}Error: deployments.json not found at $DEPLOYMENTS_FILE${NC}"
    exit 1
fi

# Extract deployed addresses for devnet
REGISTRY_ADDR=$(jq -r '.deployments.devnet.registry.packageId' "$DEPLOYMENTS_FILE")
HOPFUN_ADDR=$(jq -r '.deployments.devnet.hopfun.packageId' "$DEPLOYMENTS_FILE")

echo -e "${YELLOW}Deployed addresses:${NC}"
echo "  Registry: $REGISTRY_ADDR"
echo "  HopFun: $HOPFUN_ADDR"

# Backup the original Move.toml files
echo -e "${YELLOW}Backing up original Move.toml files...${NC}"
cp contracts/coin_template/Move.toml contracts/coin_template/Move.toml.backup
cp contracts/hopfun/Move.toml contracts/hopfun/Move.toml.backup
cp contracts/config_registry/Move.toml contracts/config_registry/Move.toml.backup

# Update the Move.toml files with deployed addresses
echo -e "${YELLOW}Updating Move.toml files with deployed addresses...${NC}"

# Update config_registry/Move.toml
cat > contracts/config_registry/Move.toml << EOF
[package]
name = "config_registry"
edition = "2024.beta"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/devnet" }

[addresses]
config_registry = "$REGISTRY_ADDR"
EOF

# Update hopfun/Move.toml
cat > contracts/hopfun/Move.toml << EOF
[package]
name = "hopfun"
edition = "2024.beta"
authors = ["Bonkman (@bonkman22)"]

[dependencies]
hopdex = { local = "../hopdex" }
config_registry = { local = "../config_registry" }

[addresses]
hopfun = "$HOPFUN_ADDR"

[dev-dependencies]

[dev-addresses]
EOF

# Update coin_template/Move.toml to use the deployed packages
cat > contracts/coin_template/Move.toml << EOF
[package]
name = "coin_template"
edition = "2024.beta"

[dependencies]
hopfun = { local = "../hopfun" }
config_registry = { local = "../config_registry" }

[addresses]
coin_template = "0x0"
EOF

echo -e "${GREEN}Move.toml files updated with deployed addresses${NC}"

# Now rebuild the coin_template bytecode
echo -e "${YELLOW}Building coin_template with correct dependencies...${NC}"
cd contracts/coin_template
sui move build --skip-fetch-latest-git-deps
cd ../..

# Extract the bytecode using the existing script
echo -e "${YELLOW}Extracting bytecode...${NC}"
node scripts/extract-bytecode.js

echo -e "${GREEN}✅ Bytecode rebuilt with correct dependencies!${NC}"

# Show the updated bytecode info
echo -e "${YELLOW}Updated bytecode info:${NC}"
jq '.dependencies' apps/frontend/src/services/bytecode-data.json

echo -e "${GREEN}=== Fix complete! ===${NC}"
echo -e "${YELLOW}The coin_template bytecode has been rebuilt with the correct deployed addresses.${NC}"
echo -e "${YELLOW}You can now try creating a token from the frontend.${NC}"