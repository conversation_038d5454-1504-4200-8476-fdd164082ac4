#!/usr/bin/env node

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Colors for better output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function runCommand(cmd, description) {
  try {
    log(`\n🔄 ${description}...`, 'cyan');
    const output = execSync(cmd, { encoding: 'utf8' });
    log(`✅ ${description} successful`, 'green');
    return output;
  } catch (error) {
    log(`❌ ${description} failed`, 'red');
    console.error(error.message);
    return null;
  }
}

async function testContracts() {
  log('\n========================================', 'magenta');
  log('   HopFun Contracts Test Suite', 'magenta');
  log('========================================\n', 'magenta');

  // 1. Load configuration
  log('1️⃣  Loading configurations...', 'blue');
  
  const deploymentsPath = path.join(__dirname, '..', 'config', 'deployments.json');
  const registryPath = path.join(__dirname, '..', 'config', 'registry-deployment.json');
  const bytecodePath = path.join(__dirname, '..', 'apps', 'frontend', 'src', 'services', 'bytecode-data.json');
  
  if (!fs.existsSync(deploymentsPath)) {
    log('❌ deployments.json not found', 'red');
    process.exit(1);
  }
  
  if (!fs.existsSync(registryPath)) {
    log('❌ registry-deployment.json not found', 'red');
    process.exit(1);
  }
  
  if (!fs.existsSync(bytecodePath)) {
    log('❌ bytecode-data.json not found', 'red');
    process.exit(1);
  }
  
  const deployments = JSON.parse(fs.readFileSync(deploymentsPath, 'utf8'));
  const registry = JSON.parse(fs.readFileSync(registryPath, 'utf8'));
  const bytecodeData = JSON.parse(fs.readFileSync(bytecodePath, 'utf8'));
  
  const devnet = deployments.deployments.devnet;
  const registryDeployment = registry.deployment;
  
  log('✅ Configurations loaded successfully', 'green');
  
  // 2. Verify package deployments
  log('\n2️⃣  Verifying package deployments...', 'blue');
  
  const packages = [
    { name: 'Config Registry', id: devnet.registry.packageId },
    { name: 'HopFun', id: devnet.hopfun.packageId },
    { name: 'HopDex', id: devnet.hopdex.packageId }
  ];
  
  for (const pkg of packages) {
    if (pkg.id) {
      const output = runCommand(
        `sui client object ${pkg.id} --json 2>/dev/null`,
        `Checking ${pkg.name} package`
      );
      if (output) {
        try {
          const data = JSON.parse(output);
          if (data.data && data.data.content) {
            log(`   ✅ ${pkg.name}: ${pkg.id.substring(0, 16)}...`, 'green');
          }
        } catch (e) {
          log(`   ❌ ${pkg.name}: Invalid response`, 'red');
        }
      }
    } else {
      log(`   ⚠️  ${pkg.name}: Not deployed`, 'yellow');
    }
  }
  
  // 3. Verify shared objects
  log('\n3️⃣  Verifying shared objects...', 'blue');
  
  const objects = [
    { name: 'Registry Object', id: devnet.registry.registryId },
    { name: 'MemeConfig', id: devnet.hopfun.memeConfigId },
    { name: 'DexConfig', id: devnet.hopdex.dexConfigId }
  ];
  
  for (const obj of objects) {
    if (obj.id) {
      const output = runCommand(
        `sui client object ${obj.id} --json 2>/dev/null`,
        `Checking ${obj.name}`
      );
      if (output) {
        try {
          const data = JSON.parse(output);
          if (data.data && data.data.content) {
            log(`   ✅ ${obj.name}: ${obj.id.substring(0, 16)}...`, 'green');
            
            // For Registry, check stored values
            if (obj.name === 'Registry Object' && data.data.content.fields) {
              const fields = data.data.content.fields;
              log('      Registry contents:', 'cyan');
              log(`      - meme_config_address: ${fields.meme_config_address || 'Not set'}`, 'cyan');
              log(`      - dex_config_address: ${fields.dex_config_address || 'Not set'}`, 'cyan');
              log(`      - hopfun_package_id: ${fields.hopfun_package_id || 'Not set'}`, 'cyan');
              log(`      - hopdex_package_id: ${fields.hopdex_package_id || 'Not set'}`, 'cyan');
            }
          }
        } catch (e) {
          log(`   ❌ ${obj.name}: Invalid response`, 'red');
        }
      }
    } else {
      log(`   ⚠️  ${obj.name}: Not created`, 'yellow');
    }
  }
  
  // 4. Verify bytecode dependencies
  log('\n4️⃣  Verifying bytecode dependencies...', 'blue');
  
  if (bytecodeData.dependencies && bytecodeData.dependencies.length > 0) {
    log(`   ✅ Dependencies found: ${bytecodeData.dependencies.length}`, 'green');
    
    const expectedDeps = [
      '0x0000000000000000000000000000000000000000000000000000000000000001',
      '0x0000000000000000000000000000000000000000000000000000000000000002',
      devnet.registry.packageId,
      devnet.hopfun.packageId
    ];
    
    let allMatch = true;
    for (let i = 0; i < expectedDeps.length; i++) {
      if (bytecodeData.dependencies[i] === expectedDeps[i]) {
        log(`   ✅ Dependency ${i + 1}: Correct`, 'green');
      } else {
        log(`   ❌ Dependency ${i + 1}: Mismatch`, 'red');
        log(`      Expected: ${expectedDeps[i]}`, 'yellow');
        log(`      Found: ${bytecodeData.dependencies[i]}`, 'yellow');
        allMatch = false;
      }
    }
    
    if (allMatch) {
      log('   ✅ All dependencies correctly configured', 'green');
    } else {
      log('   ❌ Dependency mismatch detected', 'red');
    }
  } else {
    log('   ❌ No dependencies found in bytecode', 'red');
  }
  
  // 5. Test token creation simulation
  log('\n5️⃣  Simulating token creation...', 'blue');
  
  // Create a temporary Move.toml for testing
  const testDir = path.join(__dirname, '..', 'test-token');
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }
  
  const testMoveToml = `[package]
name = "TestToken"
edition = "2024.beta"

[dependencies]
Sui = { git = "https://github.com/MystenLabs/sui.git", subdir = "crates/sui-framework/packages/sui-framework", rev = "framework/testnet" }
hopfun = { local = "../contracts/hopfun" }
config_registry = { local = "../contracts/config_registry" }

[addresses]
test_token = "0x0"
hopfun = "${devnet.hopfun.packageId}"
config_registry = "${devnet.registry.packageId}"
`;
  
  fs.writeFileSync(path.join(testDir, 'Move.toml'), testMoveToml);
  
  // Create a test module
  const sourcesDir = path.join(testDir, 'sources');
  if (!fs.existsSync(sourcesDir)) {
    fs.mkdirSync(sourcesDir, { recursive: true });
  }
  
  const testModule = `module test_token::test {
    use sui::coin::{Self, TreasuryCap};
    use sui::tx_context::TxContext;
    
    public struct TEST has drop {}
    
    fun init(witness: TEST, ctx: &mut TxContext) {
        let (treasury_cap, metadata) = coin::create_currency(
            witness,
            6,
            b"TEST",
            b"Test Token",
            b"Test token for verification",
            option::none(),
            ctx
        );
        
        transfer::public_freeze_object(metadata);
        transfer::public_transfer(treasury_cap, tx_context::sender(ctx));
    }
}`;
  
  fs.writeFileSync(path.join(sourcesDir, 'test.move'), testModule);
  
  // Try to build the test token
  const buildOutput = runCommand(
    `cd ${testDir} && sui move build 2>&1`,
    'Building test token'
  );
  
  if (buildOutput && buildOutput.includes('BUILDING')) {
    log('   ✅ Test token builds successfully', 'green');
  } else {
    log('   ⚠️  Test token build had issues', 'yellow');
  }
  
  // Clean up test directory
  fs.rmSync(testDir, { recursive: true, force: true });
  
  // 6. Summary
  log('\n========================================', 'magenta');
  log('   Test Results Summary', 'magenta');
  log('========================================\n', 'magenta');
  
  const summary = {
    registry: !!devnet.registry.packageId,
    hopfun: !!devnet.hopfun.packageId,
    hopdex: !!devnet.hopdex.packageId,
    registryObject: !!devnet.registry.registryId,
    memeConfig: !!devnet.hopfun.memeConfigId,
    dexConfig: !!devnet.hopdex.dexConfigId,
    bytecode: bytecodeData.dependencies && bytecodeData.dependencies.length === 4,
    dependencies: bytecodeData.dependencies && 
                  bytecodeData.dependencies[2] === devnet.registry.packageId &&
                  bytecodeData.dependencies[3] === devnet.hopfun.packageId
  };
  
  let allPassed = true;
  for (const [key, value] of Object.entries(summary)) {
    if (value) {
      log(`✅ ${key}: PASSED`, 'green');
    } else {
      log(`❌ ${key}: FAILED`, 'red');
      allPassed = false;
    }
  }
  
  if (allPassed) {
    log('\n🎉 All tests passed! The system is ready for token creation.', 'green');
  } else {
    log('\n⚠️  Some tests failed. Please run the deployment script again.', 'yellow');
    log('   Run: npm run deploy:contracts', 'cyan');
  }
  
  // 7. Provide helpful commands
  log('\n📝 Helpful Commands:', 'blue');
  log('   Deploy contracts: ./scripts/deploy.sh', 'cyan');
  log('   Extract bytecode: node scripts/extract-bytecode.js', 'cyan');
  log('   Check deployment: node scripts/check-deployment.js', 'cyan');
  log('   Test contracts: node scripts/test-contracts.js', 'cyan');
}

// Run the tests
testContracts().catch(error => {
  log(`\n❌ Test suite failed: ${error.message}`, 'red');
  process.exit(1);
});