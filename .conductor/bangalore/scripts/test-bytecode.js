#!/usr/bin/env node

/**
 * Test script to verify bytecode loading and generation
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const BYTECODE_DATA_PATH = path.join(__dirname, '../apps/frontend/src/services/bytecode-data.json');

// Test loading bytecode data
function testBytecodeLoading() {
  console.log('🧪 Testing bytecode loading...\n');
  
  try {
    // Read the bytecode data
    const data = JSON.parse(fs.readFileSync(BYTECODE_DATA_PATH, 'utf8'));
    
    console.log('✅ Bytecode data loaded successfully');
    console.log('📦 Bytecode size:', data.size, 'bytes');
    console.log('🔐 Checksum:', data.checksum);
    console.log('📅 Generated at:', data.timestamp);
    console.log('📝 Dependencies:', data.dependencies.length);
    
    // Decode and verify the bytecode
    const bytecode = Buffer.from(data.bytecode, 'base64');
    console.log('✅ Bytecode decoded successfully');
    console.log('📏 Decoded size:', bytecode.length, 'bytes');
    
    // Verify the size matches
    if (bytecode.length === data.size) {
      console.log('✅ Size verification passed');
    } else {
      console.log('❌ Size mismatch! Expected:', data.size, 'Got:', bytecode.length);
    }
    
    // Check if bytecode starts with expected Move magic bytes
    // Move bytecode typically starts with 0xa1 0x1c 0xeb 0x0b (little-endian)
    const magicBytes = bytecode.slice(0, 4);
    const expectedMagic = Buffer.from([0xa1, 0x1c, 0xeb, 0x0b]);
    
    if (magicBytes.equals(expectedMagic)) {
      console.log('✅ Move bytecode magic bytes verified');
    } else {
      console.log('⚠️  Magic bytes:', magicBytes.toString('hex'));
    }
    
    console.log('\n✅ All tests passed! Bytecode is ready for use.');
    
  } catch (error) {
    console.error('❌ Error testing bytecode:', error.message);
    process.exit(1);
  }
}

// Run the test
testBytecodeLoading();