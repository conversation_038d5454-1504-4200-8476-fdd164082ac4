#!/bin/bash

# Manual fix for registry configuration
# Run this if you already have deployed registry but with wrong IDs

echo "========================================="
echo "Manual Registry Configuration Fix"
echo "========================================="
echo ""

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Check if registry package exists
echo -e "${YELLOW}Step 1: Finding Registry Objects${NC}"
echo "Looking for registry package ID: 0xec9badaaa3f9c439d37445979888178ed819f165ab14a2ddbd3a19c5d36667c7"
echo ""

# Get the transaction that created the registry
echo "Fetching deployment transaction details..."
REGISTRY_PACKAGE="0xec9badaaa3f9c439d37445979888178ed819f165ab14a2ddbd3a19c5d36667c7"

# Try to find objects created by the package
echo -e "${YELLOW}Searching for ConfigRegistry and AdminCap objects...${NC}"
echo "Please run these commands manually to find the correct IDs:"
echo ""
echo -e "${BLUE}1. Find the ConfigRegistry (shared object):${NC}"
echo "   sui client objects --owner 0x0000000000000000000000000000000000000000000000000000000000000000"
echo "   Look for an object with type containing 'ConfigRegistry'"
echo ""
echo -e "${BLUE}2. Find the AdminCap (owned by your address):${NC}"
echo "   sui client objects"
echo "   Look for an object with type containing '${REGISTRY_PACKAGE}::registry::AdminCap'"
echo ""
echo -e "${YELLOW}Step 2: Once you have the correct IDs, update them here:${NC}"
echo ""

# Prompt for correct IDs
read -p "Enter the ConfigRegistry object ID (shared object): " REGISTRY_ID
read -p "Enter the AdminCap object ID (owned by you): " ADMIN_CAP_ID

# Validate inputs
if [ -z "$REGISTRY_ID" ] || [ -z "$ADMIN_CAP_ID" ]; then
    echo -e "${RED}Error: Both IDs are required${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}Using the following IDs:${NC}"
echo "  Registry Package: $REGISTRY_PACKAGE"
echo "  Registry Object: $REGISTRY_ID"
echo "  Admin Cap: $ADMIN_CAP_ID"
echo ""

# Update registry-deployment.json
cat > config/registry-deployment.json <<EOF
{
  "registry": {
    "packageId": "$REGISTRY_PACKAGE",
    "registryId": "$REGISTRY_ID",
    "adminCapId": "$ADMIN_CAP_ID",
    "deployedAt": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
  }
}
EOF

echo -e "${GREEN}Updated config/registry-deployment.json${NC}"

# Load existing deployments
MEME_CONFIG_ID="0x075ce738e0249bfeb17f74993c14ef9154c966956203a1afb3de3b9480ab7592"
DEX_CONFIG_ID="0xaf31050af159b71880fcd247ae5b986afe0ea43e55375be220b3a3e2d9a57602"
HOPFUN_PACKAGE_ID="0xee6b9a800f1e7288135a8a80a18bee4a933b271d81d23419fc3ba04cdc257589"
HOPDEX_PACKAGE_ID="0xbae3eb4c0adb2db1f9fdef0011e36df0b9d546a1c3c561af9d5f8f4d4b672e7b"

echo ""
echo -e "${YELLOW}Step 3: Update Registry with Contract Addresses${NC}"
echo "Updating registry with deployed contract addresses..."

# Try to update the registry
echo "Running: sui client call --package $REGISTRY_PACKAGE --module registry --function update_all_addresses"
UPDATE_OUTPUT=$(sui client call \
    --package "$REGISTRY_PACKAGE" \
    --module registry \
    --function update_all_addresses \
    --args "$REGISTRY_ID" "$ADMIN_CAP_ID" "$MEME_CONFIG_ID" "$DEX_CONFIG_ID" "$HOPFUN_PACKAGE_ID" "$HOPDEX_PACKAGE_ID" \
    --gas-budget 10000000 2>&1)

if echo "$UPDATE_OUTPUT" | grep -q "Status: Success"; then
    echo -e "${GREEN}✅ Registry updated successfully!${NC}"
else
    echo -e "${RED}Failed to update registry. Output:${NC}"
    echo "$UPDATE_OUTPUT"
    echo ""
    echo -e "${YELLOW}You can try updating individual addresses:${NC}"
    echo "sui client call \\"
    echo "  --package $REGISTRY_PACKAGE \\"
    echo "  --module registry \\"
    echo "  --function set_meme_config_address \\"
    echo "  --args $REGISTRY_ID $ADMIN_CAP_ID $MEME_CONFIG_ID"
fi

echo ""
echo -e "${BLUE}Step 4: Verify Registry State${NC}"
echo "To verify the registry was updated correctly:"
echo "sui client object $REGISTRY_ID"
echo ""
echo -e "${GREEN}Done! Your registry should now be properly configured.${NC}"