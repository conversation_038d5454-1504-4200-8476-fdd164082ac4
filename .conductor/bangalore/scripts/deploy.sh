#!/bin/bash

# Complete deployment script using Config Registry pattern
# Fixed version with improved ID extraction

set -e  # Exit on error

echo "========================================="
echo "HopFun Complete Deployment (Fixed)"
echo "========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Improved extraction functions for different object types
extract_package_id() {
    local output=$1
    # Look for "Published Objects" section and extract PackageID
    echo "$output" | grep -A2 "Published Objects" | grep "PackageID:" | sed 's/.*PackageID: //' | sed 's/ .*//' | tr -d ' │'
}

extract_created_object() {
    local object_type=$1
    local output=$2
    # Look for created objects with specific type
    echo "$output" | grep -B3 "$object_type" | grep "ObjectID:" | head -1 | sed 's/.*ObjectID: //' | sed 's/ .*//' | tr -d ' │'
}

extract_shared_object() {
    local object_type=$1
    local output=$2
    # Look for shared objects
    echo "$output" | grep -B2 "$object_type" | grep "ObjectID:" | head -1 | sed 's/.*ObjectID: //' | sed 's/ .*//' | tr -d ' │'
}

# Step 1: Deploy Config Registry
echo -e "${YELLOW}Step 1: Deploying Config Registry${NC}"
echo "This creates a shared registry for storing configuration addresses..."

REGISTRY_OUTPUT=$(sui client publish contracts/config_registry --gas-budget 100000000 2>&1)
echo "$REGISTRY_OUTPUT"

# Extract Registry IDs with improved precision
REGISTRY_PACKAGE_ID=$(extract_package_id "$REGISTRY_OUTPUT")
REGISTRY_ID=$(extract_shared_object "ConfigRegistry" "$REGISTRY_OUTPUT")
ADMIN_CAP_ID=$(extract_created_object "AdminCap" "$REGISTRY_OUTPUT")

# Validate extracted IDs
if [ -z "$REGISTRY_PACKAGE_ID" ] || [ -z "$REGISTRY_ID" ] || [ -z "$ADMIN_CAP_ID" ]; then
    echo -e "${RED}Failed to extract Registry IDs. Please check the output above.${NC}"
    echo "Registry Package ID: $REGISTRY_PACKAGE_ID"
    echo "Registry ID: $REGISTRY_ID"
    echo "Admin Cap ID: $ADMIN_CAP_ID"
    exit 1
fi

echo ""
echo -e "${GREEN}Config Registry Deployed:${NC}"
echo "  Package ID: $REGISTRY_PACKAGE_ID"
echo "  Registry ID: $REGISTRY_ID"
echo "  Admin Cap ID: $ADMIN_CAP_ID"
echo ""

# Save registry info
mkdir -p config
cat > config/registry-deployment.json <<EOF
{
  "registry": {
    "packageId": "$REGISTRY_PACKAGE_ID",
    "registryId": "$REGISTRY_ID",
    "adminCapId": "$ADMIN_CAP_ID",
    "deployedAt": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
  }
}
EOF

# Step 2: Deploy HopFun
echo -e "${YELLOW}Step 2: Deploying HopFun${NC}"
echo "Setting hopfun address to 0x0 for fresh deployment..."

# Backup and update Move.toml
cp contracts/hopfun/Move.toml contracts/hopfun/Move.toml.backup
sed -i '' 's/hopfun = "0x[a-f0-9]*"/hopfun = "0x0"/' contracts/hopfun/Move.toml

HOPFUN_OUTPUT=$(sui client publish contracts/hopfun --gas-budget 200000000 --skip-dependency-verification 2>&1)
echo "$HOPFUN_OUTPUT"

# Extract HopFun IDs
HOPFUN_PACKAGE_ID=$(extract_package_id "$HOPFUN_OUTPUT")
MEME_CONFIG_ID=$(extract_shared_object "MemeConfig" "$HOPFUN_OUTPUT")
HOPFUN_ADMIN_CAP=$(extract_created_object "config::AdminCap" "$HOPFUN_OUTPUT")
if [ -z "$HOPFUN_ADMIN_CAP" ]; then
    HOPFUN_ADMIN_CAP=$(extract_created_object "AdminCap" "$HOPFUN_OUTPUT")
fi
HOPFUN_UPGRADE_CAP=$(extract_created_object "UpgradeCap" "$HOPFUN_OUTPUT")

# Validate extracted IDs
if [ -z "$HOPFUN_PACKAGE_ID" ] || [ -z "$MEME_CONFIG_ID" ]; then
    echo -e "${RED}Failed to extract HopFun IDs. Please check the output above.${NC}"
    echo "HopFun Package ID: $HOPFUN_PACKAGE_ID"
    echo "MemeConfig ID: $MEME_CONFIG_ID"
    exit 1
fi

echo ""
echo -e "${GREEN}HopFun Deployed:${NC}"
echo "  Package ID: $HOPFUN_PACKAGE_ID"
echo "  MemeConfig ID: $MEME_CONFIG_ID"
echo "  Admin Cap: $HOPFUN_ADMIN_CAP"
echo "  Upgrade Cap: $HOPFUN_UPGRADE_CAP"
echo ""

# Update hopfun Move.toml with the deployed package ID
sed -i '' "s/hopfun = \"0x0\"/hopfun = \"$HOPFUN_PACKAGE_ID\"/" contracts/hopfun/Move.toml

# Step 3: Deploy HopDex (if needed)
echo -e "${YELLOW}Step 3: Checking HopDex deployment${NC}"
if [ ! -f "config/hopdex-deployment.json" ]; then
    echo "Deploying HopDex..."
    
    cp contracts/hopdex/Move.toml contracts/hopdex/Move.toml.backup
    sed -i '' 's/hopdex = "0x[a-f0-9]*"/hopdex = "0x0"/' contracts/hopdex/Move.toml
    
    HOPDEX_OUTPUT=$(sui client publish contracts/hopdex --gas-budget 200000000 --skip-dependency-verification 2>&1)
    echo "$HOPDEX_OUTPUT"
    
    HOPDEX_PACKAGE_ID=$(extract_package_id "$HOPDEX_OUTPUT")
    DEX_CONFIG_ID=$(extract_shared_object "DexConfig" "$HOPDEX_OUTPUT")
    
    # Validate
    if [ -z "$HOPDEX_PACKAGE_ID" ] || [ -z "$DEX_CONFIG_ID" ]; then
        echo -e "${RED}Failed to extract HopDex IDs.${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}HopDex Deployed:${NC}"
    echo "  Package ID: $HOPDEX_PACKAGE_ID"
    echo "  DexConfig ID: $DEX_CONFIG_ID"
    
    sed -i '' "s/hopdex = \"0x0\"/hopdex = \"$HOPDEX_PACKAGE_ID\"/" contracts/hopdex/Move.toml
    
    # Save hopdex deployment info
    cat > config/hopdex-deployment.json <<EOF
{
  "hopdex": {
    "packageId": "$HOPDEX_PACKAGE_ID",
    "dexConfigId": "$DEX_CONFIG_ID",
    "deployedAt": "$(date -u +"%Y-%m-%dT%H:%M:%SZ")"
  }
}
EOF
else
    echo "HopDex already deployed, loading from config..."
    HOPDEX_PACKAGE_ID=$(jq -r '.hopdex.packageId' config/hopdex-deployment.json)
    DEX_CONFIG_ID=$(jq -r '.hopdex.dexConfigId' config/hopdex-deployment.json)
    
    if [ "$HOPDEX_PACKAGE_ID" = "null" ] || [ "$DEX_CONFIG_ID" = "null" ]; then
        echo -e "${RED}Invalid hopdex-deployment.json. Please delete it and re-run.${NC}"
        exit 1
    fi
fi

# Step 4: Update Registry with all addresses
echo ""
echo -e "${YELLOW}Step 4: Updating Config Registry with deployed addresses${NC}"
echo "Registry ID: $REGISTRY_ID"
echo "Admin Cap ID: $ADMIN_CAP_ID"
echo "MemeConfig ID: $MEME_CONFIG_ID"
echo "DexConfig ID: $DEX_CONFIG_ID"
echo "HopFun Package: $HOPFUN_PACKAGE_ID"
echo "HopDex Package: $HOPDEX_PACKAGE_ID"

# Update all addresses in one transaction
echo "Calling update_all_addresses on registry..."
UPDATE_OUTPUT=$(sui client call \
    --package "$REGISTRY_PACKAGE_ID" \
    --module registry \
    --function update_all_addresses \
    --args "$REGISTRY_ID" "$ADMIN_CAP_ID" "$MEME_CONFIG_ID" "$DEX_CONFIG_ID" "$HOPFUN_PACKAGE_ID" "$HOPDEX_PACKAGE_ID" \
    --gas-budget 10000000 2>&1)

if echo "$UPDATE_OUTPUT" | grep -q "Status: Success"; then
    echo -e "${GREEN}Registry updated successfully!${NC}"
else
    echo -e "${RED}Failed to update registry. Output:${NC}"
    echo "$UPDATE_OUTPUT"
    echo ""
    echo -e "${YELLOW}Debugging info:${NC}"
    echo "Registry Package: $REGISTRY_PACKAGE_ID"
    echo "Registry Object: $REGISTRY_ID"
    echo "Admin Cap: $ADMIN_CAP_ID"
    echo ""
    echo -e "${YELLOW}You can manually update the registry with:${NC}"
    echo "sui client call \\"
    echo "  --package $REGISTRY_PACKAGE_ID \\"
    echo "  --module registry \\"
    echo "  --function set_meme_config_address \\"
    echo "  --args $REGISTRY_ID $ADMIN_CAP_ID $MEME_CONFIG_ID"
    exit 1
fi

# Step 5: Update frontend configuration
echo ""
echo -e "${YELLOW}Step 5: Updating frontend configuration${NC}"

# Update network-config.service.ts
CONFIG_FILE="apps/frontend/src/services/network-config.service.ts"
if [ -f "$CONFIG_FILE" ]; then
    cp $CONFIG_FILE ${CONFIG_FILE}.backup
    
    # Create a temporary file with the updated configuration
    cat > /tmp/network-config-update.js <<EOF
const fs = require('fs');
const content = fs.readFileSync('$CONFIG_FILE', 'utf8');
const updated = content
    .replace(/registryId:[\s\n\r]*process\.env\.NEXT_PUBLIC_REGISTRY_ID_DEVNET \|\| '0x[a-f0-9]*'/, "registryId: process.env.NEXT_PUBLIC_REGISTRY_ID_DEVNET || '$REGISTRY_ID'")
    .replace(/registryPackageId:[\s\n\r]*process\.env\.NEXT_PUBLIC_REGISTRY_PACKAGE_ID_DEVNET \|\| '0x[a-f0-9]*'/, "registryPackageId: process.env.NEXT_PUBLIC_REGISTRY_PACKAGE_ID_DEVNET || '$REGISTRY_PACKAGE_ID'")
    .replace(/hopfunPackageId:[\s\n\r]*process\.env\.NEXT_PUBLIC_HOPFUN_PACKAGE_ID_DEVNET \|\|[\s\n\r]*'0x[a-f0-9]*'/, "hopfunPackageId: process.env.NEXT_PUBLIC_HOPFUN_PACKAGE_ID_DEVNET || '$HOPFUN_PACKAGE_ID'")
    .replace(/memeConfigId:[\s\n\r]*process\.env\.NEXT_PUBLIC_MEME_CONFIG_ID_DEVNET \|\|[\s\n\r]*'0x[a-f0-9]*'/, "memeConfigId: process.env.NEXT_PUBLIC_MEME_CONFIG_ID_DEVNET || '$MEME_CONFIG_ID'")
    .replace(/hopdexPackageId:[\s\n\r]*process\.env\.NEXT_PUBLIC_HOPDEX_PACKAGE_ID_DEVNET \|\|[\s\n\r]*'0x[a-f0-9]*'/, "hopdexPackageId: process.env.NEXT_PUBLIC_HOPDEX_PACKAGE_ID_DEVNET || '$HOPDEX_PACKAGE_ID'")
    .replace(/hopdexConfigId:[\s\n\r]*process\.env\.NEXT_PUBLIC_HOPDEX_CONFIG_ID_DEVNET \|\|[\s\n\r]*'0x[a-f0-9]*'/, "hopdexConfigId: process.env.NEXT_PUBLIC_HOPDEX_CONFIG_ID_DEVNET || '$DEX_CONFIG_ID'");
fs.writeFileSync('$CONFIG_FILE', updated);
EOF
    
    node /tmp/network-config-update.js
    echo "Frontend configuration updated"
fi

# Update deployments.json
TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
cat > config/deployments.json <<EOF
{
  "deployments": {
    "devnet": {
      "registry": {
        "packageId": "$REGISTRY_PACKAGE_ID",
        "registryId": "$REGISTRY_ID",
        "adminCapId": "$ADMIN_CAP_ID",
        "deployedAt": "$TIMESTAMP"
      },
      "hopfun": {
        "packageId": "$HOPFUN_PACKAGE_ID",
        "memeConfigId": "$MEME_CONFIG_ID",
        "adminCapId": "$HOPFUN_ADMIN_CAP",
        "upgradeCapId": "$HOPFUN_UPGRADE_CAP",
        "deployedAt": "$TIMESTAMP"
      },
      "hopdex": {
        "packageId": "$HOPDEX_PACKAGE_ID",
        "dexConfigId": "$DEX_CONFIG_ID",
        "deployedAt": "$TIMESTAMP"
      }
    }
  }
}
EOF

# Step 6: Build and extract bytecode
echo ""
echo -e "${YELLOW}Step 6: Building coin template with registry support${NC}"

rm -rf contracts/coin_template/build
sui move build --path contracts/coin_template

echo "Extracting bytecode..."
node scripts/extract-bytecode.js

echo ""
echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}🎉 Deployment Complete! 🎉${NC}"
echo -e "${BLUE}=========================================${NC}"
echo ""
echo -e "${GREEN}Deployed Contracts:${NC}"
echo "  Registry ID: $REGISTRY_ID"
echo "  Admin Cap: $ADMIN_CAP_ID"
echo "  HopFun Package: $HOPFUN_PACKAGE_ID"
echo "  MemeConfig: $MEME_CONFIG_ID"
echo "  HopDex Package: $HOPDEX_PACKAGE_ID"
echo "  DexConfig: $DEX_CONFIG_ID"
echo ""
echo -e "${YELLOW}Next Steps:${NC}"
echo "  1. Test token creation in the frontend"
echo "  2. All addresses are stored in the registry"
echo "  3. No redeployments needed for address changes"
echo ""
echo -e "${GREEN}Files Updated:${NC}"
echo "  - config/deployments.json"
echo "  - config/registry-deployment.json"
echo "  - apps/frontend/src/services/bytecode-data.json"
echo "  - apps/frontend/src/services/network-config.service.ts"
echo ""
echo -e "${BLUE}To verify registry state:${NC}"
echo "  sui client object $REGISTRY_ID"