# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "08AE5250891188A6FB9747237C74060B812D0F4137F526E0013B3F6A013BA363"
deps_digest = "F8BBB0CCB2491CA29A3DF03D6F92277A4F3574266507ACD77214D37ECA3F3082"
dependencies = [
  { id = "Sui", name = "Sui" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "framework/devnet", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "framework/devnet", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[move.toolchain-version]
compiler-version = "1.53.2"
edition = "2024.beta"
flavor = "sui"

[env]

[env.devnet]
chain-id = "4256f1d2"
original-published-id = "0x01279390afa24a28587219283d00785eed4903c41e80cb3b6363993c15d38abc"
latest-published-id = "0x01279390afa24a28587219283d00785eed4903c41e80cb3b6363993c15d38abc"
published-version = "1"
