module config_registry::registry {
    use sui::event;

    // ======== Constants ========
    const ENotAdmin: u64 = 0;
    const EInvalidAddress: u64 = 1;

    // ======== Events ========
    public struct RegistryCreated has copy, drop {
        registry_id: address,
        admin: address,
    }

    public struct AddressUpdated has copy, drop {
        registry_id: address,
        field: vector<u8>,
        old_value: address,
        new_value: address,
        updated_by: address,
    }

    // ======== Objects ========
    /// Shared configuration registry that stores addresses
    public struct ConfigRegistry has key {
        id: sui::object::UID,
        /// HopFun MemeConfig address
        meme_config_address: address,
        /// HopDex DexConfig address  
        dex_config_address: address,
        /// HopFun package ID
        hopfun_package_id: address,
        /// HopDex package ID
        hopdex_package_id: address,
        /// Admin who can update the registry
        admin: address,
        /// Version for future upgrades
        version: u64,
    }

    /// Admin capability for updating the registry
    public struct AdminCap has key, store {
        id: sui::object::UID,
        registry_id: address,
    }

    // ======== Initialize ========
    /// Initialize the registry on package publish
    fun init(ctx: &mut sui::tx_context::TxContext) {
        let admin = sui::tx_context::sender(ctx);
        let registry_uid = sui::object::new(ctx);
        let registry_id = sui::object::uid_to_address(&registry_uid);
        
        // Create shared registry with placeholder addresses
        let registry = ConfigRegistry {
            id: registry_uid,
            meme_config_address: @0x0,
            dex_config_address: @0x0,
            hopfun_package_id: @0x0,
            hopdex_package_id: @0x0,
            admin,
            version: 1,
        };
        
        // Create admin capability
        let admin_cap = AdminCap {
            id: sui::object::new(ctx),
            registry_id,
        };

        // Emit creation event
        event::emit(RegistryCreated {
            registry_id,
            admin,
        });

        // Share the registry and transfer admin cap
        sui::transfer::share_object(registry);
        sui::transfer::transfer(admin_cap, admin);
    }

    // ======== Admin Functions ========
    /// Update the MemeConfig address (admin only)
    public entry fun set_meme_config_address(
        registry: &mut ConfigRegistry,
        cap: &AdminCap,
        new_address: address,
        ctx: &mut sui::tx_context::TxContext
    ) {
        assert!(is_admin(registry, cap), ENotAdmin);
        assert!(new_address != @0x0, EInvalidAddress);
        
        let old_address = registry.meme_config_address;
        registry.meme_config_address = new_address;
        
        event::emit(AddressUpdated {
            registry_id: object_id_address(registry),
            field: b"meme_config_address",
            old_value: old_address,
            new_value: new_address,
            updated_by: sui::tx_context::sender(ctx),
        });
    }

    /// Update the DexConfig address (admin only)
    public entry fun set_dex_config_address(
        registry: &mut ConfigRegistry,
        cap: &AdminCap,
        new_address: address,
        ctx: &mut sui::tx_context::TxContext
    ) {
        assert!(is_admin(registry, cap), ENotAdmin);
        assert!(new_address != @0x0, EInvalidAddress);
        
        let old_address = registry.dex_config_address;
        registry.dex_config_address = new_address;
        
        event::emit(AddressUpdated {
            registry_id: object_id_address(registry),
            field: b"dex_config_address",
            old_value: old_address,
            new_value: new_address,
            updated_by: sui::tx_context::sender(ctx),
        });
    }

    /// Update the HopFun package ID (admin only)
    public entry fun set_hopfun_package_id(
        registry: &mut ConfigRegistry,
        cap: &AdminCap,
        new_address: address,
        ctx: &mut sui::tx_context::TxContext
    ) {
        assert!(is_admin(registry, cap), ENotAdmin);
        assert!(new_address != @0x0, EInvalidAddress);
        
        let old_address = registry.hopfun_package_id;
        registry.hopfun_package_id = new_address;
        
        event::emit(AddressUpdated {
            registry_id: object_id_address(registry),
            field: b"hopfun_package_id",
            old_value: old_address,
            new_value: new_address,
            updated_by: sui::tx_context::sender(ctx),
        });
    }

    /// Update the HopDex package ID (admin only)
    public entry fun set_hopdex_package_id(
        registry: &mut ConfigRegistry,
        cap: &AdminCap,
        new_address: address,
        ctx: &mut sui::tx_context::TxContext
    ) {
        assert!(is_admin(registry, cap), ENotAdmin);
        assert!(new_address != @0x0, EInvalidAddress);
        
        let old_address = registry.hopdex_package_id;
        registry.hopdex_package_id = new_address;
        
        event::emit(AddressUpdated {
            registry_id: object_id_address(registry),
            field: b"hopdex_package_id",
            old_value: old_address,
            new_value: new_address,
            updated_by: sui::tx_context::sender(ctx),
        });
    }

    /// Batch update all addresses (convenience function)
    public entry fun update_all_addresses(
        registry: &mut ConfigRegistry,
        cap: &AdminCap,
        meme_config: address,
        dex_config: address,
        hopfun_package: address,
        hopdex_package: address,
        ctx: &mut sui::tx_context::TxContext
    ) {
        set_meme_config_address(registry, cap, meme_config, ctx);
        set_dex_config_address(registry, cap, dex_config, ctx);
        set_hopfun_package_id(registry, cap, hopfun_package, ctx);
        set_hopdex_package_id(registry, cap, hopdex_package, ctx);
    }

    /// Transfer admin capability to new admin
    #[allow(lint(custom_state_change))]
    public entry fun transfer_admin(
        registry: &mut ConfigRegistry,
        cap: AdminCap,
        new_admin: address,
        ctx: &mut sui::tx_context::TxContext
    ) {
        assert!(is_admin(registry, &cap), ENotAdmin);
        registry.admin = new_admin;
        
        event::emit(AddressUpdated {
            registry_id: object_id_address(registry),
            field: b"admin",
            old_value: sui::tx_context::sender(ctx),
            new_value: new_admin,
            updated_by: sui::tx_context::sender(ctx),
        });

        sui::transfer::transfer(cap, new_admin);
    }

    // ======== Read Functions ========
    /// Get the MemeConfig address
    public fun get_meme_config_address(registry: &ConfigRegistry): address {
        assert!(registry.meme_config_address != @0x0, EInvalidAddress);
        registry.meme_config_address
    }

    /// Get the DexConfig address
    public fun get_dex_config_address(registry: &ConfigRegistry): address {
        assert!(registry.dex_config_address != @0x0, EInvalidAddress);
        registry.dex_config_address
    }

    /// Get the HopFun package ID
    public fun get_hopfun_package_id(registry: &ConfigRegistry): address {
        assert!(registry.hopfun_package_id != @0x0, EInvalidAddress);
        registry.hopfun_package_id
    }

    /// Get the HopDex package ID
    public fun get_hopdex_package_id(registry: &ConfigRegistry): address {
        assert!(registry.hopdex_package_id != @0x0, EInvalidAddress);
        registry.hopdex_package_id
    }

    /// Get all configuration at once
    public fun get_all_config(registry: &ConfigRegistry): (address, address, address, address) {
        (
            registry.meme_config_address,
            registry.dex_config_address,
            registry.hopfun_package_id,
            registry.hopdex_package_id
        )
    }

    /// Check if MemeConfig is initialized
    public fun is_meme_config_initialized(registry: &ConfigRegistry): bool {
        registry.meme_config_address != @0x0
    }

    /// Get registry version
    public fun get_version(registry: &ConfigRegistry): u64 {
        registry.version
    }

    // ======== Internal Functions ========
    fun is_admin(registry: &ConfigRegistry, cap: &AdminCap): bool {
        object_id_address(registry) == cap.registry_id
    }

    fun object_id_address<T: key>(obj: &T): address {
        sui::object::id_address(obj)
    }
}