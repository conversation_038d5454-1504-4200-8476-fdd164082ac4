---
compiled_package_info:
  package_name: coin_template
  address_alias_instantiation:
    bridge: 000000000000000000000000000000000000000000000000000000000000000b
    coin_template: "0000000000000000000000000000000000000000000000000000000000000000"
    config_registry: 01279390afa24a28587219283d00785eed4903c41e80cb3b6363993c15d38abc
    hopdex: 55b04c619307e842a4644314bf30bd1fc8920ea1df36c305728eaf36cd658327
    hopfun: 7bb19a2553935a7757f33b73500aea2bce9ff06a14c308b120b171822ed41d72
    std: "0000000000000000000000000000000000000000000000000000000000000001"
    sui: "0000000000000000000000000000000000000000000000000000000000000002"
    sui_system: "0000000000000000000000000000000000000000000000000000000000000003"
  source_digest: 79761F4AF51E6564A087854915E748D924CED8E433DCC0F987E0CB7DE4D69F59
  build_flags:
    dev_mode: false
    test_mode: false
    generate_docs: false
    save_disassembly: false
    install_dir: ~
    force_recompilation: false
    lock_file: "./Move.lock"
    fetch_deps_only: false
    skip_fetch_latest_git_deps: false
    default_flavor: sui
    default_edition: ~
    deps_as_root: false
    silence_warnings: false
    warnings_are_errors: false
    json_errors: false
    additional_named_addresses: {}
    lint_flag:
      no_lint: false
      lint: false
    modes: []
    implicit_dependencies:
      Bridge:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: b448b1d971bd6c1aac8ef4eee4305943806d5d5b
              subdir: crates/sui-framework/packages/bridge
          subst: ~
          digest: ~
          dep_override: true
      MoveStdlib:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: b448b1d971bd6c1aac8ef4eee4305943806d5d5b
              subdir: crates/sui-framework/packages/move-stdlib
          subst: ~
          digest: ~
          dep_override: true
      Sui:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: b448b1d971bd6c1aac8ef4eee4305943806d5d5b
              subdir: crates/sui-framework/packages/sui-framework
          subst: ~
          digest: ~
          dep_override: true
      SuiSystem:
        Internal:
          kind:
            Git:
              git_url: "https://github.com/MystenLabs/sui.git"
              git_rev: b448b1d971bd6c1aac8ef4eee4305943806d5d5b
              subdir: crates/sui-framework/packages/sui-system
          subst: ~
          digest: ~
          dep_override: true
    force_lock_file: false
dependencies:
  - Bridge
  - MoveStdlib
  - Sui
  - SuiSystem
  - config_registry
  - hopdex
  - hopfun
