module coin_template::template {
    use std::string;
    use sui::coin::{Self, TreasuryCap, CoinMetadata};
    use sui::balance::Balance;
    use hopfun::connector;
    use config_registry::registry::ConfigRegistry;

    /// The OTW for the Coin
    public struct TEMPLATE has drop {}

    const ICON_URL: vector<u8> = b"ImageUrl";
    const TOKEN_DECIMALS: u8 = 6;

    const TWITTER: vector<u8> = b"Twitter";
    const WEBSITE: vector<u8> = b"Website";
    const TELEGRAM: vector<u8> = b"Telegram";

    const NAME: vector<u8> = b"Name";
    const SYMBOL: vector<u8> = b"Symbol";
    const DESCRIPTION: vector<u8> = b"Description";

    const TEMP_ID: u64 = 123u64;

    const TOTAL_SUPPLY: u64 = 1_000_000_000 * 1000000;

    /// Holder for currency components until connector is created
    public struct CurrencyHolder<phantom T> has key {
        id: sui::object::UID,
        treasury_cap: TreasuryCap<T>,
        supply: Balance<T>,
        temp_id: u64,
        creator: address,
    }

    /// Init the Coin - Creates currency and stores components for later connector creation
    fun init(witness: TEMPLATE, ctx: &mut sui::tx_context::TxContext) {
        // Use the OTW to create the currency immediately
        let (mut treasury_cap, metadata) = coin::create_currency<TEMPLATE>(
            witness,
            TOKEN_DECIMALS,
            SYMBOL,
            NAME,
            DESCRIPTION,
            option::some(sui::url::new_unsafe_from_bytes(ICON_URL)),
            ctx
        );

        // Freeze the metadata
        sui::transfer::public_freeze_object<CoinMetadata<TEMPLATE>>(metadata);

        // Mint the total supply
        let supply = treasury_cap.mint(TOTAL_SUPPLY, ctx);
        let creator = sui::tx_context::sender(ctx);

        // Store the treasury cap and supply for later use
        let holder = CurrencyHolder<TEMPLATE> {
            id: sui::object::new(ctx),
            treasury_cap,
            supply: supply.into_balance(),
            temp_id: TEMP_ID,
            creator,
        };

        sui::transfer::transfer(holder, creator);
    }

    /// Create the connector using the registry (second transaction)
    public entry fun create_connector(
        holder: CurrencyHolder<TEMPLATE>,
        registry: &ConfigRegistry,
        ctx: &mut sui::tx_context::TxContext
    ) {
        let CurrencyHolder {
            id,
            treasury_cap,
            supply,
            temp_id,
            creator
        } = holder;
        sui::object::delete(id);

        // Freeze the treasury cap
        sui::transfer::public_freeze_object<TreasuryCap<TEMPLATE>>(treasury_cap);

        // Create the connector from existing supply
        connector::new_from_supply<TEMPLATE>(
            temp_id,
            supply,
            string::utf8(TWITTER),
            string::utf8(WEBSITE),
            string::utf8(TELEGRAM),
            creator,
            registry,
            ctx
        );
    }
}