module hopfun::config {

    use hopfun::events;

    const CONTRACT_VERSION: u64 = 0;

    const EBadVersion: u64 = 0;
    const ECreateDisabled: u64 = 1;
    const ESwapsDisabled: u64 = 2;
    const EBadFee: u64 = 3;

    // defaults
    const DEFAULT_CURVE_SUPPLY_BPS: u64 = 800;
    const DEFAULT_VIRTUAL_SUI: u64 = 1_500 * 1_000_000_000; // $4 * 1_500 = $6,000
    const DEFAULT_LISTING_FEE: u64 = 0; // 0 SUI
    const DEFAULT_SWAP_FEE_BPS: u64 = 200;
    const DEFAULT_MIGRATION_FEE_BPS: u64 = 500;

    const DEFAULT_TREASURY_ADDRESS: address = @0xa5f11d742bb442732149129297564b121a9e7da0be214817ef75814af30c47fd;

    public struct AdminCap has key, store {
        id: object::UID,
    }

    public struct MemeConfig has key {
        id: object::UID,

        minimum_version: u64,
        is_create_enabled: bool,
        are_swaps_enabled: bool,
    
        virtual_sui_amount: u64,
        curve_supply_bps: u64, // bps

        listing_fee: u64,
        swap_fee_bps: u64,
        migration_fee_bps: u64,

        treasury: address
    }

    public struct CONFIG has drop {}

    /*
     * Init
     */

    fun init(_witness: CONFIG, ctx: &mut TxContext) {
        let sender = tx_context::sender(ctx);
        let admin_cap = AdminCap { id: object::new(ctx) };
        transfer::public_transfer(admin_cap, sender);

        let meme_config = MemeConfig {
            id: object::new(ctx),
            minimum_version: CONTRACT_VERSION,
            
            is_create_enabled: true,
            are_swaps_enabled: true,
            
            virtual_sui_amount: DEFAULT_VIRTUAL_SUI,
            curve_supply_bps: DEFAULT_CURVE_SUPPLY_BPS,

            listing_fee: DEFAULT_LISTING_FEE,
            swap_fee_bps: DEFAULT_SWAP_FEE_BPS,
            migration_fee_bps: DEFAULT_MIGRATION_FEE_BPS,

            treasury: DEFAULT_TREASURY_ADDRESS,
        };

        transfer::share_object(meme_config);
    }

    /*
     * Enforcers
     */

    public fun enforce_version(config: &MemeConfig) {
        assert!(CONTRACT_VERSION >= config.minimum_version, EBadVersion);
    }

    public fun enforce_create_enabled(config: &MemeConfig) {
        assert!(config.is_create_enabled, ECreateDisabled);
    }

    public fun enforce_swaps_enabled(config: &MemeConfig) {
        assert!(config.are_swaps_enabled, ESwapsDisabled);
    }

    /*
     * Accessors
     */
    
    public(package) fun id(config: &mut MemeConfig): &mut object::UID {
        &mut config.id
    }

    public fun minimum_version(config: &MemeConfig): u64 {
        config.minimum_version
    }

    public fun is_create_enabled(config: &MemeConfig): bool {
        config.is_create_enabled
    }

    public fun are_swaps_enabled(config: &MemeConfig): bool {
        config.are_swaps_enabled
    }

    public fun virtual_sui_amount(config: &MemeConfig): u64 {
        config.virtual_sui_amount
    }

    public fun curve_supply_bps(config: &MemeConfig): u64 {
        config.curve_supply_bps
    }

    public fun listing_fee(config: &MemeConfig): u64 {
        config.listing_fee
    }

    public fun swap_fee_bps(config: &MemeConfig): u64 {
        config.swap_fee_bps
    }

    public fun migration_fee_bps(config: &MemeConfig): u64 {
        config.migration_fee_bps
    }

    public fun treasury_address(config: &MemeConfig): address {
        config.treasury
    }

      /*
     * Admin methods
     */

    public fun set_create_enabled(
        _cap: &AdminCap,
        config: &mut MemeConfig,
        enabled: bool
    ) {
        config.enforce_version();
        config.is_create_enabled = enabled;
        config.emit_update();
    }

    public fun update_minimum_version(
        _cap: &AdminCap,
        config: &mut MemeConfig,
        new_minimum: u64,
    ) {
        config.enforce_version();
        assert!(new_minimum >= config.minimum_version, EBadVersion);
        config.minimum_version = new_minimum;
        config.emit_update();
    }

    public fun update_virtual_sui_amount(
        _cap: &AdminCap,
        config: &mut MemeConfig,
        virtual_sui_amount: u64,
    ) {
        config.enforce_version();
        config.virtual_sui_amount = virtual_sui_amount;
        config.emit_update();
    }

    public fun update_listing_fee(
        _cap: &AdminCap,
        config: &mut MemeConfig,
        listing_fee: u64,
    ) {
        config.enforce_version();
        config.listing_fee = listing_fee;
        config.emit_update();
    }

    public fun update_swap_fee_bps(
        _cap: &AdminCap,
        config: &mut MemeConfig,
        swap_fee_bps: u64,
    ) {
        config.enforce_version();
        assert!(swap_fee_bps < 10_000, EBadFee);
        config.swap_fee_bps = swap_fee_bps;
        config.emit_update();
    }

    public fun update_migration_fee_bps(
        _cap: &AdminCap,
        config: &mut MemeConfig,
        migration_fee_bps: u64,
    ) {
        config.enforce_version();
        config.migration_fee_bps = migration_fee_bps;
        config.emit_update();
    }

    public fun update_treasury(
        _cap: &AdminCap,
        config: &mut MemeConfig,
        treasury: address,
    ) {
        config.enforce_version();
        config.treasury = treasury;
        config.emit_update();
    }

    fun emit_update(config: &MemeConfig) {
        events::emit_config_update(
            config.minimum_version,
            config.is_create_enabled,
            config.are_swaps_enabled,
            config.virtual_sui_amount,
            config.curve_supply_bps,
            config.listing_fee,
            config.swap_fee_bps,
            config.migration_fee_bps,
            config.treasury
        );
    }

}