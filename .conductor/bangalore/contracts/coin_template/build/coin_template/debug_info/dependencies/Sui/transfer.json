{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_b448b1d971bd6c1aac8ef4eee4305943806d5d5b/crates/sui-framework/packages/sui-framework/sources/transfer.move", "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 110, "end": 118}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "transfer"], "struct_map": {"0": {"definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 598, "end": 607}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 616, "end": 617}]], "fields": [{"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 639, "end": 641}, {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 651, "end": 658}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2349, "end": 2442}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2360, "end": 2368}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2369, "end": 2370}]], "parameters": [["obj#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2377, "end": 2380}], ["recipient#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2385, "end": 2394}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2425, "end": 2428}, "1": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2430, "end": 2439}, "2": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2411, "end": 2440}}, "is_native": false}, "1": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2871, "end": 2979}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2882, "end": 2897}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2898, "end": 2899}]], "parameters": [["obj#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2914, "end": 2917}], ["recipient#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2922, "end": 2931}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2962, "end": 2965}, "1": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2967, "end": 2976}, "2": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 2948, "end": 2977}}, "is_native": false}, "2": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 3985, "end": 4247}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 3996, "end": 4010}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4011, "end": 4012}]], "parameters": [["obj#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4019, "end": 4022}], ["party#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4027, "end": 4032}]], "returns": [], "locals": [["addresses#1#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4137, "end": 4146}], ["default#1#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4128, "end": 4135}], ["permissions#1#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4148, "end": 4159}]], "nops": {}, "code_map": {"0": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4067, "end": 4072}, "1": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4067, "end": 4090}, "2": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4059, "end": 4117}, "4": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4092, "end": 4116}, "5": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4059, "end": 4117}, "6": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4163, "end": 4168}, "7": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4163, "end": 4182}, "8": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4148, "end": 4159}, "9": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4137, "end": 4146}, "10": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4128, "end": 4135}, "11": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4208, "end": 4211}, "12": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4213, "end": 4220}, "13": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4222, "end": 4231}, "14": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4233, "end": 4244}, "15": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 4188, "end": 4245}}, "is_native": false}, "3": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5068, "end": 5345}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5079, "end": 5100}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5101, "end": 5102}]], "parameters": [["obj#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5117, "end": 5120}], ["party#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5125, "end": 5130}]], "returns": [], "locals": [["addresses#1#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5235, "end": 5244}], ["default#1#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5226, "end": 5233}], ["permissions#1#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5246, "end": 5257}]], "nops": {}, "code_map": {"0": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5165, "end": 5170}, "1": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5165, "end": 5188}, "2": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5157, "end": 5215}, "4": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5190, "end": 5214}, "5": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5157, "end": 5215}, "6": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5261, "end": 5266}, "7": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5261, "end": 5280}, "8": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5246, "end": 5257}, "9": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5235, "end": 5244}, "10": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5226, "end": 5233}, "11": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5306, "end": 5309}, "12": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5311, "end": 5318}, "13": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5320, "end": 5329}, "14": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5331, "end": 5342}, "15": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5286, "end": 5343}}, "is_native": false}, "4": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5714, "end": 5786}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5725, "end": 5738}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5739, "end": 5740}]], "parameters": [["obj#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5747, "end": 5750}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5780, "end": 5783}, "1": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5761, "end": 5784}}, "is_native": false}, "5": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5963, "end": 6050}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5974, "end": 5994}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 5995, "end": 5996}]], "parameters": [["obj#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 6011, "end": 6014}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 6044, "end": 6047}, "1": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 6025, "end": 6048}}, "is_native": false}, "6": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 6641, "end": 6711}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 6652, "end": 6664}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 6665, "end": 6666}]], "parameters": [["obj#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 6673, "end": 6676}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 6705, "end": 6708}, "1": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 6687, "end": 6709}}, "is_native": false}, "7": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7113, "end": 7198}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7124, "end": 7143}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7144, "end": 7145}]], "parameters": [["obj#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7160, "end": 7163}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7192, "end": 7195}, "1": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7174, "end": 7196}}, "is_native": false}, "8": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7687, "end": 7863}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7698, "end": 7705}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7706, "end": 7707}]], "parameters": [["parent#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7714, "end": 7720}], ["to_receive#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7732, "end": 7742}]], "returns": [{"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7759, "end": 7760}], "locals": [["id#1#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7783, "end": 7785}], ["version#1#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7787, "end": 7794}]], "nops": {}, "code_map": {"0": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7799, "end": 7809}, "1": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7771, "end": 7796}, "2": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7787, "end": 7794}, "3": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7783, "end": 7785}, "4": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7828, "end": 7834}, "6": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7828, "end": 7847}, "7": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7849, "end": 7851}, "8": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7853, "end": 7860}, "9": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 7815, "end": 7861}}, "is_native": false}, "9": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8181, "end": 8372}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8192, "end": 8206}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8207, "end": 8208}]], "parameters": [["parent#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8223, "end": 8229}], ["to_receive#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8241, "end": 8251}]], "returns": [{"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8268, "end": 8269}], "locals": [["id#1#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8292, "end": 8294}], ["version#1#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8296, "end": 8303}]], "nops": {}, "code_map": {"0": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8308, "end": 8318}, "1": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8280, "end": 8305}, "2": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8296, "end": 8303}, "3": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8292, "end": 8294}, "4": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8337, "end": 8343}, "6": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8337, "end": 8356}, "7": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8358, "end": 8360}, "8": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8362, "end": 8369}, "9": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8324, "end": 8370}}, "is_native": false}, "10": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8447, "end": 8536}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8458, "end": 8477}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8478, "end": 8479}]], "parameters": [["receiving#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8486, "end": 8495}]], "returns": [{"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8513, "end": 8515}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8522, "end": 8531}, "1": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8522, "end": 8534}}, "is_native": false}, "11": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8538, "end": 8600}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8565, "end": 8583}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8584, "end": 8585}]], "parameters": [["obj#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8592, "end": 8595}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "12": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8602, "end": 8663}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8629, "end": 8646}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8647, "end": 8648}]], "parameters": [["obj#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8655, "end": 8658}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "13": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8665, "end": 8827}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8692, "end": 8711}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8712, "end": 8713}]], "parameters": [["obj#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8725, "end": 8728}], ["default_permissions#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8737, "end": 8756}], ["addresses#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8767, "end": 8776}], ["permissions#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8799, "end": 8810}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "14": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8829, "end": 8906}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8856, "end": 8869}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8870, "end": 8871}]], "parameters": [["obj#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8878, "end": 8881}], ["recipient#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8886, "end": 8895}]], "returns": [], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "15": {"location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8908, "end": 8990}, "definition_location": {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8919, "end": 8931}, "type_parameters": [["T", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8932, "end": 8933}]], "parameters": [["parent#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8940, "end": 8946}], ["to_receive#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8957, "end": 8967}], ["version#0#0", {"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8973, "end": 8980}]], "returns": [{"file_hash": [198, 33, 120, 164, 94, 98, 245, 172, 16, 35, 234, 159, 79, 56, 94, 131, 44, 129, 120, 105, 108, 192, 9, 43, 2, 147, 37, 218, 173, 91, 15, 165], "start": 8988, "end": 8989}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {"EBCSSerializationFailure": 1, "EInvalidPartyPermissions": 7, "ENotSupported": 5, "EReceivingObjectTypeMismatch": 2, "ESharedNonNewObject": 0, "ESharedObjectOperationNotSupported": 4, "EUnableToReceiveObject": 3}}