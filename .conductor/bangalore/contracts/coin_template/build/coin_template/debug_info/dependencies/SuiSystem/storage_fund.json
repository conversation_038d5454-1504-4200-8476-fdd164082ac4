{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_b448b1d971bd6c1aac8ef4eee4305943806d5d5b/crates/sui-framework/packages/sui-system/sources/storage_fund.move", "definition_location": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 94, "end": 106}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000003", "storage_fund"], "struct_map": {"0": {"definition_location": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 855, "end": 866}, "type_parameters": [], "fields": [{"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 883, "end": 911}, {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 931, "end": 953}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1016, "end": 1274}, "definition_location": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1036, "end": 1039}, "type_parameters": [], "parameters": [["initial_fund#0#0", {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1040, "end": 1052}]], "returns": [{"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1069, "end": 1080}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1204, "end": 1219}, "1": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1253, "end": 1265}, "2": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1087, "end": 1272}}, "is_native": false}, "1": {"location": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1378, "end": 2985}, "definition_location": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1398, "end": 1411}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1417, "end": 1421}], ["storage_charges#0#0", {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1445, "end": 1460}], ["storage_fund_reinvestment#0#0", {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1480, "end": 1505}], ["leftover_staking_rewards#0#0", {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1525, "end": 1549}], ["storage_rebate_amount#0#0", {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1569, "end": 1590}], ["non_refundable_storage_fee_amount#0#0", {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1601, "end": 1634}]], "returns": [{"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1644, "end": 1656}], "locals": [["non_refundable_storage_fee#1#0", {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2323, "end": 2349}]], "nops": {}, "code_map": {"0": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1778, "end": 1782}, "1": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1778, "end": 1805}, "2": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1811, "end": 1836}, "3": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1778, "end": 1837}, "5": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1843, "end": 1847}, "6": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1843, "end": 1870}, "7": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1876, "end": 1900}, "8": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 1843, "end": 1901}, "10": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2147, "end": 2151}, "11": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2147, "end": 2180}, "12": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2186, "end": 2201}, "13": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2147, "end": 2202}, "15": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2352, "end": 2356}, "16": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2352, "end": 2394}, "17": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2410, "end": 2443}, "18": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2352, "end": 2444}, "19": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2323, "end": 2349}, "20": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2451, "end": 2455}, "21": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2451, "end": 2478}, "22": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2484, "end": 2510}, "23": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2451, "end": 2511}, "25": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2726, "end": 2730}, "26": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2726, "end": 2759}, "27": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2766, "end": 2787}, "28": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2726, "end": 2788}, "29": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2969, "end": 2983}}, "is_native": false}, "2": {"location": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2987, "end": 3101}, "definition_location": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 2998, "end": 3026}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3027, "end": 3031}]], "returns": [{"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3048, "end": 3051}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3058, "end": 3062}, "1": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3058, "end": 3091}, "2": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3058, "end": 3099}}, "is_native": false}, "3": {"location": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3103, "end": 3240}, "definition_location": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3114, "end": 3127}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3128, "end": 3132}]], "returns": [{"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3149, "end": 3152}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3159, "end": 3163}, "1": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3159, "end": 3192}, "2": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3159, "end": 3200}, "3": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3203, "end": 3207}, "4": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3203, "end": 3230}, "5": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3203, "end": 3238}, "6": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3201, "end": 3202}, "7": {"file_hash": [70, 5, 168, 230, 216, 249, 214, 35, 69, 114, 247, 155, 191, 177, 48, 142, 25, 1, 10, 132, 197, 105, 192, 178, 35, 82, 79, 208, 169, 126, 187, 167], "start": 3159, "end": 3238}}, "is_native": false}}, "constant_map": {}}