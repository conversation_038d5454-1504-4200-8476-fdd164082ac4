{"version": 2, "from_file_path": "/Users/<USER>/Programming/jobs/hopaggregator/hopfun/.conductor/bangalore/contracts/hopdex/sources/config.move", "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 15, "end": 21}, "module_name": ["55b04c619307e842a4644314bf30bd1fc8920ea1df36c305728eaf36cd658327", "config"], "struct_map": {"0": {"definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 177, "end": 186}, "type_parameters": [], "fields": [{"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 205, "end": 207}, {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 223, "end": 241}, {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 269, "end": 286}, {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 309, "end": 325}, {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 353, "end": 366}, {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 382, "end": 393}]}, "1": {"definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 425, "end": 433}, "type_parameters": [], "fields": [{"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 459, "end": 461}]}, "2": {"definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 492, "end": 498}, "type_parameters": [], "fields": [{"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 492, "end": 498}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 516, "end": 1089}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 520, "end": 524}, "type_parameters": [], "parameters": [["_witness#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 534, "end": 542}], ["ctx#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 560, "end": 563}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 653, "end": 656}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 641, "end": 657}, "2": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 691, "end": 696}, "3": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 744, "end": 747}, "4": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 799, "end": 802}, "6": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 799, "end": 811}, "7": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 840, "end": 844}, "8": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 871, "end": 887}, "9": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 613, "end": 898}, "10": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 908, "end": 942}, "11": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1008, "end": 1011}, "12": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 996, "end": 1012}, "13": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 969, "end": 1022}, "14": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1069, "end": 1072}, "16": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1069, "end": 1081}, "17": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1032, "end": 1082}, "18": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1082, "end": 1083}}, "is_native": false}, "1": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1125, "end": 1269}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1136, "end": 1155}, "type_parameters": [], "parameters": [["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1165, "end": 1171}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1208, "end": 1224}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1228, "end": 1234}, "2": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1228, "end": 1246}, "4": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1225, "end": 1227}, "5": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1200, "end": 1262}, "7": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1248, "end": 1261}, "8": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1200, "end": 1262}, "9": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1262, "end": 1263}}, "is_native": false}, "2": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1275, "end": 1392}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1286, "end": 1307}, "type_parameters": [], "parameters": [["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1308, "end": 1314}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1346, "end": 1352}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1346, "end": 1366}, "3": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1338, "end": 1385}, "5": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1368, "end": 1384}, "6": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1338, "end": 1385}, "7": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1385, "end": 1386}}, "is_native": false}, "3": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1398, "end": 1494}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1409, "end": 1427}, "type_parameters": [], "parameters": [["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1428, "end": 1434}]], "returns": [{"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1449, "end": 1452}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1463, "end": 1469}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1463, "end": 1488}}, "is_native": false}, "4": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1500, "end": 1594}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1511, "end": 1528}, "type_parameters": [], "parameters": [["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1529, "end": 1535}]], "returns": [{"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1550, "end": 1553}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1564, "end": 1570}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1564, "end": 1588}}, "is_native": false}, "5": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1600, "end": 1696}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1611, "end": 1627}, "type_parameters": [], "parameters": [["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1628, "end": 1634}]], "returns": [{"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1649, "end": 1656}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1667, "end": 1673}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1667, "end": 1690}}, "is_native": false}, "6": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1702, "end": 1789}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1713, "end": 1726}, "type_parameters": [], "parameters": [["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1727, "end": 1733}]], "returns": [{"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1748, "end": 1752}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1763, "end": 1769}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1763, "end": 1783}}, "is_native": false}, "7": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1795, "end": 1877}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1806, "end": 1817}, "type_parameters": [], "parameters": [["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1818, "end": 1824}]], "returns": [{"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1839, "end": 1842}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1853, "end": 1859}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1853, "end": 1871}}, "is_native": false}, "8": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1912, "end": 2221}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1916, "end": 1933}, "type_parameters": [], "parameters": [["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1943, "end": 1949}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2024, "end": 2030}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2024, "end": 2051}, "2": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2066, "end": 2072}, "3": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2066, "end": 2092}, "4": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2107, "end": 2113}, "5": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2107, "end": 2132}, "6": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2147, "end": 2153}, "7": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2147, "end": 2169}, "8": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2184, "end": 2190}, "9": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2184, "end": 2204}, "10": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 1978, "end": 2214}, "11": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2214, "end": 2215}}, "is_native": false}, "9": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2227, "end": 2430}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2238, "end": 2260}, "type_parameters": [], "parameters": [["_#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2270, "end": 2271}], ["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2292, "end": 2298}], ["new_bps#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2325, "end": 2332}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2382, "end": 2389}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2354, "end": 2360}, "2": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2354, "end": 2379}, "3": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2354, "end": 2389}, "4": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2417, "end": 2423}, "6": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2399, "end": 2424}}, "is_native": false}, "10": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2436, "end": 2633}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2447, "end": 2464}, "type_parameters": [], "parameters": [["_#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2474, "end": 2475}], ["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2496, "end": 2502}], ["new_bps#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2529, "end": 2536}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2585, "end": 2592}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2558, "end": 2564}, "2": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2558, "end": 2582}, "3": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2558, "end": 2592}, "4": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2620, "end": 2626}, "6": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2602, "end": 2627}}, "is_native": false}, "11": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2639, "end": 2869}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2650, "end": 2679}, "type_parameters": [], "parameters": [["_#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2689, "end": 2690}], ["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2711, "end": 2717}], ["treasury_address#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2743, "end": 2759}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2811, "end": 2827}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2785, "end": 2791}, "2": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2785, "end": 2808}, "3": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2785, "end": 2827}, "4": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2855, "end": 2861}, "6": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2837, "end": 2862}, "7": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2862, "end": 2863}}, "is_native": false}, "12": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2875, "end": 3069}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2886, "end": 2903}, "type_parameters": [], "parameters": [["_#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2913, "end": 2914}], ["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2935, "end": 2941}], ["enabled#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2967, "end": 2974}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3020, "end": 3027}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2997, "end": 3003}, "2": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2997, "end": 3017}, "3": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 2997, "end": 3027}, "4": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3055, "end": 3061}, "6": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3037, "end": 3062}, "7": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3062, "end": 3063}}, "is_native": false}, "13": {"location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3075, "end": 3264}, "definition_location": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3086, "end": 3101}, "type_parameters": [], "parameters": [["_#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3111, "end": 3112}], ["config#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3133, "end": 3139}], ["version#0#0", {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3165, "end": 3172}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3215, "end": 3222}, "1": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3194, "end": 3200}, "2": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3194, "end": 3212}, "3": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3194, "end": 3222}, "4": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3250, "end": 3256}, "6": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3232, "end": 3257}, "7": {"file_hash": [54, 161, 117, 224, 80, 232, 53, 132, 189, 35, 130, 72, 40, 87, 149, 94, 192, 61, 224, 106, 59, 97, 88, 232, 162, 18, 164, 220, 85, 137, 236, 3], "start": 3257, "end": 3258}}, "is_native": false}}, "constant_map": {"CONTRACT_VERSION": 0, "E_MIN_VERSION": 0, "E_SWAPS_DISABLED": 1}}