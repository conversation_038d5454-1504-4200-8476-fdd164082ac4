{"version": 2, "from_file_path": "/Users/<USER>/Programming/jobs/hopaggregator/hopfun/.conductor/bangalore/contracts/hopfun/sources/util.move", "definition_location": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 15, "end": 19}, "module_name": ["7bb19a2553935a7757f33b73500aea2bce9ff06a14c308b120b171822ed41d72", "util"], "struct_map": {}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 89, "end": 307}, "definition_location": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 100, "end": 116}, "type_parameters": [["T", {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 117, "end": 118}]], "parameters": [["coin#0#0", {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 120, "end": 124}], ["sender#0#0", {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 135, "end": 141}]], "returns": [], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 165, "end": 169}, "1": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 165, "end": 177}, "2": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 181, "end": 182}, "3": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 178, "end": 180}, "4": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 162, "end": 301}, "5": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 198, "end": 202}, "6": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 198, "end": 220}, "7": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 162, "end": 301}, "8": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 277, "end": 281}, "9": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 283, "end": 289}, "10": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 251, "end": 290}, "11": {"file_hash": [61, 222, 67, 237, 82, 157, 51, 48, 48, 98, 87, 132, 150, 180, 60, 208, 212, 127, 103, 184, 190, 15, 240, 23, 229, 3, 120, 227, 12, 194, 211, 198], "start": 162, "end": 301}}, "is_native": false}}, "constant_map": {}}