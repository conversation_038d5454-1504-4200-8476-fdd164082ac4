{"version": 2, "from_file_path": "/Users/<USER>/.move/https___github_com_MystenLabs_sui_git_b448b1d971bd6c1aac8ef4eee4305943806d5d5b/crates/sui-framework/packages/sui-framework/sources/tx_context.move", "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 87, "end": 97}, "module_name": ["0000000000000000000000000000000000000000000000000000000000000002", "tx_context"], "struct_map": {"0": {"definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 603, "end": 612}, "type_parameters": [], "fields": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 696, "end": 702}, {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 757, "end": 764}, {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 815, "end": 820}, {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 875, "end": 893}, {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1044, "end": 1055}]}}, "enum_map": {}, "function_map": {"0": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1140, "end": 1209}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1151, "end": 1157}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1158, "end": 1163}]], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1178, "end": 1185}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1192, "end": 1207}}, "is_native": false}, "1": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1210, "end": 1246}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1221, "end": 1234}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1238, "end": 1245}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "2": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1361, "end": 1431}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1372, "end": 1378}, "type_parameters": [], "parameters": [["self#0#0", {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1379, "end": 1383}]], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1398, "end": 1409}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1417, "end": 1421}, "1": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1416, "end": 1429}}, "is_native": false}, "3": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1462, "end": 1525}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1473, "end": 1478}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1479, "end": 1484}]], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1499, "end": 1502}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1509, "end": 1523}}, "is_native": false}, "4": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1526, "end": 1557}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1537, "end": 1549}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1553, "end": 1556}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "5": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1628, "end": 1717}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1639, "end": 1657}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1658, "end": 1663}]], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1678, "end": 1681}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1688, "end": 1715}}, "is_native": false}, "6": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1718, "end": 1762}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1729, "end": 1754}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1758, "end": 1761}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "7": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1848, "end": 1927}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1859, "end": 1866}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1867, "end": 1872}]], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1887, "end": 1902}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 1909, "end": 1925}}, "is_native": false}, "8": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2130, "end": 2211}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2141, "end": 2161}, "type_parameters": [], "parameters": [["_ctx#0#0", {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2162, "end": 2166}]], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2185, "end": 2192}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2199, "end": 2209}}, "is_native": false}, "9": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2212, "end": 2243}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2223, "end": 2231}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2235, "end": 2242}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "10": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2346, "end": 2421}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2357, "end": 2376}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2377, "end": 2382}]], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2397, "end": 2400}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2407, "end": 2419}}, "is_native": false}, "11": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2422, "end": 2451}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2433, "end": 2443}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2447, "end": 2450}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "12": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2585, "end": 2656}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2596, "end": 2605}, "type_parameters": [], "parameters": [["_self#0#0", {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2606, "end": 2611}]], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2626, "end": 2629}], "locals": [], "nops": {}, "code_map": {"0": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2636, "end": 2654}}, "is_native": false}, "13": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2657, "end": 2692}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2668, "end": 2684}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2688, "end": 2691}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "14": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2908, "end": 2945}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2919, "end": 2937}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 2941, "end": 2944}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "15": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 3180, "end": 3216}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 3191, "end": 3208}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 3212, "end": 3215}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "16": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6337, "end": 6490}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6341, "end": 6355}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6359, "end": 6374}], "locals": [["%#1", {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6417, "end": 6488}], ["sponsor#1#0", {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6385, "end": 6392}]], "nops": {}, "code_map": {"0": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6395, "end": 6411}, "1": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6385, "end": 6392}, "2": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6421, "end": 6428}, "3": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6421, "end": 6437}, "4": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6441, "end": 6442}, "5": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6438, "end": 6440}, "6": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6417, "end": 6488}, "7": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6444, "end": 6458}, "8": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6417, "end": 6488}, "10": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6477, "end": 6487}, "11": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6485, "end": 6486}, "12": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6477, "end": 6487}, "14": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6464, "end": 6488}, "15": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6417, "end": 6488}}, "is_native": false}, "17": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6491, "end": 6536}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6502, "end": 6516}, "type_parameters": [], "parameters": [], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6520, "end": 6535}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}, "18": {"location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6871, "end": 6940}, "definition_location": {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6882, "end": 6891}, "type_parameters": [], "parameters": [["tx_hash#0#0", {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6892, "end": 6899}], ["ids_created#0#0", {"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6913, "end": 6924}]], "returns": [{"file_hash": [14, 208, 79, 14, 107, 146, 143, 24, 248, 156, 108, 31, 235, 24, 23, 80, 27, 170, 174, 44, 139, 174, 213, 48, 21, 16, 213, 13, 165, 179, 254, 221], "start": 6932, "end": 6939}], "locals": [], "nops": {}, "code_map": {}, "is_native": true}}, "constant_map": {}}