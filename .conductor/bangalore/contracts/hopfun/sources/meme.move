module hopfun::meme {

    use hopfun::connector::{Connector};
    use hopfun::math;
    use hopfun::events;
    use hopfun::config::{MemeConfig};
    use hopfun::util;

    use hopdex::config::{DexConfig};
    use hopdex::pool;

    use sui::sui::{SUI};
    use sui::balance::{Self, Balance};
    use sui::coin::{Self, Coin, CoinMetadata};
    use sui::dynamic_field::{Self as df};
    use sui::transfer::{Receiving};

    /*
     * Constants
     */

    // errors
    const EIncorrectStatus: u64 = 0;
    const ESlippageCheck: u64 = 1;
    const EFeeNotPaid: u64 = 2;
    const EUniqueIdRequired: u64 = 3;
    const EInvalidTokenAmount: u64 = 4;
    const EBadConnectorCreator: u64 = 5;

    // use this, so we can preserve decimals
    const PRICE_SCALAR: u128 = 1_000_000_000u128;

    /*
     * Structs
     */

    public struct BondingCurve<phantom A> has key {
        id: object::UID,
        
        sui_balance: Balance<SUI>,
        virtual_sui_amount: u64,

        token_balance: Balance<A>,
        available_token_reserves: u64,

        creator: address,
        open: bool,
    }

    // Accessors
    public fun get_id<A>(curve: &BondingCurve<A>): ID {
        object::uid_to_inner(&curve.id)
    }

    public fun check_open_status<A>(curve: &BondingCurve<A>) {
        assert!(curve.open, EIncorrectStatus);
    }

    fun get_price_per_token_scaled<A>(curve: &BondingCurve<A>): u64 {
        if(curve.token_balance.value() == 0) {
            return 0
        };

        let sui_amount: u128 = (curve.virtual_sui_amount + curve.sui_balance.value()) as u128;
        let price_per_token_scaled: u128 = (sui_amount * PRICE_SCALAR) / (curve.token_balance.value() as u128);

        price_per_token_scaled as u64
    }

    fun move_curve<A>(curve: &mut BondingCurve<A>, dex_config: &DexConfig, config: &MemeConfig, ctx: &mut TxContext) {
        assert!(!curve.open, EIncorrectStatus);
        assert!(curve.available_token_reserves == 0, EIncorrectStatus);

        let sui_value = curve.sui_balance.value();
        let mut sui_coin = coin::take(&mut curve.sui_balance, sui_value, ctx);

        // collect migration_fee %
        let migration_fee_amount = sui_value * config.migration_fee_bps() / 10_000;
        let migration_fee = sui_coin.split(migration_fee_amount, ctx);
        transfer::public_transfer(migration_fee, config.treasury_address());

        let token_value = curve.token_balance.value();
        let token_coin = coin::take(&mut curve.token_balance, token_value, ctx);

        let pool_id = pool::create<A>(
            dex_config,
            token_coin,
            sui_coin,
            curve.creator,
            ctx
        );
        events::emit_migrate<A>(
            curve.get_id(),
            pool_id
        );
    }

    /*
     * Entry functions
     */

    fun create_bonding_curve<A>(
        metadata: &CoinMetadata<A>,
        connector: Connector<A>,
        config: &MemeConfig,
        ctx: &mut TxContext,
    ): BondingCurve<A> {
        config.enforce_version();
        config.enforce_create_enabled();

        let (id, supply, twitter, website, telegram, creator) = connector.deconstruct<A>();
        // delete connector
        object::delete(id);

        let coin_name = metadata.get_name();
        let ticker = metadata.get_symbol();
        let description = metadata.get_description();
        let image_url = metadata.get_icon_url();
        
        let total_supply = balance::value(&supply);
        let available_token_reserves = config.curve_supply_bps() * total_supply / 10_000;

        // create bonding_curve
        let bonding_curve = BondingCurve<A> {
            id: object::new(ctx),
            sui_balance: balance::zero<SUI>(),
            virtual_sui_amount: config.virtual_sui_amount(),

            token_balance: supply,
            available_token_reserves,

            creator,
            open: true
        };

        // emit event
        events::emit_curve_create<A>(
            bonding_curve.get_id(), 
            creator,
            coin_name,
            ticker,
            description,
            image_url,
            twitter,
            website,
            telegram,
            total_supply
        );

        bonding_curve
    }

    fun buy_returns_internal<A>(
        dex_config: &DexConfig,

        curve: &mut BondingCurve<A>,
        config: &MemeConfig,

        mut coin_in: Coin<SUI>, // coin in to split,
        mut token_amount: u64, // amount to purchase
        min_token_amount: u64, // minimum amount out
        sender: address,
        is_dev_buy: bool,

        ctx: &mut TxContext
    ): (Coin<SUI>, Coin<A>) {
        config.enforce_version();
        curve.check_open_status();
        
        assert!(min_token_amount <= token_amount, EInvalidTokenAmount);

        // pre market cap sui
        let pre_price = curve.get_price_per_token_scaled();

        // total reserve
        let reserve_a = balance::value<A>(&curve.token_balance);
        let reserve_sui = curve.virtual_sui_amount + balance::value<SUI>(&curve.sui_balance);

        token_amount = token_amount.min(curve.available_token_reserves);

        // calculate sui cost
        let mut sui_cost = math::get_amount_in(token_amount, reserve_sui, reserve_a);
        let mut fee_amount = math::get_fee_amount(sui_cost, config.swap_fee_bps());

        // does user have enough?
        if(coin_in.value() < (sui_cost + fee_amount)) {
            // adjust token amount to meet what user has
            // user obviously does not have enough to buy whole supply
            fee_amount = math::get_fee_amount(coin_in.value(), config.swap_fee_bps());
            sui_cost = coin_in.value() - fee_amount;

            token_amount = math::get_amount_out(sui_cost, reserve_sui, reserve_a);
            assert!(token_amount >= min_token_amount, ESlippageCheck);
        };

        // take fee
        let fee_coin = coin_in.split<SUI>(fee_amount, ctx);
        transfer::public_transfer(fee_coin, config.treasury_address());

        // take sui
        coin::put<SUI>(&mut curve.sui_balance, coin_in.split<SUI>(sui_cost, ctx));

        // get output_coin
        let output_coin = coin::take<A>(&mut curve.token_balance, token_amount, ctx);
        curve.available_token_reserves = curve.available_token_reserves - output_coin.value();

        // lock pool
        if(curve.available_token_reserves == 0) {
            curve.open = false;
            curve.move_curve(dex_config, config, ctx);
        };

        let post_price = curve.get_price_per_token_scaled();

        // emit event
        events::emit_buy<A>(
            curve.get_id(), 

            sui_cost + fee_amount, 
            token_amount,

            pre_price,
            post_price,

            sender,
            is_dev_buy,

            curve.virtual_sui_amount, 
            curve.sui_balance.value(), 
            curve.token_balance.value(), 
            curve.available_token_reserves
        );
        
        (coin_in, output_coin)
    }

    public fun buy_returns<A>(
        dex_config: &DexConfig,

        curve: &mut BondingCurve<A>,
        config: &MemeConfig,

        coin_in: Coin<SUI>, // coin in to split,
        token_amount: u64, // amount to purchase
        min_token_amount: u64, // minimum amount out
        sender: address,

        ctx: &mut TxContext
    ): (Coin<SUI>, Coin<A>) {
        buy_returns_internal(
            dex_config,
            curve, 
            config, 
            coin_in, 
            token_amount, 
            min_token_amount,
            sender, 
            false,
            ctx
        )
    }

    public fun sell_returns<A>(
        curve: &mut BondingCurve<A>,
        config: &MemeConfig,

        coin_in: Coin<A>, // coin in to split,
        min_amount_out: u64, // minimum amount out

        ctx: &mut TxContext
    ): Coin<SUI> {
        config.enforce_version();
        curve.check_open_status();

        let pre_price = curve.get_price_per_token_scaled();

         // total reserve
        let reserve_a = balance::value<A>(&curve.token_balance);
        let reserve_sui = curve.virtual_sui_amount + balance::value<SUI>(&curve.sui_balance);
        
        // amount in
        let amount_in = coin_in.value();

        // amount out
        let amount_out = math::get_amount_out(amount_in, reserve_a, reserve_sui);

        // take fee - only charge in SUI for sells
        let fee_amount = math::get_fee_amount(amount_out, config.swap_fee_bps());

        // take input coin
        curve.available_token_reserves = curve.available_token_reserves + amount_in;
        coin::put<A>(&mut curve.token_balance, coin_in);

        // give output coin
        let mut output_coin = coin::take<SUI>(&mut curve.sui_balance, amount_out, ctx);

        // take fee coin
        let fee_coin = coin::split(&mut output_coin, fee_amount, ctx);
        transfer::public_transfer(fee_coin, config.treasury_address());

        // slippage check
        assert!(output_coin.value() >= min_amount_out, ESlippageCheck);

        // transfer output coin to sender
        let post_price = curve.get_price_per_token_scaled();

        events::emit_sell<A>(
            curve.get_id(),
            output_coin.value(),
            amount_in, 

            pre_price,
            post_price,
            
            curve.virtual_sui_amount, 
            curve.sui_balance.value(), 
            curve.token_balance.value(), 
            curve.available_token_reserves
        );
    
        output_coin
    }

    public entry fun buy<A>(
        dex_config: &DexConfig,

        curve: &mut BondingCurve<A>,
        config: &MemeConfig,

        coin_in: Coin<SUI>, // coin in to split,
        token_amount: u64, // amount to purchase
        min_amount_out: u64, // minimum amount out
        sender: address,

        ctx: &mut TxContext
    ) {
        let (coin_in, output_coin) = buy_returns<A>(
            dex_config,
            curve,
            config,
            coin_in,
            token_amount,
            min_amount_out,
            sender,
            ctx
        );
        util::delete_or_return<SUI>(coin_in, sender);
        transfer::public_transfer(output_coin, sender);
    }

    public entry fun sell<A>(
        curve: &mut BondingCurve<A>,
        config: &MemeConfig,

        coin_in: Coin<A>, // coin in to split,
        min_amount_out: u64, // minimum amount out

        ctx: &mut TxContext
    ) {
        let output_coin = sell_returns<A>(
            curve,
            config,
            coin_in,
            min_amount_out,
            ctx
        );

        let sender = tx_context::sender(ctx);
        transfer::public_transfer(output_coin, sender);
    }

    /*
     * Receiving
     */

    #[allow(lint(coin_field))]
    public struct DevOrder has store {
        buy_coin: Coin<SUI>,
        creator: address
    }

    // pay curve fee and place developer buy
    public fun place_dev_order(config: &mut MemeConfig, temp_id: u64, mut buy_coin: Coin<SUI>, ctx: &mut TxContext) {
        config.enforce_version();
        config.enforce_create_enabled();

        assert!(!df::exists_<u64>(config.id(), temp_id), EUniqueIdRequired);

        // take listing fee
        if(config.listing_fee() > 0) {
            let fee_coin = buy_coin.split(config.listing_fee(), ctx);
            transfer::public_transfer(fee_coin, config.treasury_address());
        };

        // developer wallet
        let creator = tx_context::sender(ctx);

        // mark connector id as paid
        df::add(config.id(), temp_id, DevOrder {
            buy_coin,
            creator
        });
    }

    public fun accept_connector<T>(dex_config: &DexConfig, config: &mut MemeConfig, sent: Receiving<Connector<T>>, metadata: &CoinMetadata<T>, ctx: &mut TxContext) {
        config.enforce_version();
        let connector = transfer::public_receive(config.id(), sent);
        let temp_id = connector.get_temp_id();
        let connector_creator = connector.get_creator();

        // check if user paid for this connector_id
        assert!(df::exists_<u64>(config.id(), temp_id), EFeeNotPaid);

        // remove user paid flag and get dev order
        let dev_order = df::remove<u64, DevOrder>(config.id(), temp_id);
        let DevOrder { buy_coin, creator } = dev_order;

        assert!(connector_creator == creator, EBadConnectorCreator);

        // create bonding curve
        let mut curve = create_bonding_curve<T>(metadata, connector, config, ctx);
        if (buy_coin.value() > 0) {
            // calculate token amount
            let buy_amount = buy_coin.value() - math::get_fee_amount(buy_coin.value(), config.swap_fee_bps());
            let token_amount = math::get_amount_out(
                buy_amount, 
                curve.virtual_sui_amount + curve.sui_balance.value(),
                curve.token_balance.value()
            );

            let (coin_in, output_coin) = buy_returns_internal<T>(dex_config, &mut curve, config, buy_coin, token_amount, token_amount, creator, true, ctx);
            util::delete_or_return<SUI>(coin_in, creator);
            transfer::public_transfer(output_coin, creator);
        } else {
            buy_coin.destroy_zero();
        };

        // share bonding curve
        transfer::share_object(curve);
    }

}