module hopfun::events {

    use sui::event;
    use std::ascii;
    use std::string;
    use std::type_name::{Self, TypeName};
    use sui::url;

    /*
     * Event Structs
     */

    public struct ConnectorCreated has copy, drop {
        connector_id: ID,
        coin: TypeName,
    }

    public struct BondingCurveCreated<phantom T> has copy, drop {
        curve_id: ID,
        creator: address,

        coin_name: string::String,
        ticker: ascii::String,
        description: string::String,
        image_url: option::Option<url::Url>,

        twitter: string::String,
        website: string::String,
        telegram: string::String,

        total_supply: u64,
    }

    public struct BondingCurveBuy<phantom T> has copy, drop {
        curve_id: ID,
        sui_amount: u64,
        token_amount: u64,

        pre_price: u64,
        post_price: u64,

        sender: address,
        is_dev_buy: bool,

        virtual_sui_amount: u64,
        post_sui_balance: u64,
        post_token_balance: u64,
        available_token_reserves: u64
    }

    public struct BondingCurveSell<phantom T> has copy, drop {
        curve_id: ID,
        sui_amount: u64,
        token_amount: u64,

        pre_price: u64,
        post_price: u64,

        virtual_sui_amount: u64,
        post_sui_balance: u64,
        post_token_balance: u64,
        available_token_reserves: u64
    }

    public struct BondingCurveComplete<phantom T> has copy, drop {
        curve_id: ID
    }

    public struct BondingCurveMigrate<phantom T> has copy, drop {
        curve_id: ID,
        to_pool_id: ID
    }

    public struct MemeConfigUpdated has copy, drop {
        minimum_version: u64,
        is_create_enabled: bool,
        are_swaps_enabled: bool,
        virtual_sui_amount: u64,
        curve_supply_bps: u64,
        listing_fee: u64,
        swap_fee_bps: u64,
        migration_fee_bps: u64,
        treasury: address
    }

    /*
     * Methods
     */
 
    public(package) fun emit_connector_create<T>(
        connector_id: ID,
    ) {
        event::emit(ConnectorCreated {
            connector_id,
            coin: type_name::get<T>()
        });
    }

    public(package) fun emit_curve_create<T>(
        curve_id: ID, 
        creator: address, 
        
        coin_name: string::String, 
        ticker: ascii::String, 
        description: string::String, 
        image_url: Option<url::Url>,

        twitter: string::String,
        website: string::String,
        telegram: string::String,

        total_supply: u64,
    ) {
        event::emit(BondingCurveCreated<T> {
            curve_id,
            creator,
            coin_name,
            ticker,
            description,
            image_url,
            twitter,
            website,
            telegram,
            total_supply
        });
    }

    public(package) fun emit_buy<T>(
        curve_id: ID, 
        sui_amount: u64, 
        token_amount: u64,

        pre_price: u64,
        post_price: u64,
        sender: address,
        is_dev_buy: bool,

        virtual_sui_amount: u64, 
        post_sui_balance: u64, 
        post_token_balance: u64, 
        available_token_reserves: u64
    ) {
        event::emit(BondingCurveBuy<T> {
            curve_id,
            sui_amount,
            token_amount,

            pre_price,
            post_price,
            sender,
            is_dev_buy,

            virtual_sui_amount,
            post_sui_balance,
            post_token_balance,
            available_token_reserves
        });
    }

    public(package) fun emit_sell<T>(
        curve_id: ID, 
        sui_amount: u64, 
        token_amount: u64,

        pre_price: u64,
        post_price: u64,

        virtual_sui_amount: u64, 
        post_sui_balance: u64,
        post_token_balance: u64, 
        available_token_reserves: u64
    ) {
        event::emit(BondingCurveSell<T> {
            curve_id,
            sui_amount,
            token_amount,

            pre_price,
            post_price,

            virtual_sui_amount,
            post_sui_balance,
            post_token_balance,
            available_token_reserves
        });
    }

    public(package) fun emit_complete<T>(curve_id: ID) {
        event::emit(BondingCurveComplete<T> {
            curve_id
        });
    }

    public(package) fun emit_migrate<T>(curve_id: ID, to_pool_id: ID) {
        event::emit(BondingCurveMigrate<T> {
            curve_id,
            to_pool_id
        });
    }

    public(package) fun emit_config_update(
        minimum_version: u64,
        is_create_enabled: bool,
        are_swaps_enabled: bool,
        virtual_sui_amount: u64,
        curve_supply_bps: u64,
        listing_fee: u64,
        swap_fee_bps: u64,
        migration_fee_bps: u64,
        treasury: address
    ) {
        event::emit(MemeConfigUpdated {
            minimum_version,
            is_create_enabled,
            are_swaps_enabled,
            virtual_sui_amount,
            curve_supply_bps,
            listing_fee,
            swap_fee_bps,
            migration_fee_bps,
            treasury
        });
    }

}