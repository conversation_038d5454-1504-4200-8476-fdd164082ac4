[package]
name = "hopfun"
edition = "2024.beta" # edition = "legacy" to use legacy (pre-2024) Move
authors = ["<PERSON><PERSON> (@bonkman22)"]

[dependencies]
hopdex = { local = "../hopdex" }
config_registry = { local = "../config_registry" }

[addresses]
hopfun = "0xb8de4579fddee9b0bd27367a57c42244be9caeb2da08ae8b2be2da7968173a46"

# Named addresses will be accessible in Move as `@name`. They're also exported:
# for example, `std = "0x1"` is exported by the Standard Library.
# alice = "0xA11CE"

[dev-dependencies]
# The dev-dependencies section allows overriding dependencies for `--test` and
# `--dev` modes. You can introduce test-only dependencies here.
# Local = { local = "../path/to/dev-build" }

[dev-addresses]
# The dev-addresses section allows overwriting named addresses for the `--test`
# and `--dev` modes.
# alice = "0xB0B"