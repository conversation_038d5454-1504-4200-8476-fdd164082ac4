module hopdex::events {
    use std::type_name::TypeName;
    use sui::event;

    /*
     * Admin Events
     */

    public struct UpdateConfigEvent has copy, drop {
        protocol_share_bps: u64, // 500 - 50%
        pool_fee_rate_bps: u64, // bps

        treasury_address: address,
        
        swaps_enabled: bool,
        min_version: u64,
    }

    /*
     * Pool Events
     */

    public struct CreatePoolEvent has copy, drop {
        pool_id: object::ID,

        coin_a_type: TypeName,
        fee_owner: address,

        reserve_a: u64,
        reserve_b: u64,
    }

    public struct AddLiquidityEvent has copy, drop {
        pool_id: object::ID,

        coin_a_amount: u64,
        coin_b_amount: u64,

        new_reserve_a: u64,
        new_reserve_b: u64,

        liquidity_provider: address,
    }

    public struct RemoveLiquidityEvent has copy, drop {
        pool_id: object::ID,
        
        coin_a_amount: u64,
        coin_b_amount: u64,

        new_reserve_a: u64,
        new_reserve_b: u64,

        liquidity_provider: address,
    }

    public struct SwapEvent has copy, drop {
        pool_id: object::ID,

        a2b: bool,
        coin_in_amount: u64,
        coin_out_amount: u64,

        new_reserve_a: u64,
        new_reserve_b: u64,

        swapper: address,
    }

    /*
     * Admin Events
     */

    public(package) fun emit_update_config_event(
        protocol_share_bps: u64,
        pool_fee_rate_bps: u64,
        treasury_address: address,
        swaps_enabled: bool,
        min_version: u64,
    ) {
        let event = UpdateConfigEvent {
            protocol_share_bps,
            pool_fee_rate_bps,

            treasury_address,
            swaps_enabled,
            min_version,
        };
        event::emit(event);
    }

    /*
     * Pool Events
     */

    public(package) fun emit_create_pool_event(
        pool_id: object::ID,

        coin_a_type: TypeName,
        fee_owner: address,

        reserve_a: u64,
        reserve_b: u64,
    ) {
        let event = CreatePoolEvent {
            pool_id,
            coin_a_type,
            fee_owner,
            reserve_a,
            reserve_b
        };
        event::emit(event);
    }

    public(package) fun emit_add_liquidity_event(
        pool_id: object::ID,
        coin_a_amount: u64,
        coin_b_amount: u64,
        new_reserve_a: u64,
        new_reserve_b: u64,
        liquidity_provider: address,
    ) {
        let event = AddLiquidityEvent {
            pool_id,
            coin_a_amount,
            coin_b_amount,
            new_reserve_a,
            new_reserve_b,
            liquidity_provider,
        };
        event::emit(event);
    }

    public(package) fun emit_remove_liquidity_event(
        pool_id: object::ID,
        coin_a_amount: u64,
        coin_b_amount: u64,
        new_reserve_a: u64,
        new_reserve_b: u64,
        liquidity_provider: address,
    ) {
        let event = RemoveLiquidityEvent {
            pool_id,
            coin_a_amount,
            coin_b_amount,
            new_reserve_a,
            new_reserve_b,
            liquidity_provider,
        };
        event::emit(event);
    }

    public(package) fun emit_swap_event(
        pool_id: object::ID,
        a2b: bool,
        coin_in_amount: u64,
        coin_out_amount: u64,
        new_reserve_a: u64,
        new_reserve_b: u64,
        swapper: address,
    ) {
        let event = SwapEvent {
            pool_id,
            a2b,
            coin_in_amount,
            coin_out_amount,
            new_reserve_a,
            new_reserve_b,
            swapper,
        };
        event::emit(event);
    }

    

}