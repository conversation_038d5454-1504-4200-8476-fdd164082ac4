# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "35E29B67D8340F89EFCBBA1A9A5D64CBAE15A1BFA0D2B83A4DFB3CEBF7259D62"
deps_digest = "F8BBB0CCB2491CA29A3DF03D6F92277A4F3574266507ACD77214D37ECA3F3082"
dependencies = [
  { id = "Sui", name = "Sui" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "framework/devnet", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "framework/devnet", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[move.toolchain-version]
compiler-version = "1.53.2"
edition = "2024.beta"
flavor = "sui"

[env]

[env.devnet]
chain-id = "4256f1d2"
original-published-id = "0x55b04c619307e842a4644314bf30bd1fc8920ea1df36c305728eaf36cd658327"
latest-published-id = "0x55b04c619307e842a4644314bf30bd1fc8920ea1df36c305728eaf36cd658327"
published-version = "1"
