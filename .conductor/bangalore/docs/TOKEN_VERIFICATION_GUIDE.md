# Token Verification Guide

This guide explains how to check if a token has been successfully created on the Sui blockchain and verify its status.

## Overview

When a token is created through the HopFun platform, several objects are created on-chain:

1. **Package** - The coin module package
2. **CoinMetadata** - Token metadata (name, symbol, decimals, etc.)
3. **CurrencyHolder** - Temporary holder for treasury cap (first transaction)
4. **Connector** - Bonding curve connector (second transaction)
5. **BondingCurve** - The actual trading curve (third transaction)

## Quick Verification Methods

### Method 1: Check by Package ID

If you have the package ID from token creation:

```bash
# Check if package exists
sui client object <PACKAGE_ID> --json

# Example
sui client object 0xa289cfb68ab8afadb854ef0f07bdf9485577eb3402545d65961fa3cb8b2b1a9d --json
```

**Expected Response:**
```json
{
  "data": {
    "objectId": "0xa289cfb68ab8afadb854ef0f07bdf9485577eb3402545d65961fa3cb8b2b1a9d",
    "version": "1",
    "digest": "...",
    "type": "package",
    "owner": "Immutable",
    "content": {
      "disassembled": {
        "template": "// Move bytecode..."
      }
    }
  }
}
```

### Method 2: Check CoinMetadata

CoinMetadata contains the token's basic information:

```bash
# Find CoinMetadata by package ID pattern
sui client objects --json | jq '.[] | select(.type | contains("CoinMetadata")) | select(.type | contains("<PACKAGE_ID>"))'

# Direct check if you have the metadata ID
sui client object <COIN_METADATA_ID> --json
```

### Method 3: Check Connector Object

The Connector object indicates the token has a bonding curve:

```bash
# Find connector by package ID pattern
sui client objects --json | jq '.[] | select(.type | contains("Connector")) | select(.type | contains("<PACKAGE_ID>"))'
```

## Detailed Verification Steps

### Step 1: Verify Package Deployment

```bash
#!/bin/bash

PACKAGE_ID="0xa289cfb68ab8afadb854ef0f07bdf9485577eb3402545d65961fa3cb8b2b1a9d"

echo "🔍 Checking package deployment..."
PACKAGE_CHECK=$(sui client object $PACKAGE_ID --json 2>/dev/null)

if echo "$PACKAGE_CHECK" | jq -e '.data.type == "package"' > /dev/null; then
    echo "✅ Package deployed successfully"
    echo "   Package ID: $PACKAGE_ID"
    echo "   Version: $(echo "$PACKAGE_CHECK" | jq -r '.data.version')"
else
    echo "❌ Package not found or invalid"
    exit 1
fi
```

### Step 2: Verify Token Metadata

```bash
echo "🔍 Checking token metadata..."

# Find CoinMetadata object
METADATA_ID=$(sui client objects --json 2>/dev/null | \
    jq -r ".[] | select(.type | contains(\"CoinMetadata\")) | select(.type | contains(\"$PACKAGE_ID\")) | .objectId" | \
    head -1)

if [ -n "$METADATA_ID" ] && [ "$METADATA_ID" != "null" ]; then
    echo "✅ Token metadata found"
    echo "   Metadata ID: $METADATA_ID"
    
    # Get metadata details
    METADATA_DETAILS=$(sui client object $METADATA_ID --json 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo "   Name: $(echo "$METADATA_DETAILS" | jq -r '.data.content.fields.name')"
        echo "   Symbol: $(echo "$METADATA_DETAILS" | jq -r '.data.content.fields.symbol')"
        echo "   Decimals: $(echo "$METADATA_DETAILS" | jq -r '.data.content.fields.decimals')"
    fi
else
    echo "❌ Token metadata not found"
fi
```

### Step 3: Verify Connector (Bonding Curve)

```bash
echo "🔍 Checking bonding curve connector..."

# Find Connector object
CONNECTOR_ID=$(sui client objects --json 2>/dev/null | \
    jq -r ".[] | select(.type | contains(\"Connector\")) | select(.type | contains(\"$PACKAGE_ID\")) | .objectId" | \
    head -1)

if [ -n "$CONNECTOR_ID" ] && [ "$CONNECTOR_ID" != "null" ]; then
    echo "✅ Bonding curve connector found"
    echo "   Connector ID: $CONNECTOR_ID"
    
    # Check if connector is transferred to MemeConfig (indicates bonding curve is active)
    CONNECTOR_DETAILS=$(sui client object $CONNECTOR_ID --json 2>/dev/null)
    OWNER=$(echo "$CONNECTOR_DETAILS" | jq -r '.data.owner.AddressOwner // .data.owner')
    
    if [ "$OWNER" = "0xf0d970d4459922d14862d10cfb6464abd84a90e86e394574f7e474b7aa5e85ef" ]; then
        echo "   Status: Active (transferred to MemeConfig)"
    else
        echo "   Status: Pending (owner: $OWNER)"
    fi
else
    echo "❌ Bonding curve connector not found"
fi
```

## Complete Verification Script

Create a complete verification script:

```bash
#!/bin/bash
# save as: check-token-status.sh

PACKAGE_ID="$1"

if [ -z "$PACKAGE_ID" ]; then
    echo "Usage: $0 <PACKAGE_ID>"
    echo "Example: $0 0xa289cfb68ab8afadb854ef0f07bdf9485577eb3402545d65961fa3cb8b2b1a9d"
    exit 1
fi

echo "🔍 Token Verification Report"
echo "================================"
echo "Package ID: $PACKAGE_ID"
echo "Network: $(sui client active-env)"
echo "Time: $(date)"
echo ""

# Check 1: Package exists
echo "1️⃣ Package Deployment"
PACKAGE_CHECK=$(sui client object $PACKAGE_ID --json 2>/dev/null)
if echo "$PACKAGE_CHECK" | jq -e '.data.type == "package"' > /dev/null; then
    echo "   ✅ Package deployed"
    echo "   📦 Version: $(echo "$PACKAGE_CHECK" | jq -r '.data.version')"
else
    echo "   ❌ Package not found"
    exit 1
fi

# Check 2: CoinMetadata
echo ""
echo "2️⃣ Token Metadata"
METADATA_ID=$(sui client objects --json 2>/dev/null | \
    jq -r ".[] | select(.type | contains(\"CoinMetadata\")) | select(.type | contains(\"$PACKAGE_ID\")) | .objectId" | \
    head -1)

if [ -n "$METADATA_ID" ] && [ "$METADATA_ID" != "null" ]; then
    echo "   ✅ Metadata created"
    echo "   🆔 ID: $METADATA_ID"
    
    METADATA_DETAILS=$(sui client object $METADATA_ID --json 2>/dev/null)
    if [ $? -eq 0 ]; then
        echo "   📝 Name: $(echo "$METADATA_DETAILS" | jq -r '.data.content.fields.name')"
        echo "   🏷️  Symbol: $(echo "$METADATA_DETAILS" | jq -r '.data.content.fields.symbol')"
        echo "   🔢 Decimals: $(echo "$METADATA_DETAILS" | jq -r '.data.content.fields.decimals')"
    fi
else
    echo "   ❌ Metadata not found"
fi

# Check 3: Connector
echo ""
echo "3️⃣ Bonding Curve"
CONNECTOR_ID=$(sui client objects --json 2>/dev/null | \
    jq -r ".[] | select(.type | contains(\"Connector\")) | select(.type | contains(\"$PACKAGE_ID\")) | .objectId" | \
    head -1)

if [ -n "$CONNECTOR_ID" ] && [ "$CONNECTOR_ID" != "null" ]; then
    echo "   ✅ Connector created"
    echo "   🔗 ID: $CONNECTOR_ID"
    
    CONNECTOR_DETAILS=$(sui client object $CONNECTOR_ID --json 2>/dev/null)
    OWNER=$(echo "$CONNECTOR_DETAILS" | jq -r '.data.owner.AddressOwner // .data.owner')
    
    if [ "$OWNER" = "0xf0d970d4459922d14862d10cfb6464abd84a90e86e394574f7e474b7aa5e85ef" ]; then
        echo "   📈 Status: ACTIVE (bonding curve ready)"
    else
        echo "   ⏳ Status: PENDING (owner: $OWNER)"
    fi
else
    echo "   ❌ Connector not found"
fi

echo ""
echo "================================"
echo "✅ Verification complete"
```

## Using the Verification Script

1. **Make the script executable:**
   ```bash
   chmod +x check-token-status.sh
   ```

2. **Run the verification:**
   ```bash
   ./check-token-status.sh 0xa289cfb68ab8afadb854ef0f07bdf9485577eb3402545d65961fa3cb8b2b1a9d
   ```

## Token Creation States

A token goes through several states during creation:

| State | Description | Objects Present |
|-------|-------------|-----------------|
| **Not Started** | Token creation not initiated | None |
| **Package Deployed** | First transaction completed | Package, CoinMetadata, CurrencyHolder |
| **Connector Created** | Second transaction completed | + Connector |
| **Bonding Curve Active** | Third transaction completed | + BondingCurve (Connector transferred to MemeConfig) |

## Common Issues and Troubleshooting

### Issue 1: Package exists but no metadata
**Cause:** First transaction failed partially
**Solution:** Check transaction logs, retry token creation

### Issue 2: Metadata exists but no connector
**Cause:** Second transaction failed
**Solution:** Check Registry configuration, retry connector creation

### Issue 3: Connector exists but not transferred
**Cause:** Third transaction (bonding curve creation) failed
**Solution:** Check MemeConfig status, retry bonding curve creation

## API Integration

For programmatic verification, you can use the Sui TypeScript SDK:

```typescript
import { SuiClient } from '@mysten/sui/client';

async function verifyToken(packageId: string) {
  const client = new SuiClient({ url: 'https://fullnode.devnet.sui.io' });
  
  try {
    // Check package
    const packageObj = await client.getObject({ id: packageId });
    if (!packageObj.data) {
      return { status: 'not_found', message: 'Package not found' };
    }
    
    // Check metadata
    const objects = await client.getOwnedObjects({
      owner: 'Immutable',
      filter: {
        StructType: `0x2::coin::CoinMetadata<${packageId}::template::TEMPLATE>`
      }
    });
    
    return {
      status: 'success',
      package: packageObj.data,
      metadata: objects.data[0]
    };
  } catch (error) {
    return { status: 'error', error: error.message };
  }
}
```

## Next Steps

- [Token Trading Guide](./TOKEN_TRADING_GUIDE.md)
- [Bonding Curve Mechanics](./BONDING_CURVE_GUIDE.md)
- [API Reference](./API_REFERENCE.md)
